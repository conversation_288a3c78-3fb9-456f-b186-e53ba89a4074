Computational analysis of cancer genome sequencing data
<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & <PERSON> 
Nature Reviews Genetics volume 23, pages298–314 (2022)Cite this article

26k Accesses

93 Citations

51 Altmetric

Metricsdetails

Abstract
Distilling biologically meaningful information from cancer genome sequencing data requires comprehensive identification of somatic alterations using rigorous computational methods. As the amount and complexity of sequencing data have increased, so has the number of tools for analysing them. Here, we describe the main steps involved in the bioinformatic analysis of cancer genomes, review key algorithmic developments and highlight popular tools and emerging technologies. These tools include those that identify point mutations, copy number alterations, structural variations and mutational signatures in cancer genomes. We also discuss issues in experimental design, the strengths and limitations of sequencing modalities and methodological challenges for the future.

Similar content being viewed by others

Reconstructing and counting genomic fragments through tagmentation-based haploid phasing
Article Open access
23 September 2021

Analysis of 10,478 cancer genomes identifies candidate driver genes and opportunities for precision oncology
Article Open access
18 June 2024

Signatures of copy number alterations in human cancer
Article Open access
15 June 2022
Introduction
Analysis of cancer genomes using high-throughput sequencing technologies can provide a comprehensive view of the mutational landscape of human tumours, from single base mutations to chromosomal or whole genome-scale events1,2. Exome sequencing and whole-genome sequencing (WGS) have become commonplace for characterizing genomic alterations in research studies, whereas targeted sequencing of selected genes is routine for detecting therapeutically relevant hotspot mutations in clinical practice.

With an increasingly large number of tumour genomes sequenced, researchers have expanded the catalogue of ‘driver’ mutations across multiple cancer types (reviewed in ref.3) including primary1,2,4,5,6 and metastatic7,8,9 tumours, described a wide range of complex structural variations2,10, unravelled the mutational patterns shaping tumour evolution and heterogeneity11,12,13,14,15, examined the role of non-coding mutations in cancer (reviewed in ref.16) and elucidated the molecular mechanisms underlying tumour immune evasion and resistance to anticancer therapy17. In a recent compendium of papers, investigators in the Pan-Cancer Analysis of Whole Genomes (PCAWG) project2 within the International Cancer Genome Consortium reported the analysis of WGS data for 2,658 primary tumours spanning 38 tumour types. These papers highlighted the diversity of somatic rearrangements18,19,20, extracted a comprehensive set of mutational signatures21, reconstructed evolutionary histories22, delineated RNA-level alterations23, characterized mutations in mitochondrial DNA24, examined somatic retrotransposition events25, detected associations with viruses26 and identified chromothripsis events19 (Fig. 1).

Fig. 1: Workflow for cancer genome analysis.
figure 1
a | Detection of somatic mutations in human cancers involves the extraction of DNA from a tumour and, ideally, a matched normal sample. Sequencing is usually performed in the paired-end mode with read lengths of 100–150 bp. Quality control is performed on the sequencing reads by assessing several metrics before and after alignment to the reference genome. b | Computational analysis of whole-genome sequencing (WGS) data, provided in BAM format or in a compressed format called CRAM, enables the detection of not only sequence alterations (such as point mutations and indels) but also many types of structural variations. Additional analysis of somatic alterations allows the identification of driver events, the mutational processes operative in cancer, patterns of tumour evolution and biomarkers of response to therapy.

Full size image
Accurate characterization of somatic alterations from genome sequencing data is critical for deriving biological insights. Despite the advances in algorithm development27,28, applications of different methods often give discordant results, especially for variants present in a small fraction of cells or variant types other than single-nucleotide substitutions. Several fundamental challenges are also associated with current genome sequencing technologies. For example, short-read sequencing (reads of 100–150 bp) on the dominant Illumina platform poses inherent constraints in reconstructing the tumour genome, especially in repetitive regions and for complex structural alterations29,30. Given the cost of sequencing, current assays also impose a trade-off between the depth of sequencing and genome coverage — WGS offers whole-genome coverage but with limited depth (typically 30–60×), making variants present in a small fraction of cells hard to detect, whereas targeted (or ‘panel’) sequencing of a subset of genes offers high depth (typically 500–1,000×) but low sensitivity for copy number variants (CNVs) and misses nearly all structural variants (SVs). Exome sequencing offers a compromise (typically a depth of ~100–200×), although it is becoming less common as the cost of WGS has decreased.

In this Review, we provide an overview of the algorithms designed for detecting somatic alterations using genome sequencing data, highlighting innovations made in recent years. We discuss the limitations of current sequencing technologies and the extent to which these can be addressed by the algorithmic paradigms utilized today. Several topics specific to clinical applications, such as the interpretation of germline and somatic variants and techniques for detecting predetermined hotspot mutations, and emerging areas such as computational immuno-oncology, single-cell technologies, long-read sequencing and cell-free DNA analysis, are not discussed as they have been recently reviewed elsewhere31,32,33,34,35,36,37,38.

Preprocessing sequencing reads
The first step in sequencing data analysis consists of mapping (aligning) the sequenced reads, usually delivered from sequencing facilities in FASTQ or BAM format39, to the human reference genome. To reduce the storage footprint (which is ~100 GB for a 30× genome), reference-based compression (which reduces the footprint to 30–50% of the original size) using an open format called CRAM40 is becoming common. The quality of sequencing data has improved substantially with the successive generations of sequencing platforms, with more consistency between machine runs and lower per-base error rates. One technical problem on some sequencers was the occurrence of ‘index hopping’ with multiplexed libraries, in which a tiny fraction of DNA fragments were assigned to incorrect samples, but its effect can be mitigated by dual indexing41. The quality of sequencing runs is assessed by inspecting biases in the distribution of base quality scores and base composition along the sequencing reads, using tools such as FastQC42. To assess mapping quality, the fraction of reads that align in the expected orientation to the reference genome, the insert size and read length distributions, and the fraction of duplicate reads (for example, due to PCR amplification artefacts and low library complexity) are among the common metrics. Several tools, including Alfred43 and Qualimap 2 (ref.44), can assess sequencing and mapping quality for large sequencing data sets.

Mapping short reads to the human genome sequence is complex and requires heuristic approaches. The most popular aligner for cancer genome analysis is BWA-MEM45. This algorithm can efficiently align relatively long reads (from 70 bp to a few hundred base pairs) against the human genome, supporting paired-end reads and chimeric alignment while being robust to mismatches. As mapping reads from diverse individuals to a single linear reference genome does not adequately account for genetic variation in the population, the next generation of alignment strategies should involve graph-based representations of polymorphic regions across individuals and de novo reconstruction of regions that are altered in the cancer genome, ideally assisted by long-read technologies46. For RNA sequencing (RNA-seq) data, splicing events and diverse isoforms must be considered when mapping to the human genome or transcriptome, and STAR47 and HISAT48 are among the popular aligners. Furthermore, with improved accuracy and reduced cost, long-read sequencing is becoming more popular for characterizing SVs, and minimap2 (ref.49) is a common aligner for mapping long reads.

Alignment can be confounded by biological variability (for example, polymorphisms), sequencing errors, segmental duplications, repetitive sequences and an incomplete reference genome, all of which could result in ambiguous or mistaken mapping. For example, when mapping sequencing reads to a version of the human reference genome lacking the ‘decoy’ sequences (which include parts of the human genome that were not assembled into the reference and viral sequences such as the Epstein–Barr virus genome), the fragments corresponding to the decoy segments are often misaligned to the reference. When reads are misaligned, the mismatched bases may be incorrectly identified by a detection algorithm as a somatic mutation. Sophisticated post-alignment filtering steps are needed to reduce such false positives (see below).

Genome alignment requires choosing a reference genome version. The Genome Reference Consortium released the most recent build of the human reference genome, GRCh38, in 2013. GRCh38 is a more complete reference than previous versions, and includes the refinement of thousands of nucleotides, a greater number of alternative contigs for regions that are too complex to be represented by a single sequence (for example, HLA loci), modelled centromeric sequences and gap closures50. These enhancements improve alignment quality, including for some transcripts in clinically relevant genes50. Yet adoption of each new assembly is slow, due to possible incompatibility with previous work and the amount of effort required to reprocess existing data and update databases. The PCAWG, for instance, used the hs37d5 assembly that was developed by the 1000 Genomes Project51, whereas The Cancer Genome Atlas (TCGA) utilized GRCh37, although the official NIH (National Institutes of Health) repository (Genomic Data Commons) realigned TCGA data to GRCh38 recently. Differing versions of genomes can lead to differences in analysis output, especially in repetitive regions, but these are relatively minor52 compared with the impact of different analytical tools. With the availability of gapless, telomere-to-telomere assemblies53, it may be possible to reach a stable human reference genome in the near future.

Detecting SNVs and indels
Algorithms for variant detection
Single-nucleotide variants (SNVs) and small (<50 bp) insertions and deletions (indels) are the most common alterations found in tumours54,55. In this article, we use the term SNVs to refer to somatic SNVs, although the term ‘variant’ is used in the literature for somatic or germ line depending on the variant type and the context. In a typical tumour–normal paired design, candidate somatic mutations are identified as genomic positions for which an alternate allele supported by tumour reads is not present in the matched normal sample (in the absence of tumour-in-normal contamination). The variant allele fraction (VAF), which is the number of reads supporting a candidate mutation divided by the read depth at that position, is a key determinant in finding a somatic variant. For germline variants, the VAF is close to 0.5 for heterozygous variants, and ~1 for homozygous variants. For somatic variants, the VAF is often substantially lower than 0.5 and depends on the tumour purity, copy number at the candidate location and intra-tumour heterogeneity (Fig. 2). For instance, a subclonal mutation present in 20% of the diploid tumour cells in a 60× sample should have ~6 reads containing the variant, but this would be halved to ~3 reads if the tumour purity is 50%. Depending on the cancer type and mode of tissue extraction (for example, needle biopsy), tumour purity could be as low as 10–20%. Thus, high-depth sequencing or a sophisticated algorithm (preferably both) is needed for identifying mutations with a low VAF in tumour samples. For a comprehensive discussion on experimental design considerations and analytical strategies for SNV detection, see ref.56.

Fig. 2: Identifying variants and artefacts in sequencing reads.
figure 2
a | Whole-genome sequencing (WGS) provides nearly uniform depth of coverage across the genome. By comparing WGS reads from cancer cells and matched controls, clonal single-nucleotide variants (SNVs) can be reliably detected both in coding and non-coding regions, while filtering out germline variants including common single-nucleotide polymorphisms (SNPs). However, subclonal SNVs may be missed when the number of reads containing the mutation is too small or comparable with the number of reads containing artefacts. The discordant reads are mapped further apart than expected, indicating a deletion; in the ‘clipped’ or ‘split’ reads, a portion of the read (coloured bars) maps to another location. b | Exome sequencing (~100–200×) and gene panel sequencing of cancer-related genes (~500–1,000×) have higher depth of sequencing than WGS, resulting in increased sensitivity to detect subclonal mutations. However, the coverage within and across target regions is more variable than with WGS, making copy number estimation more difficult.

Full size image
Assessing whether alternate alleles supported by sequencing reads represent true mutations or artefacts is the core task in variant calling. The large majority of tools (reviewed in ref.57) make use of a matched normal sample, typically blood, as a control. Many of the initial variant calling methodologies were heuristic approaches utilizing hard thresholds, for example, requiring a minimum number of reads or an allele fraction over a specific value. More advanced algorithms such as MuTect2 (ref.58), CaVEMan59, Strelka2 (ref.60), VarDict61 and MuSE62 estimate the genotype of both the normal and the tumour sample jointly, with prior probabilities for the genotypes or allele frequencies adjusted for factors such as expected mutation rates or mutation types (that is, transitions versus transversions), tumour purity and tumour ploidy, and local features such as copy number. Although many research studies have a tumour–normal paired design, a normal tissue is often not available in clinical applications. In such cases, variant calls are made using the tumour sample only and efforts are made afterwards to remove germline variants. A standard approach to remove germline variants is to filter annotated single-nucleotide polymorphisms (SNPs) found in dbSNP63 (the NCBI (National Center for Biotechnology Information) database of genetic variation) as well as additional population variants in a large set of exomes or genomes such as those in the Genome Aggregation Database (gnomAD)64. However, this database-based filtering approach is often inadequate because it does not capture SNVs incorrectly identified due to the particularities of the specific analytical workflow used (for example, misalignment due to the aligner used), or population-specific germline variants not contained in the database. Thus, using a ‘panel of normals’ collected from other studies and processed in the same way as the tumour samples is an effective approach for removing germline variants. Identical sequencing protocols and data processing of the samples is essential for removing false positives — on one occasion, we traced some false positive variants to subtle alignment differences that arose between 150 bp reads from tumour samples and 100 bp reads from normal samples (P.J.P., unpublished observations). Without matched normals, some individual-specific germline variants will still be misclassified as somatic variants65, but the number of such variants should be small for a sufficiently large panel of normals.

For stringent filtering, sites with any reads supporting alternate alleles in a normal sample may be filtered as potential germline variants or artefacts. On rare occasions, true SNVs may overlap with known polymorphic sites. To avoid such variants from getting filtered, one could keep those with sufficient coverage in the tumour but no support in the normal sample2. It is also possible to distinguish between somatic and germline variants by employing a machine learning model that integrates features gathered from large collections of known somatic (for example, COSMIC)66 and germline (for example, dbSNP)63 variants.

Comparison of algorithms and variant filtering
Numerous studies have found variable agreement when comparing the performance of existing mutation calling methods67,68,69,70,71. The ICGC-TCGA DREAM Somatic Mutation Calling challenge used simulated cancer genomes to benchmark pipelines70; other benchmarking efforts focused on sequencing data from clinical samples2,71. Analysis of high-depth (~300×) paired tumour–normal sequencing data showed that precision and recall strongly varied depending on the combination of aligner and caller used71. The library preparation protocol used also profoundly influenced the accuracy of the mutation calls; PCR-free libraries performed best, as they produced the most uniform coverage across the genome and were less affected by GC content bias than PCR-based libraries. Filtering out variants supported by soft-clipped or low mapping quality reads, or that showed positional or strand bias, improved the detection of SNVs. To accurately detect subclonal mutations, reaching a sequencing depth of at least 100× for the tumour sample was recommended67,71. The validation of 13 SNV calling pipelines on 50 tumour genomes, through high-depth targeted sequencing of thousands of candidate mutations, showed2 that mutation detection algorithms generally exhibit high specificity but variable sensitivity (ranging from 0.10 to >0.95), especially for variants with a low VAF.

Several studies showed that combining the output of multiple callers decreases the number of false positives71,72. Ensemble methods based on simple rules (for example, keep only mutations called by at least a minimum number of algorithms2,73 or majority vote70) or weighted schemes have been used to derive consensus call sets in large cancer genomics projects1,2 and in benchmarking studies70,72. For instance, the consensus call set in the PCAWG consisted of SNVs detected by at least two out of four algorithms: CaVEMan, MuSE, MuTect2 and SAMtools45. This ‘wisdom of crowds’ approach is reasonable for gathering a high-confidence set of variants, but may be too conservative, thus sacrificing sensitivity for increased specificity. To improve both sensitivity and specificity, more complex strategies using machine learning have been proposed74,75,76,77,78.

Despite the increased sophistication in the statistical models, an essential component of the variant identification process is a set of ad hoc filters designed to remove the likely false positive calls. These filters include strand bias (for example, supporting reads are all in the reverse strand) (Fig. 2), the mapping quality of supporting reads, the presence of nearby indels or multiple alternative alleles and whether the read alignment patterns in the genomic region are ‘noisy’. These factors cannot be captured effectively in a standard linear model. As a result, visual inspection of the aligned read patterns in a genome browser and/or experimental validation remains a time-consuming but often necessary step for obtaining a comprehensive set of mutations71. Given the effectiveness of visual review, a natural approach is to utilize machine learning techniques to incorporate the features that are informative to the reviewer but not easily captured by simple models. In DeepVariant, a read ‘pile-up’ image of the candidate site is fed into a convolutional neural network (CNN) to detect germline variants and indels79. Similar approaches have been developed subsequently for detecting somatic mutations80,81. The main difficulty with CNN or other machine learning methods is obtaining a large amount of high-quality training data. Whereas known SNPs can be used to train a model for germline variants, the training data for SNVs are harder to obtain. In some cases, read-based phasing (for example, as used in MosaicForecast82) or other schemes may be needed to procure training data. Nonetheless, these data-driven formulations permit learning of complex patterns of errors and biases and should increase sensitivity and specificity79.

Detecting indels is substantially more difficult than detecting SNVs, as alignment issues are more intricate83. The preferred strategy for detecting indels, therefore, is to reassemble and realign the reads based on the most likely local haplotype, as done in GATK HaplotypeCaller84 (for SNPs) or MuTect2 (for SNVs), SvABA85, Platypus86 and other methods. Although the integration of indel calls from multiple methods increases performance, the accuracy remains low. In the PCAWG Consortium, for instance, consensus indel calls had precision of 91% and sensitivity of 60%; in comparison, consensus SNV calls had precision and sensitivity of 95% and 97.5%, respectively.

Identification of cancer drivers
The availability of large collections of cancer genomes permits the identification of cancer driver mutations using data-driven methods, as recently reviewed3. A higher than expected frequency of a mutation across samples is generally interpreted as evidence for positive selection, and such alterations are considered driver events. Over the years, the calculation of the background mutation frequency has improved considerably. The earliest, most naïve model had assumed a constant background mutation rate along the genome; an improved model used a synonymous mutation count on each gene to estimate the background87. Currently, more sophisticated methods, such as MutSig2CV88 and dNdScv14, utilize statistical models that consider several variables that correlate with mutation frequency, including transcriptional activity, the timing of DNA replication88, epigenetic context (that is, open versus closed chromatin)89,90, DNA repair activity91 and the sites of DNA-binding proteins92,93,94. Other tools, such as MutPanning95, also utilize the identification of mutational processes, which have variable preferences for different trinucleotides in specific contexts, to inform driver-gene identification95,96,97. In addition to mutation recurrence, other signals of positive selection for identifying driver genes include unexpected clustering of mutations in the linear amino acid sequence98, and the enrichment of mutations in 3D positions that are functionally important for the protein99,100, in regulatory elements101,102 and in evolutionarily conserved regions103. Recently, the cytidine deaminase APOBEC3A was shown to generate up to 200 times more mutations at DNA hairpin substrates than at non-hairpin sites, causing ‘hotspot’ mutations that are not driver events104. Additional work is needed to identify other sources of recurrent but non-driver mutations in cancer. For all statistical models, proper examination of the p-value distribution using quantile–quantile plots and accurate estimation of statistical significance using false discovery rates are essential.

Prioritizing the observed somatic mutations based on their potential functional consequences is challenging. The choice of the transcript database used (for example, RefSeq, UCSC, GENCODE or ENSEMBL) can have a major impact on annotation because the same mutation can have different effects on different transcripts. Variant Effect Predictor (VEP)105, SnpEff106 and ANNOVAR107 are among the most popular annotators and report the effects of mutations on transcripts, although discrepancies exist among annotators especially for mutations that fall within splice sites and non-coding DNA108,109. These tools often check databases such as the Catalogue of Somatic Mutations In Cancer (COSMIC)66, ClinVar110 and OncoKB111 to determine whether the mutation was found in previous cancer studies or whether it has been shown to be pathogenic and potentially actionable. The functions of non-coding variants are much more difficult to infer than those of coding variants, but are informed by cell type-specific genome annotations such as open chromatin regions (for example, from DNase I hypersensitive sites or peaks identified using ATAC-seq (assay for transposase-accessible chromatin using sequencing)), regulatory regions (for example, from the acetylated histone H3 lysine 27 (H3K27ac) histone mark for enhancers) and matched transcriptome data112,113.

Sources of errors in SNV and indel identification
In addition to the misclassification of germline variants as somatic variants, there are other potential sources of artefacts in identifying SNVs and indels. First, DNA amplification errors can result in artefacts. Until the PCR-free library preparation technique became available, sequencing library preparation often involved PCR amplification to generate a sufficiently large amount of DNA for sequencing. When the amount of DNA extracted is very small (<0.1 μg, for instance), PCR is still used. PCR amplification leads to increased levels of stutter noise at repetitive regions and additional GC bias, and can create single-nucleotide artefacts114. In other cases, whole-genome amplification (WGA) by multiple displacement amplification or PCR-based techniques was used prior to sequencing. Early TCGA data sets suffer from such WGA-related artefacts that must be filtered out carefully115.

Second, oxidative damage, such as the oxidation of guanine to 8-oxoguanine during DNA shearing, also generates artefacts such as the low-frequency transversions C > A within CCG > CAG116. These lesions might be mistakenly identified as true mutations if not flagged by computational tools specifically designed for their detection, such as D-ToxoG116, or by the requirement for a candidate mutation to be supported by both strands.

Third, although many research studies, such as TCGA, were based on fresh-frozen tissues, many clinical samples are archived by embedding in paraffin and tissue fixation can generate artefacts. Indeed, in such formalin-fixed paraffin-embedded (FFPE) tissues, artefacts may arise from DNA fragmentation and base changes induced by formaldehyde, especially the deamination of cytosine into thymines at methylated CpG sites117. For FFPE samples, specialized experimental protocols and algorithms must be used for variant detection118,119,120,121.

Finally, whereas the inclusion of non-cancerous cells from the same individual in the tumour sample reduces sensitivity for somatic variant detection, cross-individual contamination — such as a tumour sample being mistakenly paired with a normal sample from a different individual — is more serious in terms of false positive calls. Specific tools to assess cross-individual contamination, including ContEst122, ART-DeCO123 and Conpair124, estimate the probability of contamination based on the allele fraction of homozygous polymorphisms. Algorithms to identify sample swaps based on the concordance of germline polymorphisms between samples include BAMixChecker125, NGSCheckMate126 and HYSIS127. In some cancers, a matched ‘normal’ sample may be contaminated by tumour cells that have invaded healthy tissue. Algorithms such as DeTiN128 can be used to avoid erroneous filtering of true SNVs in these cases.

Mutational signature analysis
Somatic mutations are caused by specific mutational processes, such as defective DNA repair, imperfect DNA replication and exposure to various mutagens. Many such processes induce specific nucleotide changes, for example, lung cancer in smokers is characterized by the abundance of G > T transversions, and melanoma is characterized by C > T transitions at dipyrimidine sites21. Cancer genomes share the footprints of these mutational processes at different relative abundances. From a sufficiently large set of cancer exomes or genomes, it is possible to perform de novo discovery of independent components that underlie the mutational spectrum, with each component referred to as a ‘mutational signature’ (Fig. 3a). This decomposition is similar to how orchestral sound can be separated into specific components corresponding to musical instruments if the sound is heard long enough with sufficient variation.

Fig. 3: Mutational signature analysis of cancer genomes.
figure 3
a | Mutations accumulate in the DNA starting from the first cell division. Therefore, mutations detected in a cancer sample using bulk sequencing data reflect the composite effect of multiple mutational processes. The composite mutational spectrum can be described by the counts of single base substitution (SBS) types and their genomic contexts. b | An SBS spectrum is commonly represented by a 96-dimensional vector, with the six canonical substitutions (C > A/G/T, T > A/C/G; their reverse complements are indistinguishable) and their flanking 5′ and 3′ bases. Additional spectra based on indels, genomic rearrangements or copy number alterations (CNAs) are also becoming more common. c | The mutational signatures associated with distinct mutational processes can be discovered de novo through joint analysis of a large number of cancer genomes. For example, application of non-negative matrix factorization (NMF) on 2,780 whole-genome sequencing (WGS) samples from the Pan-Cancer Analysis of Whole Genomes (PCAWG) project established the latest Catalogue of Somatic Mutations In Cancer (COSMIC) Mutational Signatures database21. Identification of mutational processes from a few genomes is underpowered. Thus, mutational signature analysis on a small set of samples is generally performed by estimating the relative contribution for a set of signatures previously defined (‘refitting’) using methods such as non-negative least squares (NNLS). Hybrid models perform both de novo discovery and refitting using existing signatures as priors. Dashed lines indicate that the refitting procedure and the catalogue are utilized in the matching step. LOH, loss of heterozygosity.

Full size image
In their pioneering work129, the cancer group at the Sanger Institute represented each single base substitution (SBS) spectrum by a 96-dimensional vector: the 6 canonical substitutions (C > A/G/T, T > A/C/G; their reverse complements are indistinguishable), combined with flanking 5′ and 3′ bases (6 base changes × four 5′ bases × four 3′ bases = 96) (Fig. 3b). Application of a matrix decomposition technique called non-negative matrix factorization (NMF) to the mutation type by sample matrix of thousands of samples enabled the decoupling of the distinct patterns of mutations that are attributable to different underlying mutational processes, along with their relative contribution in each sample (Fig. 3c). In this way, the COSMIC Mutational Signatures catalogue was constructed130. This catalogue initially consisted of 30 signatures and has since been extended to more than 50 distinct SBS processes21. A similar concept was applied to other types of mutations, including indels21, rearrangements131 and copy number alterations (CNAs)132,133. Various tools for such de novo signature discovery are available now, utilizing NMF129,130 or its Bayesian formulation134 as well as expectation maximization135, topic models136 and others. Although NMF is a popular dimensionality reduction technique, it suffers from non-uniqueness; thus, a modified version of NMF with additional constraints should be considered in some cases.

Although the mechanistic origins of many signatures remain to be elucidated, mutational signature analysis has proved invaluable in providing insights into the mechanisms behind observed mutations. Indeed, the mutational signatures of a large variety of exogenous agents, DNA repair deficiencies and therapies have been described21,137,138. Characterization of mutational signatures in different tumour types allows a better understanding of the underlying mechanisms of cancer in different tissues. Some SBS signatures, such as the ‘clock-like’ signatures, have been observed in both normal and malignant tissues139,140,141,142. Others have a high prevalence in specific tumour types, for example, signatures corresponding to the activation of APOBEC cytidine deaminases are frequent in breast, cervical and bladder cancers, and signatures of exogeneous agents are specific to tissues that are directly exposed to them21.

Prior knowledge about the mutational signatures in cancer genomes, as summarized in a signature catalogue, allows signatures in a new tumour sample to be estimated without necessitating de novo signature extraction with a large cohort. For this ‘refitting’ step, restricting the set of signatures under consideration to those known to be active in specific tumour types is helpful, because several signatures in the full catalogue have similar distributions and can result in incorrect assignments. Further measures should also be taken to increase the sparsity of signatures of interest so that signatures can be assigned parsimoniously143,144. Several tools are available to perform refitting and use algorithms such as non-negative least squares (NNLS)145,146,147. An obvious limitation of refitting is that the analysis is restricted to known signatures. To overcome this challenge, other methods perform de novo discovery and refitting using known signatures as priors148,149,150.

One limitation of the above approaches is that a large number of mutations (at least hundreds) must be observed per sample to enable signature decomposition to work. Therefore, exome or WGS is required, unless the tumour has an exceptionally large number of mutations (for example, unless it is positive for microsatellite instability or has a mutation in DNA polymerase epsilon). A recent method by our group, SigMA151, enables signature analysis to be performed from a much smaller number of mutations, thus extending mutational signature analysis to targeted cancer panels for clinical applications. This method was used to identify patients displaying the signature of a deficiency in homologous recombination, which may serve as an indication for favourable response to poly(ADP-ribose) polymerase (PARP) inhibitors152.

Analysing somatic structural variations
With the availability of WGS, a great deal of progress has been made in the characterization of somatic SVs in human cancers. The SVs inferred from WGS data range from simple deletions, insertions, duplications, inversions and translocations to copy number changes, transposable element insertions, viral integrations, telomere length variation and complex rearrangements such as chromothripsis. Small deletions and insertions (<50–100 bp) can be identified using individual reads, for example, by gapped alignment, but other SVs require different strategies. Although not discussed in this Review, a large body of work exists on how each type of variant may contribute to cancer development. For example, integrative analysis of translocations and epigenetic data has revealed the phenomenon of enhancer hijacking153,154,155 and enhancer amplification156,157.

As the terms CNVs and SVs are used to refer to either germline or somatic variants in the literature, here we use CNAs (also known as copy number aberrations) to refer to somatic CNVs and ‘somatic SVs’ (when contrasting with ‘germline SVs’) or simply ‘SVs’ to refer to somatic structural alterations. Approaches for investigating germline SVs have been recently reviewed elsewhere29.

Detecting copy number alterations
Conventional cytogenetic techniques such as fluorescent in situ hybridization (FISH) and spectral karyotyping are useful for the routine diagnosis of genetic disorders and large chromosomal alterations, but their spatial resolution is in the order of megabases. Use of array comparative genomic hybridization (aCGH)158 and SNP arrays increased spatial resolution to the order of 100 kb159. Some of the array-based platforms also offer information on copy-neutral loss of heterozygosity (LOH) events, tumour purity and ploidy160,161. WGS can identify CNAs and their underlying SVs, with breakpoints at single-nucleotide resolution.

One class of methods for identifying CNAs from sequencing data adapts the techniques developed for aCGH and SNP arrays, utilizing the ‘read depth’ along the genome as the main component. These methods segment the genome into regions with distinct copy numbers using hidden Markov models, circular binary segmentation162, piecewise constant fitting regression163 or other statistical techniques164. Even for a diploid genome, the read depth varies along the genome due to mappability, GC bias and other factors; thus, either a matched control (so that the same biases could be subtracted) or normalization using a statistical model is necessary164. More advanced methods for detecting CNAs incorporate the frequencies of minor alleles, termed the B-allele frequency (BAF), which are inferred from heterozygous SNPs, for segmentation, and also detect allele-specific CNAs and copy-neutral LOH events (Fig. 4). The BAF profile, for instance, can help identify copy-neutral LOH events, which have comparable read depth with the non-altered regions of the genome (Fig. 4).

Fig. 4: Impact of different copy number alterations on read depth and BAF profiles.
figure 4
In a diploid region with no alteration, there is one copy of each of the maternal and paternal alleles (alleles 1 and 2), so the total copy number is 2, the minor allele copy number is 1, the B-allele frequency (BAF) profile is centred on 0.5 and the read depth is approximately the same between the tumour and normal samples. In the event of a deletion, the total copy number decreases to 1 and the minor allele copy number to 0. The coverage of that region in the tumour sample drops, and the BAF of heterozygous single-nucleotide polymorphisms becomes either 0 or 1, with deviation towards 0.5 due to the infiltration of normal tissue. An amplification leads to an increase in the total copy number to 3, but the copy number of the minor allele remains 1 because the non-amplified allele is not altered. The BAF profile shifts towards 1/3 and 2/3, corresponding to the fractions of reads covering the non-amplified or amplified allele, respectively. Finally, the total copy number for copy-neutral loss of heterozygosity (LOH) events, where one allele is amplified and the other is lost, is 2, and the minor copy number is 0, reflecting the loss of one allele. Thus, the BAF profile is similar to that of a deletion, although the read depth of the tumour relative to the normal sample is not altered. These examples illustrate the importance of integrating depth of coverage and BAF information to fully characterize copy number alterations.

Full size image
Most segmentation methods can detect large, chromosome-scale CNAs, but often give conflicting copy number profiles at higher resolution165. This inconsistency is, in part, because the parameters in each method are tuned based on specific data sets with certain assumptions, for example, on sequencing coverage, availability of matched controls, tumour purity or the size of the CNA. Careful testing and parameter adjustments for the data set of interest appear to be as important as the underlying algorithm. Given that a change in copy number is a result of a genome rearrangement, incorporating SV information helps identify CNAs more accurately166. In the PCAWG Consortium, consensus copy number profiles for WGS were derived from integrating the output of six algorithms (ABSOLUTE, ACEseq, Battenberg, cloneHD, JaBbA and Sclust)161,163,167,168,169,170 using an ad hoc procedure that incorporates SV breakpoints to improve the accuracy of segment boundaries. Methods such as Weaver171, JaBbA172 and Reconstructing Cancer Karyotypes173 use graph-based approaches to jointly model breakpoint, read depth and BAF data, allowing allele-specific reconstruction of cancer genome configurations and of the timing of SVs.

Although WGS offers the best platform for detecting CNAs, CNAs can also be inferred using exome174,175,176,177,178,179,180 or targeted sequencing data7, albeit with lower accuracy and resolution. Not surprisingly, benchmarking studies often show low concordance of exome-derived copy number profiles between algorithms and in comparison with matched WGS profiles181,182. The main challenge for capture-based platforms is the variable read coverage across the genome introduced by the uneven capture efficiency of the probes. Many methods, such as ExomeCNV177 and CONTRA179, perform normalization of the read coverage and carry out a segmentation algorithm on the tumour–normal ratio profile. Other methods, such as CopywriteR183 and CNVkit184, improve genome-wide profiles by incorporating off-target reads. More recent algorithms, such as Sequenza185 and FACETS162, infer allele-specific copy numbers, tumour purity and ploidy simultaneously by fitting both sequencing depth and BAF data. It is also possible to identify chimeric reads from genomic rearrangements in exome data but with low sensitivity186.

Once CNAs are found in a set of samples, regions altered at a higher than expected frequency are identified using algorithms such as GISTIC187, as such regions are likely to be driver events under positive selection. To infer the timing of some key events in tumour evolution (see below), the precise copy number for each CNA region must also be determined, based on accurate estimation of tumour purity and ploidy. Although such estimates derived from sequencing data188 are generally concordant with those derived from histopathology, concomitant inference of purity, ploidy and genome-wide copy number profiles for genomes with complex karyotypes may not lead to a unique mathematical solution. Thus, estimates of tumour purity and ploidy made by an algorithm should be checked with experimental data or the output of multiple algorithms should be examined2,189.

Detecting structural variants
Upon mapping paired-end sequencing reads from the tumour genome to the reference genome, SVs are identified by the presence of split reads and clusters of discordant read pairs (Fig. 5). Nearly all SV detection algorithms, such as DELLY190, Lumpy191,192 and Meerkat193, rely on split read and discordant read pair information to detect SVs. However, breakpoint junctions often show complex patterns, such as microhomology tracts as a consequence of DNA repair and insertions generated through template-switching events, that can result in poor alignment. To mitigate this problem, methods such as CREST194, SvABA85, BRASS131 and Manta195, also include a local assembly step. Although computationally intensive, contigs assembled from raw reads improve read mapping and the characterization of insertion sequences at the breakpoints196. Read depth data can provide additional information to improve the detection of deletions and amplifications191,192.

Fig. 5: Examples of detecting somatic structural variants from patterns of paired-end reads.
figure 5
a | Detecting deletions. A somatic deletion bridges together two DNA segments (orange and green) that are not adjacent in the reference genome. When tumour reads are mapped to the reference genome, reads spanning the breakpoint between these two segments show a clipped (or split) alignment. These split reads contain two contiguous DNA segments that map to distant regions in the genome, corresponding to the two segments bridged by the deletion. Discordant read pairs spanning, but not mapping to, the breakpoint show longer than expected insert sizes. b | Detecting duplications. Tandem duplications also generate one novel adjacency, revealed by the presence of split reads. Discordant read pairs spanning the breakpoint map to the reference genome in an unexpected orientation and distance. c | Detecting inversions. Inversion of a genomic segment generates two new adjacencies. Reads mapping to these breakpoints show split patterns; discordant read pairs also occur as shown. d | Detecting translocations. A translocation bridges two genomic regions located in distant regions to generate a novel adjacency, which is revealed by the presence of split reads and clusters of discordant read pairs. In the inter-chromosomal translocation example shown, one read of the discordant read pair maps to one chromosome and the mate read maps to another chromosome. e | Detecting viral insertions. The insertion of viral sequences in the genome generates two adjacencies, as shown. A split read has one side mapping to the viral genome and the other side mapping to the human genome; a discordant read pair has one read mapping to the viral genome and the other read mapping to the human genome. Identification of the viral species inserted in the genome requires a database of viral genomes. f | Detecting transposable element insertions. Transposable element insertions are flanked by target site duplications (TSDs; shown in blue) at both ends of the insertion and by poly-A tails (shown in red) on one side. Split reads and discordant read pairs have similar patterns to those seen during viral insertion, but with transposable element consensus sequences rather than viral sequences. For all panels, depending on the sequencing depth and clonality of the alteration, only a subset of the read patterns may be observed in the data. There may be increased ploidy and multiple alterations in the same region, further complicating the detection and classification of these structural variants (SVs).

Full size image
Detecting somatic SVs is substantially more difficult than detecting germline SVs because of the low VAF of some somatic SVs. For example, if the tumour is sequenced at 60×, a subclonal SV present in 33% of diploid cancer cells in a sample with 30% tumour purity might only have, on average, 3–4 discordant and/or split reads supporting the insertion. In addition, the number of supporting reads will fluctuate due to non-uniform read coverage across the genome and sampling variation. Thus, the dynamic determination of appropriate thresholds (for example, number of supporting split reads) depending on the local context, as well as various filters to increase detection sensitivity, are key to a successful algorithm. Not surprisingly, benchmarking of SV detection algorithms shows discrepancies in specificity and recall, even across callers based on the same strategy191. Although improvement is not guaranteed197, combining the output of multiple callers to increase specificity is a common strategy, both for germline198,199 and somatic2 events. For instance, in the PCAWG project, only SVs detected by at least two out of four algorithms were considered2. Similarly, germline SVs are filtered out by comparing them against a matched normal sample or a catalogue of germline SVs, similar to the ‘panel of normals’ strategy for SNV and indel calls. Several artefacts can still lead to false positive SV calls, such as chimeric reads originating from whole-genome amplification or library preparation.

Analysis of sequence homology at SV breakpoints can reveal the DNA repair mechanisms operative in human cancers193. For instance, microhomology and templated insertions at the breakpoints suggest the involvement of replication-associated mechanisms, such as microhomology-mediated break-induced replication200. Examining the number of SVs categorized by type (deletion, insertion, inversion or translocation), size and whether they are clustered or not can also give mechanistic insights131. Similar to mutational signature analysis using SNVs, the observed spectrum can be decomposed into signatures that might be linked to specific biological processes. Other features, such as background SV rates and copy number information18,201, can also be incorporated into SV signature analysis. Some of the SV signatures have been shown to be relevant clinically, for example, the presence of indels showing microhomology indicates defects in homologous recombination202.

Characterizing complex rearrangements
WGS studies enabled the discovery of additional types of complex genomic rearrangements2,8. These include chromothripsis19,203, which is characterized by massive de novo rearrangements of one or multiple chromosomes; chromoplexy204,205, which involves balanced translocations across multiple chromosomes; and chromoanasynthesis206, which is characterized by low-level copy number gains originating from template-switching events. WGS data also enabled the detailed characterization of known mechanisms underlying SV formation, such as breakage–fusion–bridge cycles (BFB cycles; characterized by copy number gains associated with multiple fold-back inversions)207 and the generation of extrachromosomal DNA elements208, which constitute self-replicating, circular DNA structures amplified to high copy numbers and often contain oncogenes209.

The diversity, complexity and overlapping features of complex genomic rearrangements, coupled to their co-localization in highly rearranged tumours, make their detection and classification challenging19,210, often requiring extensive manual curation. CTLPScanner211 and ShatterProof212 detect chromothripsis in array data based on the clustering of breakpoints and oscillating copy number patterns; ShatterSeek19 and chromAL210, developed for analysing WGS, utilize intra-chromosomal and inter-chromosomal SVs and copy number data to increase sensitivity and specificity and can detect events involving multiple chromosomes. Detection of chromoanasynthesis and BFB cycles relies on ad hoc solutions, whereas ChainFinder204 and AmpliconArchitect208 are specifically designed to detect chromoplexy and extrachromosomal circular DNA elements respectively. Reconstruction of the genomic sequence generated by these complex rearrangements is generally intractable using short reads only, as breakpoints falling in repetitive or low-complexity regions cannot always be reliably detected213. To characterize the full spectrum of somatic variation in a haplotype-resolved manner, data from multiple platforms must be integrated, for example, combining short reads with optical mapping, long-read sequencing and conformation capture information30,214,215.

Detecting gene fusions using RNA-seq data
Although limited in sensitivity for transcripts expressed at low levels, RNA-seq data can be used to identify fusion oncogenes216,217. A standard approach consists of mapping sequencing reads to the transcriptome to identify discordantly mapped reads or reads mapping to the fusion junction. Tools such as Arriba218, TopHat-Fusion219, STAR-Fusion220 and deFuse221 follow this strategy. Other methods, such as TrinityFusion220, CICERO222 and JAFFA-Assembly223, perform de novo assembly of sequencing reads to identify chimeric fusions. Methods relying on aligned reads show better performance than assembly-based callers, likely due to the difficulty in assembling de novo fusion transcripts when few reads spanning the junction are present220. However, assembly-based methods are more sensitive than those relying on aligned reads for reconstructing complex breakpoint junctions, such as those involving non-templated insertions222 or viral and bacterial sequences220. Overall, the sensitivity, recall and computational efficiency of these tools are highly variable, with fusion calls often showing little overlap between tools216,224. Thus, when detecting fusion oncogenes, as for other variant types, considering calls made by multiple algorithms improves specificity23,220,224, and filtering calls detected in normal samples helps to further reduce the false positive rate23,225. Profiling both DNA and RNA, or enriching for transcripts of interest showing low expression levels, also increases the sensitivity of detection for clinically relevant fusions225,226.

Clonal composition and evolution
Cancer progression is an evolutionary process characterized by clonal competition that is fuelled by the accumulation of somatic mutations. As a result, tumours are complex mixtures of cells with different morphological and molecular profiles. Intra-tumour heterogeneity underpins drug resistance and relapse17,227, and is associated with poor prognosis. Thus, dissecting clonal structure based on mutational data is important for understanding the molecular underpinnings of cancer evolution.

Clonal mutations are accrued in cancer development and are thus present in most cancer cells, whereas subclonal mutations are detected in a subset of cancer cells. Most algorithms discussed above can be used to detect subclonal mutations, but specialized methods have been developed to infer the subclonal architecture, especially when multiple samples (either across time or space) from the same individual are available (reviewed in refs228,229,230). Popular tools such as SciClone231, PyClone232 and EXPANDS11 were originally designed for high-coverage exomes, whereas other tools are focused on WGS-based inference165,167,233,234. Central to these methods is the notion that mutations from the same subclone should have the same cancer cell fraction. Assignment of somatic mutations to subclones is achieved by clustering their VAFs adjusted for normal contamination and copy number.

Although WGS with standard sequencing depth is limited in detecting subclonal mutations, its genome-wide coverage of SNVs and more accurate characterization of CNAs allow for inference of the temporal order of CNAs relative to SNVs22,168. In the case of an early copy number gain, for example, most somatic mutations will be present in just one copy as they would have been acquired after the copy number gain; for a copy number gain late in tumour evolution, the majority of somatic mutations, assuming a comparable mutation rate, will be present in two out of three copies, because the mutations would have been amplified in the same event. Similarly, point mutations occurring before whole-genome doubling would be present in two out of four copies, and those accrued after whole-genome doubling would be present in only one copy. By applying this rationale to the set of somatic mutations detected in a cancer genome, the temporal order of somatic mutations during cancer evolution can be established235. Recent tumour evolution analysis of the PCAWG cohort uncovered common patterns of tumour evolution across 39 cancer types, revealing that some driver alterations, such as the formation of isochromosome 17q in medulloblastomas, date back to early development22. In other studies, the timing of CNAs and point mutations revealed a latency of years to decades between the acquisition of the early driver alterations and the major clonal expansion in lung adenocarcinomas236 and renal cell carcinomas237.

Although nearly all genomic analyses of tumour samples are based on a single biopsy, multiregional sampling is needed to reveal spatially complex patterns of clonal distribution. Such multiregional studies have proven powerful in delineating the heterogeneity of tumour clones and their evolutionary trajectories, underscoring the commonality of evolutionary branching and the role of subclonal drivers and chromosomal instability238 (recently reviewed in refs17,239). Analysis of longitudinally collected samples is ideal for understanding the effect of clonal dynamics over time, but most such studies focused on haematologic malignancies and selected solid tumour types that are easily sampled240,241. These studies showed the impact of treatments on the mutational landscape of tumours and their clonal compositions242. Although cancer genome analyses including TCGA and the PCAWG have been largely restricted to primary tumours, more recent efforts have collected large numbers of clinically annotated WGS and whole-exome sequencing data sets for metastatic samples8,9, permitting the characterization of the effect of chemotherapeutic agents138 and radiotherapy243 on the genomes of cancer cells.

Visualizing and exploring cancer genomes
A critical component at all stages of cancer genome analysis is data visualization and exploratory analysis. At the variant calling step, visual inspection of the read-level data for candidate mutations is customary and performed using tools such as the Integrative Genomics Viewer (IGV)244. To investigate the functional and therapeutic relevance of somatic mutations, cBioPortal245 provides easy access to a comprehensive set of genomic and clinical data from large-scale cancer genomics projects. Other portals include Genomic Data Commons by the US National Cancer Institute246, GenomePaint (paediatric cancers)247 and the International Cancer Genome Consortium data portal248. These portals offer many tools for interactively exploring genomic alterations across genes, samples and pathways, as well as for correlating them with clinical attributes. Currently, most tools are focused on SNVs and CNAs; to take full advantage of WGS data, tools for visualizing various types of SVs and for the integrative analysis of multiple data types will be helpful.

Conclusions and perspectives
In the last decade, we have witnessed tremendous growth in sequencing capability, accompanied by growing sophistication in computational tools. As DNA sequencing is now a commodity, the amount of data generated by the cancer research community will continue to increase. To harness the potential of these data, effective consolidation of data and their integrative analysis using efficient cloud-based tools will be needed. The recent PCAWG Consortium efforts were exemplary, yet there are tens or hundreds of thousands of genomes that have been profiled but are not being fully utilized. Data access and utilization are often limited due to, for example, restrictive language on patient consent forms, government regulations, especially for sharing across countries, and incomplete annotation of the data by researchers that submit them. Improved data sharing practices and the development of scalable cloud-based infrastructure for storage, analysis and sharing of genomic data249,250 will be essential for researchers to utilize all of the available sequencing data.

For analytical tools, we envision exciting new insights from integration of genomics data with other data modalities, such as imaging and histopathology data251,252,253,254, as well as an expanding role for methods based on deep learning. The development of new methods for mining and extracting clinical information from electronic health records will also add new dimensions to the interpretation of genomics data in the context of disease progression. New technologies that will be prominent in the next few years include non-invasive techniques based on circulating tumour DNA to characterize tumour heterogeneity and monitor disease255; long-read technologies — assuming a substantial decrease in their cost — that can overcome many of the limitations discussed above, especially in characterization of the repetitive regions and SVs; and single-cell DNA sequencing technologies, aided by improved DNA amplification techniques256,257 and with concurrent profiling of the RNA or epigenome of the same cells, to interrogate cellular heterogeneity and clonal evolution at high resolution. All of these technologies will require the development of innovative bioinformatic tools for analysis, visualization and interpretation.