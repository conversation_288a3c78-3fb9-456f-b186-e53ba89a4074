<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VCF Format: A Comprehensive Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .code-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>VCF Format: A Comprehensive Tutorial</h1>
            <p>Understanding the Variant Call Format for Genomic Data</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction to VCF Format</a></li>
                <li><a href="#file-structure">2. File Structure and Syntax</a></li>
                <li><a href="#fixed-fields">3. Fixed Fields Specification</a></li>
                <li><a href="#genotype-fields">4. Genotype Fields and Formatting</a></li>
                <li><a href="#variant-representation">5. Variant Representation</a></li>
                <li><a href="#advanced-features">6. Advanced Features</a></li>
                <li><a href="#bcf-format">7. BCF Binary Format</a></li>
                <li><a href="#examples">8. Practical Examples and Best Practices</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction to VCF Format -->
        <div id="introduction" class="section">
            <h2>1. Introduction to VCF Format</h2>

            <div class="highlight">
                <strong>Universal Standard:</strong> The Variant Call Format (VCF) is the de facto standard for storing genetic variant information, enabling interoperability across the entire genomics ecosystem from sequencing to clinical applications.
            </div>

            <h3>What is VCF?</h3>

            <div class="info-box">
                <h4>Core Definition</h4>
                <p><strong>VCF (Variant Call Format):</strong> A text file format for storing genetic variant information including:</p>
                <ul>
                    <li><strong>Variant positions:</strong> Chromosome coordinates of genetic differences</li>
                    <li><strong>Reference and alternative alleles:</strong> What the genome "should be" vs. what was observed</li>
                    <li><strong>Quality scores:</strong> Confidence measures for variant calls</li>
                    <li><strong>Genotype information:</strong> Which variants are present in each sample</li>
                    <li><strong>Annotations:</strong> Functional predictions, population frequencies, and more</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        VCF: From Raw Sequencing Data to Clinical Insights
                    </text>

                    <!-- Sequencing reads -->
                    <g transform="translate(50, 60)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Raw Sequencing Reads
                        </text>

                        <!-- Reference genome -->
                        <g transform="translate(20, 40)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">Reference:</text>
                            <rect x="80" y="0" width="120" height="20" fill="#ecf0f1" stroke="#bdc3c7"/>
                            <text x="140" y="15" text-anchor="middle" font-size="12" fill="#2c3e50">ATCGATCGATCG</text>
                        </g>

                        <!-- Sample reads -->
                        <g transform="translate(20, 80)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">Sample reads:</text>
                            <rect x="80" y="0" width="100" height="15" fill="#3498db" opacity="0.7"/>
                            <text x="130" y="12" text-anchor="middle" font-size="10" fill="white">ATCAATCGATCG</text>
                            <rect x="90" y="20" width="100" height="15" fill="#3498db" opacity="0.7"/>
                            <text x="140" y="32" text-anchor="middle" font-size="10" fill="white">TCAATCGATCGA</text>
                            <rect x="100" y="40" width="100" height="15" fill="#3498db" opacity="0.7"/>
                            <text x="150" y="52" text-anchor="middle" font-size="10" fill="white">AATCGATCGAAA</text>
                        </g>

                        <!-- Variant detection -->
                        <g transform="translate(20, 140)">
                            <circle cx="120" cy="10" r="8" fill="#e74c3c"/>
                            <text x="120" y="15" text-anchor="middle" font-size="10" fill="white">G→A</text>
                            <text x="120" y="35" text-anchor="middle" font-size="10" fill="#e74c3c">SNP detected</text>
                        </g>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(300, 200)">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">VCF</text>
                    </g>

                    <!-- VCF representation -->
                    <g transform="translate(450, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            Structured VCF Record
                        </text>

                        <!-- VCF fields -->
                        <g transform="translate(20, 40)">
                            <rect width="280" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>

                            <!-- Field labels and values -->
                            <text x="10" y="25" font-size="11" font-weight="bold" fill="#2c3e50">CHROM:</text>
                            <text x="70" y="25" font-size="11" fill="#2c3e50">chr1</text>

                            <text x="10" y="45" font-size="11" font-weight="bold" fill="#2c3e50">POS:</text>
                            <text x="70" y="45" font-size="11" fill="#2c3e50">12345</text>

                            <text x="10" y="65" font-size="11" font-weight="bold" fill="#2c3e50">REF:</text>
                            <text x="70" y="65" font-size="11" fill="#2c3e50">G</text>

                            <text x="10" y="85" font-size="11" font-weight="bold" fill="#2c3e50">ALT:</text>
                            <text x="70" y="85" font-size="11" fill="#2c3e50">A</text>

                            <text x="10" y="105" font-size="11" font-weight="bold" fill="#2c3e50">QUAL:</text>
                            <text x="70" y="105" font-size="11" fill="#2c3e50">99.9</text>

                            <text x="150" y="25" font-size="11" font-weight="bold" fill="#2c3e50">FILTER:</text>
                            <text x="210" y="25" font-size="11" fill="#2c3e50">PASS</text>

                            <text x="150" y="45" font-size="11" font-weight="bold" fill="#2c3e50">INFO:</text>
                            <text x="210" y="45" font-size="11" fill="#2c3e50">DP=50;AF=0.5</text>

                            <text x="150" y="65" font-size="11" font-weight="bold" fill="#2c3e50">FORMAT:</text>
                            <text x="210" y="65" font-size="11" fill="#2c3e50">GT:DP:GQ</text>

                            <text x="150" y="85" font-size="11" font-weight="bold" fill="#2c3e50">SAMPLE:</text>
                            <text x="210" y="85" font-size="11" fill="#2c3e50">0/1:25:99</text>
                        </g>
                    </g>

                    <!-- Applications -->
                    <g transform="translate(100, 300)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            VCF Applications Across Genomics
                        </text>

                        <!-- Research -->
                        <g transform="translate(0, 40)">
                            <rect width="140" height="80" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Research</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Population genetics</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">GWAS studies</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Evolutionary analysis</text>
                        </g>

                        <!-- Clinical -->
                        <g transform="translate(160, 40)">
                            <rect width="140" height="80" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Clinical</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Diagnostic testing</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Pharmacogenomics</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Cancer genomics</text>
                        </g>

                        <!-- Bioinformatics -->
                        <g transform="translate(320, 40)">
                            <rect width="140" height="80" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Bioinformatics</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Variant annotation</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Quality control</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Data integration</text>
                        </g>

                        <!-- Databases -->
                        <g transform="translate(480, 40)">
                            <rect width="140" height="80" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Databases</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">dbSNP</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">ClinVar</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">gnomAD</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>VCF Evolution and Versions</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Version</th>
                        <th>Release Year</th>
                        <th>Key Features</th>
                        <th>Major Improvements</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>VCF 4.0</strong></td>
                        <td>2009</td>
                        <td>Basic variant representation</td>
                        <td>Initial standardization</td>
                    </tr>
                    <tr>
                        <td><strong>VCF 4.1</strong></td>
                        <td>2011</td>
                        <td>Improved genotype fields</td>
                        <td>Better phasing support</td>
                    </tr>
                    <tr>
                        <td><strong>VCF 4.2</strong></td>
                        <td>2013</td>
                        <td>Structural variants, breakends</td>
                        <td>Complex rearrangements</td>
                    </tr>
                    <tr>
                        <td><strong>VCF 4.3</strong></td>
                        <td>2017</td>
                        <td>gVCF, symbolic alleles</td>
                        <td>Reference blocks, CNVs</td>
                    </tr>
                    <tr>
                        <td><strong>VCF 4.4</strong></td>
                        <td>2021</td>
                        <td>Local alleles, base modifications</td>
                        <td>Efficiency improvements</td>
                    </tr>
                    <tr>
                        <td><strong>VCF 4.5</strong></td>
                        <td>2024</td>
                        <td>Enhanced metadata, tandem repeats</td>
                        <td>Long-read support</td>
                    </tr>
                </tbody>
            </table>

            <h3>Why VCF Matters</h3>

            <div class="math-box">
                <h4>Statistical Foundation</h4>
                <p><strong>Quality Scores:</strong> VCF uses Phred-scaled quality scores to quantify confidence</p>

                <p><strong>Phred Score Formula:</strong></p>
                $$Q = -10 \log_{10}(P_{\text{error}})$$

                <p><strong>Examples:</strong></p>
                <ul>
                    <li><strong>Q=10:</strong> 90% confidence (1 in 10 chance of error)</li>
                    <li><strong>Q=20:</strong> 99% confidence (1 in 100 chance of error)</li>
                    <li><strong>Q=30:</strong> 99.9% confidence (1 in 1,000 chance of error)</li>
                    <li><strong>Q=40:</strong> 99.99% confidence (1 in 10,000 chance of error)</li>
                </ul>

                <p><strong>Genotype Quality:</strong> Conditional probability that genotype call is wrong</p>
                $$GQ = -10 \log_{10}(P(\text{genotype wrong} | \text{variant exists}))$$
            </div>

            <div class="warning-box">
                <h4>Common Misconceptions</h4>
                <ul>
                    <li><strong>"VCF only stores SNPs":</strong> Modern VCF supports all variant types including structural variants</li>
                    <li><strong>"VCF is just for humans":</strong> Used across all species from bacteria to plants</li>
                    <li><strong>"VCF files are huge":</strong> BCF binary format and compression make files manageable</li>
                    <li><strong>"VCF is outdated":</strong> Actively developed with new features for emerging technologies</li>
                </ul>
            </div>
        </div>

        <!-- Section 2: File Structure and Syntax -->
        <div id="file-structure" class="section">
            <h2>2. File Structure and Syntax</h2>

            <div class="highlight">
                <strong>Three-Part Structure:</strong> VCF files consist of meta-information lines (##), a header line (#), and data lines, each serving a specific purpose in defining and storing variant information.
            </div>

            <h3>Overall File Organization</h3>

            <div class="code-box">
##fileformat=VCFv4.5
##fileDate=20240101
##source=MyVariantCaller_v2.1
##reference=GRCh38
##contig=&lt;ID=chr1,length=*********&gt;
##INFO=&lt;ID=DP,Number=1,Type=Integer,Description="Total Depth"&gt;
##FORMAT=&lt;ID=GT,Number=1,Type=String,Description="Genotype"&gt;
#CHROM	POS	ID	REF	ALT	QUAL	FILTER	INFO	FORMAT	Sample1
chr1	12345	rs123	G	A	99.9	PASS	DP=50	GT	0/1
chr1	67890	.	AT	A	85.2	PASS	DP=35	GT	1/1
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        VCF File Structure: Three Essential Components
                    </text>

                    <!-- Meta-information section -->
                    <g transform="translate(50, 60)">
                        <rect width="200" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Meta-information Lines
                        </text>
                        <text x="100" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">Start with ##</text>

                        <g transform="translate(10, 50)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">##fileformat=VCFv4.5</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">##reference=GRCh38</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">##INFO=&lt;ID=DP,...&gt;</text>
                            <text x="0" y="60" font-size="10" fill="#2c3e50">##FORMAT=&lt;ID=GT,...&gt;</text>
                        </g>
                    </g>

                    <!-- Header section -->
                    <g transform="translate(300, 60)">
                        <rect width="200" height="120" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">
                            Header Line
                        </text>
                        <text x="100" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">Starts with #</text>

                        <g transform="translate(10, 50)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">#CHROM POS ID REF ALT</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">QUAL FILTER INFO</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">FORMAT Sample1 Sample2</text>
                        </g>
                    </g>

                    <!-- Data section -->
                    <g transform="translate(550, 60)">
                        <rect width="200" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            Data Lines
                        </text>
                        <text x="100" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">Tab-delimited records</text>

                        <g transform="translate(10, 50)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">chr1 12345 rs123 G A</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">99.9 PASS DP=50</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">GT 0/1 1/0</text>
                        </g>
                    </g>

                    <!-- Character encoding info -->
                    <g transform="translate(100, 220)">
                        <rect width="600" height="150" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">
                            Character Encoding and Special Characters
                        </text>

                        <!-- UTF-8 info -->
                        <g transform="translate(20, 40)">
                            <text x="0" y="20" font-size="12" font-weight="bold" fill="#2c3e50">UTF-8 Encoding:</text>
                            <text x="0" y="40" font-size="11" fill="#2c3e50">• Superset of 7-bit ASCII</text>
                            <text x="0" y="55" font-size="11" fill="#2c3e50">• No byte order mark (BOM)</text>
                            <text x="0" y="70" font-size="11" fill="#2c3e50">• Line separators: LF or CR+LF</text>
                        </g>

                        <!-- Special characters -->
                        <g transform="translate(300, 40)">
                            <text x="0" y="20" font-size="12" font-weight="bold" fill="#2c3e50">Percent Encoding:</text>
                            <text x="0" y="40" font-size="11" fill="#2c3e50">%3A = : (colon)</text>
                            <text x="0" y="55" font-size="11" fill="#2c3e50">%3B = ; (semicolon)</text>
                            <text x="0" y="70" font-size="11" fill="#2c3e50">%3D = = (equals)</text>
                            <text x="0" y="85" font-size="11" fill="#2c3e50">%2C = , (comma)</text>
                            <text x="0" y="100" font-size="11" fill="#2c3e50">%25 = % (percent)</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Meta-information Lines</h3>

            <div class="info-box">
                <h4>Two Types of Meta-information</h4>

                <p><strong>Unstructured Lines:</strong></p>
                <div class="code-box">
##key=value
##fileformat=VCFv4.5
##fileDate=20240101
##source=MyVariantCaller_v2.1
                </div>

                <p><strong>Structured Lines:</strong></p>
                <div class="code-box">
##key=&lt;key=value,key=value,...&gt;
##INFO=&lt;ID=DP,Number=1,Type=Integer,Description="Total Depth"&gt;
##FORMAT=&lt;ID=GT,Number=1,Type=String,Description="Genotype"&gt;
##FILTER=&lt;ID=LowQual,Description="Low quality variant"&gt;
                </div>
            </div>

            <h3>Data Types</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Examples</th>
                        <th>Special Values</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Integer</strong></td>
                        <td>32-bit signed integer</td>
                        <td>42, -17, 0</td>
                        <td>Range: -2³¹+8 to 2³¹-1</td>
                    </tr>
                    <tr>
                        <td><strong>Float</strong></td>
                        <td>32-bit IEEE-754</td>
                        <td>3.14, -2.5e-3, 1.0</td>
                        <td>INF, -INF, NAN</td>
                    </tr>
                    <tr>
                        <td><strong>Character</strong></td>
                        <td>Single character</td>
                        <td>A, T, G, C</td>
                        <td>Case insensitive</td>
                    </tr>
                    <tr>
                        <td><strong>String</strong></td>
                        <td>Text sequence</td>
                        <td>"Hello", "rs123456"</td>
                        <td>UTF-8 encoded</td>
                    </tr>
                    <tr>
                        <td><strong>Flag</strong></td>
                        <td>Boolean presence</td>
                        <td>DB, SOMATIC</td>
                        <td>No value, just key</td>
                    </tr>
                </tbody>
            </table>

            <h3>Number Field Specifications</h3>

            <div class="math-box">
                <h4>Special Number Values</h4>
                <p><strong>Fixed Numbers:</strong> 1, 2, 3, ... (exact count)</p>

                <p><strong>Variable Numbers:</strong></p>
                <ul>
                    <li><strong>A:</strong> One value per alternate allele</li>
                    <li><strong>R:</strong> One value per allele (reference + alternates)</li>
                    <li><strong>G:</strong> One value per genotype</li>
                    <li><strong>. (dot):</strong> Variable, unknown, or unbounded</li>
                </ul>

                <p><strong>Genotype Count Formula:</strong></p>
                $$\text{Number of genotypes} = \binom{n + p - 1}{p}$$

                <p>Where n = number of alleles, p = ploidy</p>

                <p><strong>Examples:</strong></p>
                <ul>
                    <li><strong>Diploid, 2 alleles:</strong> $\binom{2+2-1}{2} = \binom{3}{2} = 3$ genotypes (0/0, 0/1, 1/1)</li>
                    <li><strong>Diploid, 3 alleles:</strong> $\binom{3+2-1}{2} = \binom{4}{2} = 6$ genotypes</li>
                    <li><strong>Triploid, 2 alleles:</strong> $\binom{2+3-1}{3} = \binom{4}{3} = 4$ genotypes</li>
                </ul>
            </div>
        </div>

        <!-- Section 3: Fixed Fields Specification -->
        <div id="fixed-fields" class="section">
            <h2>3. Fixed Fields Specification</h2>

            <div class="highlight">
                <strong>Eight Mandatory Fields:</strong> Every VCF record contains eight fixed fields that provide essential information about each variant, from genomic location to quality assessment.
            </div>

            <h3>The Eight Fixed Fields</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Example</th>
                        <th>Special Cases</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>CHROM</strong></td>
                        <td>String</td>
                        <td>Chromosome identifier</td>
                        <td>chr1, 22, X, MT</td>
                        <td>Must match reference</td>
                    </tr>
                    <tr>
                        <td><strong>POS</strong></td>
                        <td>Integer</td>
                        <td>1-based position</td>
                        <td>12345</td>
                        <td>0 or N+1 for telomeres</td>
                    </tr>
                    <tr>
                        <td><strong>ID</strong></td>
                        <td>String</td>
                        <td>Variant identifier</td>
                        <td>rs123456</td>
                        <td>. if unknown</td>
                    </tr>
                    <tr>
                        <td><strong>REF</strong></td>
                        <td>String</td>
                        <td>Reference allele</td>
                        <td>A, AT, ATCG</td>
                        <td>Must include padding base</td>
                    </tr>
                    <tr>
                        <td><strong>ALT</strong></td>
                        <td>String</td>
                        <td>Alternative allele(s)</td>
                        <td>G, A,T, &lt;DEL&gt;</td>
                        <td>. if no variant</td>
                    </tr>
                    <tr>
                        <td><strong>QUAL</strong></td>
                        <td>Float</td>
                        <td>Phred-scaled quality</td>
                        <td>99.9, 30.5</td>
                        <td>. if unknown</td>
                    </tr>
                    <tr>
                        <td><strong>FILTER</strong></td>
                        <td>String</td>
                        <td>Filter status</td>
                        <td>PASS, LowQual</td>
                        <td>. if not applied</td>
                    </tr>
                    <tr>
                        <td><strong>INFO</strong></td>
                        <td>String</td>
                        <td>Additional information</td>
                        <td>DP=50;AF=0.5</td>
                        <td>. if none</td>
                    </tr>
                </tbody>
            </table>

            <h3>Variant Representation Examples</h3>

            <div class="code-box">
# SNP (Single Nucleotide Polymorphism)
chr1    12345   rs123   G   A   99.9    PASS    DP=50;AF=0.5

# Insertion
chr1    67890   .       T   TATG    85.2    PASS    DP=35;SVTYPE=INS

# Deletion
chr1    98765   .       ATCG    A   92.1    PASS    DP=42;SVTYPE=DEL

# Complex substitution
chr1    11111   .       AT  GC  88.5    PASS    DP=28;COMPLEX
            </div>
        </div>

        <!-- Section 4: Genotype Fields and Formatting -->
        <div id="genotype-fields" class="section">
            <h2>4. Genotype Fields and Formatting</h2>

            <div class="highlight">
                <strong>Sample-Specific Data:</strong> Genotype fields provide per-sample information including allele calls, quality scores, and read depth, enabling population-scale variant analysis.
            </div>

            <h3>FORMAT and Sample Columns</h3>

            <div class="info-box">
                <h4>Genotype Field Structure</h4>
                <p><strong>FORMAT Column:</strong> Defines the order and type of data in sample columns</p>
                <p><strong>Sample Columns:</strong> Colon-separated values corresponding to FORMAT specification</p>

                <div class="code-box">
FORMAT      Sample1     Sample2     Sample3
GT:DP:GQ    0/1:25:99   1/1:30:95   0/0:20:85
                </div>
            </div>

            <h3>Key Genotype Fields</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>GT</strong></td>
                        <td>String</td>
                        <td>Genotype call</td>
                        <td>0/1, 1|0, ./.</td>
                    </tr>
                    <tr>
                        <td><strong>DP</strong></td>
                        <td>Integer</td>
                        <td>Read depth</td>
                        <td>25, 30, 42</td>
                    </tr>
                    <tr>
                        <td><strong>GQ</strong></td>
                        <td>Integer</td>
                        <td>Genotype quality</td>
                        <td>99, 85, 42</td>
                    </tr>
                    <tr>
                        <td><strong>AD</strong></td>
                        <td>R Integer</td>
                        <td>Allelic depth</td>
                        <td>15,10 (ref,alt)</td>
                    </tr>
                    <tr>
                        <td><strong>PL</strong></td>
                        <td>G Integer</td>
                        <td>Phred-scaled likelihoods</td>
                        <td>0,30,255</td>
                    </tr>
                </tbody>
            </table>

            <h3>Phasing Information</h3>

            <div class="math-box">
                <h4>Phasing Notation</h4>
                <p><strong>Unphased:</strong> / separator (e.g., 0/1)</p>
                <p><strong>Phased:</strong> | separator (e.g., 0|1)</p>

                <p><strong>Phase Sets:</strong> PS field groups phased variants</p>
                <div class="code-box">
GT:PS    0|1:12345    1|0:12345    0|1:67890
                </div>

                <p><strong>Interpretation:</strong></p>
                <ul>
                    <li>0|1:12345 - First allele from maternal, second from paternal chromosome</li>
                    <li>1|0:12345 - Same phase set, opposite configuration</li>
                    <li>0|1:67890 - Different phase set, independent phasing</li>
                </ul>
            </div>
        </div>

        <!-- Section 5: Variant Representation -->
        <div id="variant-representation" class="section">
            <h2>5. Variant Representation</h2>

            <div class="highlight">
                <strong>Universal Representation:</strong> VCF can represent all types of genetic variation from simple SNPs to complex structural variants, using standardized notation for interoperability.
            </div>

            <h3>Simple Variants</h3>

            <div class="code-box">
# SNP: G to A substitution at position 12345
chr1    12345   .   G   A   99.9    PASS    .

# Insertion: Insert ATG after position 67890
chr1    67890   .   T   TATG    85.2    PASS    .

# Deletion: Delete TCG at positions 98766-98768
chr1    98765   .   ATCG    A   92.1    PASS    .

# MNP: Multiple nucleotide polymorphism
chr1    11111   .   AT  GC  88.5    PASS    .
            </div>

            <h3>Structural Variants</h3>

            <div class="info-box">
                <h4>Symbolic Alleles</h4>
                <p><strong>Large Deletions:</strong></p>
                <div class="code-box">
chr1    12345   .   N   &lt;DEL&gt;   99  PASS    SVTYPE=DEL;SVLEN=-1000;END=13345
                </div>

                <p><strong>Insertions:</strong></p>
                <div class="code-box">
chr1    67890   .   T   &lt;INS&gt;   85  PASS    SVTYPE=INS;SVLEN=500
                </div>

                <p><strong>Copy Number Variants:</strong></p>
                <div class="code-box">
chr1    98765   .   N   &lt;CNV&gt;   92  PASS    SVTYPE=CNV;CN=3;END=99765
                </div>
            </div>
        </div>

        <!-- Section 6: Advanced Features -->
        <div id="advanced-features" class="section">
            <h2>6. Advanced Features</h2>

            <div class="highlight">
                <strong>Cutting-Edge Capabilities:</strong> Modern VCF supports advanced features like base modifications, local alleles, and complex genomic rearrangements for comprehensive genomic analysis.
            </div>

            <h3>Base Modifications</h3>

            <div class="info-box">
                <h4>Epigenetic Information</h4>
                <p><strong>5-Methylcytosine (5mC):</strong></p>
                <div class="code-box">
chr1    12345   .   C   .   .   PASS    .   GT:M5mC:DPM5mC    0/0:0.85:20
                </div>

                <p><strong>Multiple Modifications:</strong></p>
                <div class="code-box">
chr1    67890   .   C   .   .   PASS    .   GT:M5mC:M5hmC    0|0:0.7,0.1:0.2,0.8
                </div>
            </div>

            <h3>Local Alleles (LAA)</h3>

            <div class="math-box">
                <h4>Efficiency Optimization</h4>
                <p><strong>Problem:</strong> Large multi-allelic sites create huge genotype likelihood arrays</p>
                <p><strong>Solution:</strong> Local Allele Arrays (LAA) specify relevant alleles per sample</p>

                <div class="code-box">
# Traditional representation (inefficient)
POS REF ALT                 FORMAT      Sample1
1   G   A,C,T,&lt;*&gt;          GT:PL       2/2:90,.,.,80,.,0,.,.,.,.,100,.,110,.,120

# Local allele representation (efficient)
POS REF ALT                 FORMAT      Sample1
1   G   A,C,T,&lt;*&gt;          GT:LAA:LPL  2/4:2,4:90,80,0,100,110,120
                </div>
            </div>
        </div>

        <!-- Section 7: BCF Binary Format -->
        <div id="bcf-format" class="section">
            <h2>7. BCF Binary Format</h2>

            <div class="highlight">
                <strong>Compressed Efficiency:</strong> BCF (Binary Call Format) provides space-efficient storage and fast random access while maintaining full compatibility with VCF.
            </div>

            <h3>BCF Advantages</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Aspect</th>
                        <th>VCF (Text)</th>
                        <th>BCF (Binary)</th>
                        <th>Improvement</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>File Size</strong></td>
                        <td>Large (text)</td>
                        <td>Compact (binary)</td>
                        <td>50-90% reduction</td>
                    </tr>
                    <tr>
                        <td><strong>Parse Speed</strong></td>
                        <td>Slow (text parsing)</td>
                        <td>Fast (binary)</td>
                        <td>5-10× faster</td>
                    </tr>
                    <tr>
                        <td><strong>Random Access</strong></td>
                        <td>Sequential only</td>
                        <td>Indexed access</td>
                        <td>Instant queries</td>
                    </tr>
                    <tr>
                        <td><strong>Compatibility</strong></td>
                        <td>Universal</td>
                        <td>Tool dependent</td>
                        <td>Growing support</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 8: Practical Examples -->
        <div id="examples" class="section">
            <h2>8. Practical Examples and Best Practices</h2>

            <div class="highlight">
                <strong>Real-World Application:</strong> Understanding VCF through practical examples demonstrates proper usage patterns and common pitfalls in genomic data analysis.
            </div>

            <h3>Complete VCF Example</h3>

            <div class="code-box">
##fileformat=VCFv4.5
##fileDate=20240101
##source=GATK_HaplotypeCaller_v4.2
##reference=GRCh38
##contig=&lt;ID=chr20,length=62435964&gt;
##INFO=&lt;ID=DP,Number=1,Type=Integer,Description="Total Depth"&gt;
##INFO=&lt;ID=AF,Number=A,Type=Float,Description="Allele Frequency"&gt;
##FORMAT=&lt;ID=GT,Number=1,Type=String,Description="Genotype"&gt;
##FORMAT=&lt;ID=DP,Number=1,Type=Integer,Description="Read Depth"&gt;
##FORMAT=&lt;ID=GQ,Number=1,Type=Integer,Description="Genotype Quality"&gt;
#CHROM  POS     ID          REF ALT QUAL    FILTER  INFO            FORMAT      NA00001     NA00002
chr20   14370   rs6054257   G   A   29      PASS    DP=14;AF=0.5    GT:DP:GQ    0|0:1:48    1|0:8:48
chr20   17330   .           T   A   3       q10     DP=11;AF=0.017  GT:DP:GQ    0|0:3:49    0|1:5:3
chr20   1110696 rs6040355   A   G,T 67      PASS    DP=10;AF=0.33,0.67 GT:DP:GQ 1|2:6:21   2|1:0:2
            </div>

            <h3>Best Practices</h3>

            <div class="info-box">
                <h4>VCF Quality Guidelines</h4>
                <ul class="step-list">
                    <li><strong>Always include fileformat:</strong> First line must specify VCF version</li>
                    <li><strong>Define all INFO/FORMAT fields:</strong> Include complete meta-information</li>
                    <li><strong>Use standard field names:</strong> Follow reserved field conventions</li>
                    <li><strong>Validate files:</strong> Use vcf-validator or similar tools</li>
                    <li><strong>Sort records:</strong> Order by chromosome and position</li>
                    <li><strong>Index for access:</strong> Create .tbi or .csi index files</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Common Pitfalls</h4>
                <ul>
                    <li><strong>Missing padding bases:</strong> Indels require reference context</li>
                    <li><strong>Inconsistent chromosome naming:</strong> chr1 vs 1 causes issues</li>
                    <li><strong>Invalid quality scores:</strong> Negative or non-numeric QUAL values</li>
                    <li><strong>Malformed genotypes:</strong> Incorrect allele indices or separators</li>
                    <li><strong>Encoding issues:</strong> Non-UTF8 characters or improper escaping</li>
                </ul>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Initialize MathJax after page load
        window.addEventListener('load', function() {
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        });
    </script>
</body>
</html>
