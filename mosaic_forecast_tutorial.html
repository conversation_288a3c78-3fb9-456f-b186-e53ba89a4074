<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MosaicForecast: Accurate Detection of Mosaic Variants</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .outline {
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .outline h2 {
            color: #667eea;
            margin-top: 0;
        }
        
        .outline ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            font-weight: 500;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #fafbfc;
            border-radius: 10px;
            border: 1px solid #e1e5e9;
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 25px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .method-box {
            background: #e8f4f8;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .results-box {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .formula {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .nav-tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 30px;
        }
        
        .nav-tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
            transition: all 0.3s ease;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MosaicForecast</h1>
            <p>Accurate Detection of Mosaic Variants in Sequencing Data Without Matched Controls</p>
            <p><em>Nature Biotechnology, 2020</em></p>
        </div>
        
        <div class="content">
            <!-- Outline Section -->
            <div class="outline">
                <h2>📋 Tutorial Outline</h2>
                <ol>
                    <li><strong>Introduction to Mosaic Variants</strong> - Understanding somatic mosaicism and detection challenges</li>
                    <li><strong>MosaicForecast Framework</strong> - Three-step machine learning approach</li>
                    <li><strong>Read-Based Phasing</strong> - Haplotype classification and training set generation</li>
                    <li><strong>Machine Learning Models</strong> - Random Forest and genotype refinement</li>
                    <li><strong>Performance Evaluation</strong> - Comparison with existing methods</li>
                    <li><strong>Validation Results</strong> - Single-cell, trio, and targeted sequencing validation</li>
                    <li><strong>Advanced Features</strong> - Indel detection and complex mosaic events</li>
                    <li><strong>Applications and Impact</strong> - Clinical and research implications</li>
                </ol>
            </div>

            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('intro')">Introduction</button>
                <button class="nav-tab" onclick="showTab('framework')">Framework</button>
                <button class="nav-tab" onclick="showTab('phasing')">Phasing</button>
                <button class="nav-tab" onclick="showTab('ml')">Machine Learning</button>
                <button class="nav-tab" onclick="showTab('performance')">Performance</button>
                <button class="nav-tab" onclick="showTab('validation')">Validation</button>
                <button class="nav-tab" onclick="showTab('advanced')">Advanced</button>
                <button class="nav-tab" onclick="showTab('impact')">Impact</button>
            </div>

            <!-- Tab Contents -->
            <div id="intro" class="tab-content active">
                <div class="section">
                    <h2>🧬 Introduction to Mosaic Variants</h2>
                    
                    <div class="highlight">
                        <h3>What are Mosaic Variants?</h3>
                        <p><strong>Somatic mosaicism</strong> occurs when an individual harbors multiple populations of cells with distinct genotypes due to somatic mutations arising postzygotically. These mutations can occur during development, creating cellular diversity within a single individual.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="800" height="400" viewBox="0 0 800 400">
                            <!-- Background -->
                            <rect width="800" height="400" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Somatic Mosaicism Development</text>
                            
                            <!-- Zygote -->
                            <circle cx="100" cy="100" r="25" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                            <text x="100" y="105" text-anchor="middle" font-size="12" fill="white">Zygote</text>
                            
                            <!-- Early Development -->
                            <circle cx="250" cy="100" r="20" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                            <circle cx="280" cy="100" r="20" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                            <text x="265" y="105" text-anchor="middle" font-size="10" fill="white">Early</text>
                            <text x="265" y="115" text-anchor="middle" font-size="10" fill="white">Mutation</text>
                            
                            <!-- Tissue Development -->
                            <circle cx="400" cy="100" r="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                            <circle cx="420" cy="100" r="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                            <circle cx="440" cy="100" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                            <circle cx="460" cy="100" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                            
                            <!-- Adult Tissues -->
                            <rect x="500" y="85" width="60" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                            <rect x="570" y="85" width="60" height="30" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                            <rect x="640" y="85" width="60" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                            
                            <!-- Arrows -->
                            <path d="M125 100 L245 100" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <path d="M300 100 L395 100" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <path d="M475 100 L495 100" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <path d="M570 100 L635 100" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Labels -->
                            <text x="185" y="80" text-anchor="middle" font-size="12" fill="#7f8c8d">Cell Division</text>
                            <text x="347" y="80" text-anchor="middle" font-size="12" fill="#7f8c8d">Tissue Formation</text>
                            <text x="485" y="80" text-anchor="middle" font-size="12" fill="#7f8c8d">Organ Development</text>
                            <text x="602" y="80" text-anchor="middle" font-size="12" fill="#7f8c8d">Adult</text>
                            
                            <!-- Legend -->
                            <rect x="50" y="200" width="15" height="15" fill="#e74c3c"/>
                            <text x="75" y="212" font-size="12" fill="#2c3e50">Mutant Cells</text>
                            <rect x="200" y="200" width="15" height="15" fill="#27ae60"/>
                            <text x="225" y="212" font-size="12" fill="#2c3e50">Normal Cells</text>
                            <rect x="350" y="200" width="15" height="15" fill="#f39c12"/>
                            <text x="375" y="212" font-size="12" fill="#2c3e50">Other Mutations</text>
                            
                            <!-- VAF Distribution -->
                            <text x="400" y="280" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Variant Allele Fraction (VAF) Distribution</text>
                            
                            <!-- VAF Bars -->
                            <rect x="100" y="320" width="40" height="60" fill="#e74c3c" opacity="0.3"/>
                            <rect x="150" y="340" width="40" height="40" fill="#e74c3c" opacity="0.5"/>
                            <rect x="200" y="350" width="40" height="30" fill="#e74c3c" opacity="0.7"/>
                            <rect x="250" y="360" width="40" height="20" fill="#e74c3c" opacity="0.9"/>
                            <rect x="300" y="370" width="40" height="10" fill="#e74c3c"/>
                            
                            <text x="120" y="395" text-anchor="middle" font-size="10" fill="#7f8c8d">0.01</text>
                            <text x="170" y="395" text-anchor="middle" font-size="10" fill="#7f8c8d">0.05</text>
                            <text x="220" y="395" text-anchor="middle" font-size="10" fill="#7f8c8d">0.1</text>
                            <text x="270" y="395" text-anchor="middle" font-size="10" fill="#7f8c8d">0.2</text>
                            <text x="320" y="395" text-anchor="middle" font-size="10" fill="#7f8c8d">0.5</text>
                            
                            <!-- Arrow marker definition -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>Detection Challenges</h3>
                    <div class="method-box">
                        <h4>🔍 Key Challenges in Mosaic Variant Detection:</h4>
                        <ul>
                            <li><strong>Low VAF:</strong> Most mosaic mutations have very low variant allele fractions (VAFs) of 2-5% or less</li>
                            <li><strong>No Matched Control:</strong> Early developmental mutations exist in multiple tissues, making paired control filtering ineffective</li>
                            <li><strong>Artifact Distinction:</strong> Difficult to distinguish real mutations from sequencing artifacts and germline variants</li>
                            <li><strong>Limited Phasing:</strong> Only ~10-30% of variants are phasable using short-read sequencing</li>
                        </ul>
                    </div>

                    <h3>Current Methods and Limitations</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Advantages</th>
                                <th>Limitations</th>
                                <th>VAF Detection Range</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Cancer Mutation Callers</strong><br>(MuTect2, Strelka2)</td>
                                <td>High sensitivity for cancer mutations</td>
                                <td>Lower VAF limit of 2-5%, requires matched controls</td>
                                <td>2-5% and above</td>
                            </tr>
                            <tr>
                                <td><strong>Germline Callers</strong><br>(GATK HaplotypeCaller)</td>
                                <td>Can detect low VAF variants</td>
                                <td>High false positive rate, misclassifies germline variants</td>
                                <td>1-2% and above</td>
                            </tr>
                            <tr>
                                <td><strong>Specialized Tools</strong><br>(MosaicHunter)</td>
                                <td>Designed for mosaic detection</td>
                                <td>Limited to non-repeat regions, lower sensitivity</td>
                                <td>2% and above</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="results-box">
                        <h4>🎯 The MosaicForecast Solution</h4>
                        <p>MosaicForecast addresses these challenges through a novel three-step machine learning approach that:</p>
                        <ul>
                            <li>Uses read-based phasing to create high-confidence training sets</li>
                            <li>Leverages 31 read-level features for accurate classification</li>
                            <li>Applies genome-wide prediction without requiring matched controls</li>
                            <li>Achieves 7-43x improvement in precision compared to existing methods</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="framework" class="tab-content">
                <div class="section">
                    <h2>🏗️ MosaicForecast Framework</h2>
                    
                    <div class="highlight">
                        <h3>Three-Step Machine Learning Approach</h3>
                        <p>MosaicForecast employs a sophisticated three-step process that combines read-based phasing with machine learning to achieve high accuracy in mosaic variant detection without requiring matched control samples.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="500" viewBox="0 0 900 500">
                            <!-- Background -->
                            <rect width="900" height="500" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">MosaicForecast Three-Step Framework</text>
                            
                            <!-- Step 1: Training Set Generation -->
                            <rect x="50" y="80" width="200" height="120" fill="#3498db" stroke="#2980b9" stroke-width="3" rx="10"/>
                            <text x="150" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Step 1</text>
                            <text x="150" y="125" text-anchor="middle" font-size="12" fill="white">Training Set</text>
                            <text x="150" y="140" text-anchor="middle" font-size="12" fill="white">Generation</text>
                            <text x="150" y="160" text-anchor="middle" font-size="10" fill="white">Read-based phasing</text>
                            <text x="150" y="175" text-anchor="middle" font-size="10" fill="white">Haplotype classification</text>
                            <text x="150" y="190" text-anchor="middle" font-size="10" fill="white">hap=2, hap=3, hap>3</text>
                            
                            <!-- Step 2: RF Model Construction -->
                            <rect x="350" y="80" width="200" height="120" fill="#e74c3c" stroke="#c0392b" stroke-width="3" rx="10"/>
                            <text x="450" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Step 2</text>
                            <text x="450" y="125" text-anchor="middle" font-size="12" fill="white">Random Forest</text>
                            <text x="450" y="140" text-anchor="middle" font-size="12" fill="white">Model</text>
                            <text x="450" y="160" text-anchor="middle" font-size="10" fill="white">31 read-level features</text>
                            <text x="450" y="175" text-anchor="middle" font-size="10" fill="white">VAF, depth, quality</text>
                            <text x="450" y="190" text-anchor="middle" font-size="10" fill="white">strand bias, etc.</text>
                            
                            <!-- Step 3: Genome-wide Prediction -->
                            <rect x="650" y="80" width="200" height="120" fill="#27ae60" stroke="#229954" stroke-width="3" rx="10"/>
                            <text x="750" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Step 3</text>
                            <text x="750" y="125" text-anchor="middle" font-size="12" fill="white">Genome-wide</text>
                            <text x="750" y="140" text-anchor="middle" font-size="12" fill="white">Prediction</text>
                            <text x="750" y="160" text-anchor="middle" font-size="10" fill="white">Apply model to</text>
                            <text x="750" y="175" text-anchor="middle" font-size="10" fill="white">non-phasable sites</text>
                            <text x="750" y="190" text-anchor="middle" font-size="10" fill="white">High precision output</text>
                            
                            <!-- Arrows -->
                            <path d="M250 140 L350 140" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                            <path d="M550 140 L650 140" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                            
                            <!-- Input Data -->
                            <rect x="50" y="250" width="800" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="5"/>
                            <text x="450" y="270" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Input: MuTect2 Candidate Variants</text>
                            <text x="450" y="290" text-anchor="middle" font-size="12" fill="#7f8c8d">High sensitivity, low specificity initial calls</text>
                            
                            <!-- Feature Categories -->
                            <rect x="50" y="380" width="180" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
                            <text x="140" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Quality Features</text>
                            <text x="140" y="415" text-anchor="middle" font-size="10" fill="white">Base quality, mapping</text>
                            <text x="140" y="430" text-anchor="middle" font-size="10" fill="white">quality, mismatches</text>
                            
                            <rect x="250" y="380" width="180" height="80" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                            <text x="340" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Position Features</text>
                            <text x="340" y="415" text-anchor="middle" font-size="10" fill="white">Read position, strand</text>
                            <text x="340" y="430" text-anchor="middle" font-size="10" fill="white">bias, cycle bias</text>
                            
                            <rect x="450" y="380" width="180" height="80" fill="#1abc9c" stroke="#16a085" stroke-width="2" rx="5"/>
                            <text x="540" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Statistical Features</text>
                            <text x="540" y="415" text-anchor="middle" font-size="10" fill="white">VAF, depth, likelihoods</text>
                            <text x="540" y="430" text-anchor="middle" font-size="10" fill="white">genotype probabilities</text>
                            
                            <rect x="650" y="380" width="180" height="80" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="5"/>
                            <text x="740" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Context Features</text>
                            <text x="740" y="415" text-anchor="middle" font-size="10" fill="white">GC content, mappability</text>
                            <text x="740" y="430" text-anchor="middle" font-size="10" fill="white">sequence context</text>
                            
                            <!-- Arrow marker definition -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>Step 1: Training Set Generation via Read-Based Phasing</h3>
                    <div class="method-box">
                        <h4>🔬 Phasing Strategy:</h4>
                        <ul>
                            <li><strong>Haplotype Classification:</strong> Variants are classified based on the number of observed haplotypes</li>
                            <li><strong>hap=2:</strong> Consistent with heterozygous germline variants</li>
                            <li><strong>hap=3:</strong> Consistent with true mosaic mutations</li>
                            <li><strong>hap>3:</strong> Suggestive of low-mappability regions, CNVs, or artifacts</li>
                        </ul>
                    </div>

                    <h3>Step 2: Random Forest Model Construction</h3>
                    <div class="formula">
                        <h4>📊 Model Features (31 total):</h4>
                        <p>The Random Forest model uses 31 read-level features categorized into:</p>
                        <ul>
                            <li><strong>Quality Features (8):</strong> Base quality, mapping quality, mismatches per read</li>
                            <li><strong>Position Features (6):</strong> Read mapping position, strand bias, sequencing cycle bias</li>
                            <li><strong>Statistical Features (12):</strong> VAF, read depth, genotype likelihoods</li>
                            <li><strong>Context Features (5):</strong> GC content, mappability score, sequence context</li>
                        </ul>
                    </div>

                    <h3>Step 3: Genome-Wide Prediction</h3>
                    <div class="results-box">
                        <h4>🎯 Prediction Process:</h4>
                        <ol>
                            <li><strong>Model Application:</strong> Apply trained RF model to non-phasable candidate sites</li>
                            <li><strong>Feature Extraction:</strong> Compute 31 read-level features for each candidate</li>
                            <li><strong>Classification:</strong> Predict haplotype categories (hap=2, hap=3, hap>3)</li>
                            <li><strong>Filtering:</strong> Remove clustered variants and low-confidence regions</li>
                            <li><strong>Output:</strong> High-confidence mosaic variant calls</li>
                        </ol>
                    </div>

                    <div class="highlight">
                        <h4>🚀 Key Innovations:</h4>
                        <ul>
                            <li><strong>No Matched Control Required:</strong> Works with single samples</li>
                            <li><strong>High Precision:</strong> 7-43x improvement over existing methods</li>
                            <li><strong>Genome-Wide Coverage:</strong> Detects variants in both repeat and non-repeat regions</li>
                            <li><strong>Low VAF Detection:</strong> Sensitive to variants with VAF as low as 1-2%</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="phasing" class="tab-content">
                <div class="section">
                    <h2>🔗 Read-Based Phasing</h2>
                    
                    <div class="highlight">
                        <h3>Haplotype Classification Strategy</h3>
                        <p>Read-based phasing is the cornerstone of MosaicForecast's training set generation. By analyzing the co-occurrence of candidate mosaic variants with nearby germline SNPs, the method can classify variants into distinct haplotype categories that correlate with their biological origin.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="600" viewBox="0 0 900 600">
                            <!-- Background -->
                            <rect width="900" height="600" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">Read-Based Phasing Classification</text>
                            
                            <!-- Haplotype 2: Heterozygous Germline -->
                            <rect x="50" y="80" width="800" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                            <text x="450" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#27ae60">Haplotype = 2: Heterozygous Germline Variant</text>
                            
                            <!-- DNA strands for hap=2 -->
                            <line x1="100" y1="140" x2="800" y2="140" stroke="#27ae60" stroke-width="3"/>
                            <line x1="100" y1="160" x2="800" y2="160" stroke="#27ae60" stroke-width="3"/>
                            
                            <!-- Germline SNP -->
                            <circle cx="300" cy="150" r="8" fill="#3498db"/>
                            <text x="300" y="155" text-anchor="middle" font-size="10" fill="white">A</text>
                            <text x="300" y="175" text-anchor="middle" font-size="12" fill="#2c3e50">Germline SNP</text>
                            
                            <!-- Candidate variant (same haplotype) -->
                            <circle cx="500" cy="150" r="8" fill="#3498db"/>
                            <text x="500" y="155" text-anchor="middle" font-size="10" fill="white">T</text>
                            <text x="500" y="175" text-anchor="middle" font-size="12" fill="#2c3e50">Candidate (Het)</text>
                            
                            <!-- Reads -->
                            <rect x="280" y="180" width="40" height="8" fill="#3498db" opacity="0.7"/>
                            <rect x="480" y="180" width="40" height="8" fill="#3498db" opacity="0.7"/>
                            <text x="450" y="200" text-anchor="middle" font-size="10" fill="#7f8c8d">2 Haplotypes Observed</text>
                            
                            <!-- Haplotype 3: True Mosaic -->
                            <rect x="50" y="240" width="800" height="120" fill="#fef9e7" stroke="#f39c12" stroke-width="2" rx="10"/>
                            <text x="450" y="265" text-anchor="middle" font-size="18" font-weight="bold" fill="#f39c12">Haplotype = 3: True Mosaic Mutation</text>
                            
                            <!-- DNA strands for hap=3 -->
                            <line x1="100" y1="300" x2="800" y2="300" stroke="#f39c12" stroke-width="3"/>
                            <line x1="100" y1="320" x2="800" y2="320" stroke="#f39c12" stroke-width="3"/>
                            
                            <!-- Germline SNP -->
                            <circle cx="300" cy="310" r="8" fill="#3498db"/>
                            <text x="300" y="315" text-anchor="middle" font-size="10" fill="white">A</text>
                            
                            <!-- Mosaic mutation -->
                            <circle cx="500" cy="310" r="8" fill="#e74c3c"/>
                            <text x="500" y="315" text-anchor="middle" font-size="10" fill="white">T</text>
                            <text x="500" y="330" text-anchor="middle" font-size="12" fill="#2c3e50">Mosaic</text>
                            
                            <!-- Reads showing 3 haplotypes -->
                            <rect x="280" y="340" width="40" height="8" fill="#3498db" opacity="0.7"/>
                            <rect x="480" y="340" width="40" height="8" fill="#e74c3c" opacity="0.7"/>
                            <rect x="480" y="355" width="40" height="8" fill="#3498db" opacity="0.7"/>
                            <text x="450" y="375" text-anchor="middle" font-size="10" fill="#7f8c8d">3 Haplotypes Observed</text>
                            
                            <!-- Haplotype >3: Artifacts/CNVs -->
                            <rect x="50" y="400" width="800" height="120" fill="#fdf2f2" stroke="#e74c3c" stroke-width="2" rx="10"/>
                            <text x="450" y="425" text-anchor="middle" font-size="18" font-weight="bold" fill="#e74c3c">Haplotype > 3: Artifacts/CNVs/Repeat Regions</text>
                            
                            <!-- DNA strands for hap>3 -->
                            <line x1="100" y1="460" x2="800" y2="460" stroke="#e74c3c" stroke-width="3"/>
                            <line x1="100" y1="480" x2="800" y2="480" stroke="#e74c3c" stroke-width="3"/>
                            
                            <!-- Multiple variants -->
                            <circle cx="300" cy="470" r="8" fill="#3498db"/>
                            <text x="300" y="475" text-anchor="middle" font-size="10" fill="white">A</text>
                            <circle cx="400" cy="470" r="8" fill="#9b59b6"/>
                            <text x="400" y="475" text-anchor="middle" font-size="10" fill="white">C</text>
                            <circle cx="500" cy="470" r="8" fill="#e67e22"/>
                            <text x="500" y="475" text-anchor="middle" font-size="10" fill="white">G</text>
                            <circle cx="600" cy="470" r="8" fill="#1abc9c"/>
                            <text x="600" y="475" text-anchor="middle" font-size="10" fill="white">T</text>
                            
                            <!-- Reads showing >3 haplotypes -->
                            <rect x="280" y="500" width="40" height="8" fill="#3498db" opacity="0.7"/>
                            <rect x="380" y="500" width="40" height="8" fill="#9b59b6" opacity="0.7"/>
                            <rect x="480" y="500" width="40" height="8" fill="#e67e22" opacity="0.7"/>
                            <rect x="580" y="500" width="40" height="8" fill="#1abc9c" opacity="0.7"/>
                            <text x="450" y="520" text-anchor="middle" font-size="10" fill="#7f8c8d">>3 Haplotypes Observed</text>
                            
                            <!-- Legend -->
                            <rect x="50" y="550" width="15" height="15" fill="#3498db"/>
                            <text x="75" y="562" font-size="12" fill="#2c3e50">Germline SNP</text>
                            <rect x="200" y="550" width="15" height="15" fill="#e74c3c"/>
                            <text x="225" y="562" font-size="12" fill="#2c3e50">Mosaic Mutation</text>
                            <rect x="350" y="550" width="15" height="15" fill="#9b59b6"/>
                            <text x="375" y="562" font-size="12" fill="#2c3e50">Artifact/CNV</text>
                        </svg>
                    </div>

                    <h3>Phasing Process Details</h3>
                    <div class="method-box">
                        <h4>🔍 Phasing Algorithm:</h4>
                        <ol>
                            <li><strong>Germline SNP Identification:</strong> Scan reads up to 1kb from each candidate mosaic site</li>
                            <li><strong>Quality Filtering:</strong> Remove reads with low mapping quality (<20) or low base quality (<20)</li>
                            <li><strong>VAF Validation:</strong> Apply binomial test to ensure germline SNPs have ~50% VAF</li>
                            <li><strong>Haplotype Construction:</strong> Build haplotypes by connecting candidate variants with nearby germline SNPs</li>
                            <li><strong>Classification:</strong> Count distinct haplotypes and assign categories</li>
                        </ol>
                    </div>

                    <h3>Haplotype Categories and Biological Interpretation</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Haplotype Count</th>
                                <th>Biological Interpretation</th>
                                <th>Expected Frequency</th>
                                <th>Validation Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>hap=2</strong></td>
                                <td>Heterozygous germline variant</td>
                                <td>~50% of phasable sites</td>
                                <td>~94% (mostly het)</td>
                            </tr>
                            <tr>
                                <td><strong>hap=3</strong></td>
                                <td>True mosaic mutation</td>
                                <td>~25% of phasable sites</td>
                                <td>~50% (true mosaics)</td>
                            </tr>
                            <tr>
                                <td><strong>hap>3</strong></td>
                                <td>Artifacts, CNVs, repeat regions</td>
                                <td>~25% of phasable sites</td>
                                <td>~1% (false positives)</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>Training Set Generation</h3>
                    <div class="formula">
                        <h4>📊 Training Data Statistics:</h4>
                        <ul>
                            <li><strong>Phasable Sites:</strong> ~25% of candidate mosaics are phasable with at least one germline SNP</li>
                            <li><strong>Validation Coverage:</strong> 483 phasable sites evaluated with orthogonal data</li>
                            <li><strong>Single-cell Validation:</strong> 3 individuals with comprehensive single-cell WGS data</li>
                            <li><strong>Trio Validation:</strong> 2 individuals with parent-child sequencing data</li>
                            <li><strong>Targeted Sequencing:</strong> IonTorrent deep sequencing for validation</li>
                        </ul>
                    </div>

                    <div class="results-box">
                        <h4>🎯 Phasing Performance:</h4>
                        <p>The phasing approach successfully creates a high-confidence training set:</p>
                        <ul>
                            <li><strong>hap=3 Sites:</strong> 50% validation rate for true mosaics</li>
                            <li><strong>hap=2 Sites:</strong> 94% validation rate for germline heterozygous variants</li>
                            <li><strong>hap>3 Sites:</strong> 99% validation rate for artifacts/false positives</li>
                            <li><strong>Genome Coverage:</strong> Phasable sites distributed across both repeat and non-repeat regions</li>
                        </ul>
                    </div>

                    <div class="highlight">
                        <h4>💡 Key Insights:</h4>
                        <ul>
                            <li><strong>Limited Phasing:</strong> Only ~10-30% of variants are phasable with short-read sequencing</li>
                            <li><strong>Quality Dependence:</strong> Phasing accuracy depends on read quality and germline SNP density</li>
                            <li><strong>Training Set Quality:</strong> Phasable sites provide high-confidence labels for machine learning</li>
                            <li><strong>Genome-wide Application:</strong> Model trained on phasable sites can predict non-phasable sites</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="ml" class="tab-content">
                <div class="section">
                    <h2>🤖 Machine Learning Models</h2>
                    
                    <div class="highlight">
                        <h3>Two-Stage Machine Learning Approach</h3>
                        <p>MosaicForecast employs a sophisticated two-stage machine learning pipeline: first, a Random Forest model for haplotype prediction, followed by a genotype refinement step using multinomial logistic regression for improved accuracy.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="500" viewBox="0 0 900 500">
                            <!-- Background -->
                            <rect width="900" height="500" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">Two-Stage Machine Learning Pipeline</text>
                            
                            <!-- Stage 1: Random Forest -->
                            <rect x="50" y="80" width="350" height="300" fill="#e8f4f8" stroke="#3498db" stroke-width="3" rx="10"/>
                            <text x="225" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#3498db">Stage 1: Random Forest Model</text>
                            
                            <!-- Input Features -->
                            <rect x="70" y="120" width="310" height="60" fill="#bdc3c7" stroke="#95a5a6" stroke-width="2" rx="5"/>
                            <text x="225" y="140" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">31 Read-Level Features</text>
                            <text x="225" y="155" text-anchor="middle" font-size="10" fill="#7f8c8d">VAF, depth, quality, position, context</text>
                            <text x="225" y="170" text-anchor="middle" font-size="10" fill="#7f8c8d">strand bias, mismatches, likelihoods</text>
                            
                            <!-- RF Model -->
                            <rect x="70" y="200" width="310" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                            <text x="225" y="220" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Random Forest Classifier</text>
                            <text x="225" y="235" text-anchor="middle" font-size="10" fill="white">500 trees, max depth=10</text>
                            <text x="225" y="250" text-anchor="middle" font-size="10" fill="white">Feature importance ranking</text>
                            <text x="225" y="265" text-anchor="middle" font-size="10" fill="white">Cross-validation optimized</text>
                            
                            <!-- Output -->
                            <rect x="70" y="300" width="310" height="60" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                            <text x="225" y="320" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Haplotype Predictions</text>
                            <text x="225" y="335" text-anchor="middle" font-size="10" fill="white">hap=2, hap=3, hap>3</text>
                            <text x="225" y="350" text-anchor="middle" font-size="10" fill="white">Probability scores</text>
                            
                            <!-- Stage 2: Genotype Refinement -->
                            <rect x="500" y="80" width="350" height="300" fill="#fef9e7" stroke="#f39c12" stroke-width="3" rx="10"/>
                            <text x="675" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#f39c12">Stage 2: Genotype Refinement</text>
                            
                            <!-- PCA Features -->
                            <rect x="520" y="120" width="310" height="60" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
                            <text x="675" y="140" text-anchor="middle" font-size="14" font-weight="bold" fill="white">PCA Components</text>
                            <text x="675" y="155" text-anchor="middle" font-size="10" fill="white">Top 5 principal components</text>
                            <text x="675" y="170" text-anchor="middle" font-size="10" fill="white">~50% variance explained</text>
                            
                            <!-- Logistic Regression -->
                            <rect x="520" y="200" width="310" height="80" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="5"/>
                            <text x="675" y="220" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Multinomial Logistic Regression</text>
                            <text x="675" y="235" text-anchor="middle" font-size="10" fill="white">L1 regularization (glmnet)</text>
                            <text x="675" y="250" text-anchor="middle" font-size="10" fill="white">4-class classification</text>
                            <text x="675" y="265" text-anchor="middle" font-size="10" fill="white">het, mosaic, repeat, refhom</text>
                            
                            <!-- Final Output -->
                            <rect x="520" y="300" width="310" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                            <text x="675" y="320" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Refined Genotypes</text>
                            <text x="675" y="335" text-anchor="middle" font-size="10" fill="white">85% validation rate</text>
                            <text x="675" y="350" text-anchor="middle" font-size="10" fill="white">High-confidence calls</text>
                            
                            <!-- Arrow between stages -->
                            <path d="M400 230 L500 230" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                            <text x="450" y="220" text-anchor="middle" font-size="12" fill="#7f8c8d">Validation Data</text>
                            
                            <!-- Feature importance chart -->
                            <text x="450" y="420" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Top Feature Importance (Random Forest)</text>
                            
                            <!-- Feature bars -->
                            <rect x="100" y="440" width="120" height="20" fill="#3498db"/>
                            <text x="110" y="453" font-size="10" fill="white">VAF (0.15)</text>
                            
                            <rect x="100" y="465" width="100" height="20" fill="#e74c3c"/>
                            <text x="110" y="478" font-size="10" fill="white">Read Depth (0.12)</text>
                            
                            <rect x="100" y="490" width="80" height="20" fill="#27ae60"/>
                            <text x="110" y="503" font-size="10" fill="white">Base Quality (0.10)</text>
                            
                            <rect x="100" y="515" width="60" height="20" fill="#f39c12"/>
                            <text x="110" y="528" font-size="10" fill="white">Strand Bias (0.08)</text>
                            
                            <!-- Arrow marker definition -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>Stage 1: Random Forest Model</h3>
                    <div class="method-box">
                        <h4>🌲 Random Forest Configuration:</h4>
                        <ul>
                            <li><strong>Number of Trees:</strong> 500 decision trees</li>
                            <li><strong>Maximum Depth:</strong> 10 levels per tree</li>
                            <li><strong>Features per Split:</strong> Square root of total features</li>
                            <li><strong>Training Data:</strong> Phasable sites with known haplotype labels</li>
                            <li><strong>Cross-validation:</strong> 5-fold cross-validation for hyperparameter tuning</li>
                        </ul>
                    </div>

                    <h3>Feature Engineering and Selection</h3>
                    <div class="formula">
                        <h4>📊 31 Read-Level Features:</h4>
                        <p>The model uses comprehensive read-level features organized into four categories:</p>
                        
                        <h5>Quality Features (8 features):</h5>
                        <ul>
                            <li>Base quality at variant position</li>
                            <li>Mapping quality of supporting reads</li>
                            <li>Number of mismatches per read</li>
                            <li>Quality difference between ref/alt alleles</li>
                        </ul>
                        
                        <h5>Position Features (6 features):</h5>
                        <ul>
                            <li>Read mapping position bias</li>
                            <li>Strand bias (forward/reverse)</li>
                            <li>Sequencing cycle bias</li>
                            <li>Base query position differences</li>
                        </ul>
                        
                        <h5>Statistical Features (12 features):</h5>
                        <ul>
                            <li>Variant allele fraction (VAF)</li>
                            <li>Read depth and depth ratios</li>
                            <li>Genotype likelihoods for 4 genotypes</li>
                            <li>Binomial test statistics</li>
                        </ul>
                        
                        <h5>Context Features (5 features):</h5>
                        <ul>
                            <li>GC content in surrounding region</li>
                            <li>Mappability score</li>
                            <li>Three-nucleotide sequence context</li>
                            <li>Repeat region annotation</li>
                        </ul>
                    </div>

                    <h3>Stage 2: Genotype Refinement</h3>
                    <div class="results-box">
                        <h4>🔧 Refinement Process:</h4>
                        <ol>
                            <li><strong>Principal Component Analysis:</strong> Reduce 31 features to top 5 components</li>
                            <li><strong>Validation Data Integration:</strong> Use experimental validation results</li>
                            <li><strong>Multinomial Logistic Regression:</strong> 4-class classification model</li>
                            <li><strong>Regularization:</strong> L1 penalty to prevent overfitting</li>
                            <li><strong>Probability Calibration:</strong> Output confidence scores</li>
                        </ol>
                    </div>

                    <h3>Mathematical Formulation</h3>
                    <div class="formula">
                        <h4>🧮 Genotype Likelihood Calculation:</h4>
                        <p>For each candidate variant, the model calculates genotype likelihoods using Bernoulli sampling:</p>
                        
                        <p><strong>For genotype G with allele fraction θ:</strong></p>
                        <p>\[L(G|\theta) = \prod_{i=1}^{n} P(r_i|G, \theta)\]</p>
                        
                        <p><strong>Where:</strong></p>
                        <ul>
                            <li>\(r_i\) = read i</li>
                            <li>\(o_i\) = observed allele on read i</li>
                            <li>\(q_i\) = base quality on read i</li>
                            <li>\(\theta\) = true mutant allele fraction</li>
                        </ul>
                        
                        <p><strong>For mosaic variants:</strong></p>
                        <p>\[L(mosaic) = \int_0^1 L(G|\theta) \cdot \beta(\theta) d\theta\]</p>
                        
                        <p>Where \(\beta(\theta)\) is the beta function representing the prior distribution of allele fractions for mosaic variants.</p>
                    </div>

                    <div class="highlight">
                        <h4>🎯 Model Performance:</h4>
                        <ul>
                            <li><strong>Training Accuracy:</strong> 85% validation rate for refined genotypes</li>
                            <li><strong>Feature Importance:</strong> VAF, read depth, and base quality are top predictors</li>
                            <li><strong>Generalization:</strong> Model performs well across different read depths (50-250x)</li>
                            <li><strong>Robustness:</strong> Consistent performance in both repeat and non-repeat regions</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="performance" class="tab-content">
                <div class="section">
                    <h2>📈 Performance Evaluation</h2>
                    
                    <div class="highlight">
                        <h3>Comprehensive Benchmarking Against Existing Methods</h3>
                        <p>MosaicForecast was rigorously evaluated against state-of-the-art mosaic detection tools using multiple validation approaches, demonstrating superior performance across all metrics.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="600" viewBox="0 0 900 600">
                            <!-- Background -->
                            <rect width="900" height="600" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">Performance Comparison Across Methods</text>
                            
                            <!-- Precision Comparison -->
                            <text x="450" y="70" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Precision Comparison (Non-Repeat Regions)</text>
                            
                            <!-- Precision Bars -->
                            <rect x="100" y="100" width="40" height="8" fill="#e74c3c"/>
                            <text x="90" y="115" text-anchor="end" font-size="10" fill="#2c3e50">MuTect2</text>
                            <text x="150" y="108" text-anchor="start" font-size="10" fill="#2c3e50">8.9%</text>
                            
                            <rect x="100" y="130" width="60" height="8" fill="#f39c12"/>
                            <text x="90" y="145" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p2</text>
                            <text x="170" y="138" text-anchor="start" font-size="10" fill="#2c3e50">20%</text>
                            
                            <rect x="100" y="160" width="80" height="8" fill="#3498db"/>
                            <text x="90" y="175" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p5</text>
                            <text x="190" y="168" text-anchor="start" font-size="10" fill="#2c3e50">62%</text>
                            
                            <rect x="100" y="190" width="120" height="8" fill="#27ae60"/>
                            <text x="90" y="205" text-anchor="end" font-size="10" fill="#2c3e50">MosaicForecast-Phase</text>
                            <text x="230" y="198" text-anchor="start" font-size="10" fill="#2c3e50">76%</text>
                            
                            <rect x="100" y="220" width="140" height="8" fill="#8e44ad"/>
                            <text x="90" y="235" text-anchor="end" font-size="10" fill="#2c3e50">MosaicForecast-Refine</text>
                            <text x="250" y="228" text-anchor="start" font-size="10" fill="#2c3e50">85%</text>
                            
                            <!-- Repeat Regions Comparison -->
                            <text x="450" y="280" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Precision Comparison (Repeat Regions)</text>
                            
                            <!-- Repeat Precision Bars -->
                            <rect x="100" y="310" width="4" height="8" fill="#e74c3c"/>
                            <text x="90" y="325" text-anchor="end" font-size="10" fill="#2c3e50">MuTect2</text>
                            <text x="110" y="318" text-anchor="start" font-size="10" fill="#2c3e50">1%</text>
                            
                            <rect x="100" y="340" width="20" height="8" fill="#f39c12"/>
                            <text x="90" y="355" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p2</text>
                            <text x="130" y="348" text-anchor="start" font-size="10" fill="#2c3e50">19%</text>
                            
                            <rect x="100" y="370" width="40" height="8" fill="#3498db"/>
                            <text x="90" y="385" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p5</text>
                            <text x="150" y="378" text-anchor="start" font-size="10" fill="#2c3e50">58%</text>
                            
                            <rect x="100" y="400" width="80" height="8" fill="#27ae60"/>
                            <text x="90" y="415" text-anchor="end" font-size="10" fill="#2c3e50">MosaicForecast-Phase</text>
                            <text x="190" y="408" text-anchor="start" font-size="10" fill="#2c3e50">50%</text>
                            
                            <rect x="100" y="430" width="120" height="8" fill="#8e44ad"/>
                            <text x="90" y="445" text-anchor="end" font-size="10" fill="#2c3e50">MosaicForecast-Refine</text>
                            <text x="230" y="438" text-anchor="start" font-size="10" fill="#2c3e50">77%</text>
                            
                            <!-- VAF Detection Comparison -->
                            <text x="450" y="480" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Low VAF Detection (≤5%)</text>
                            
                            <!-- VAF Detection Bars -->
                            <rect x="100" y="510" width="20" height="8" fill="#e74c3c"/>
                            <text x="90" y="525" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p2</text>
                            <text x="130" y="518" text-anchor="start" font-size="10" fill="#2c3e50">0</text>
                            
                            <rect x="100" y="540" width="30" height="8" fill="#f39c12"/>
                            <text x="90" y="555" text-anchor="end" font-size="10" fill="#2c3e50">GATK-HC-p5</text>
                            <text x="140" y="548" text-anchor="start" font-size="10" fill="#2c3e50">4</text>
                            
                            <rect x="100" y="570" width="60" height="8" fill="#3498db"/>
                            <text x="90" y="585" text-anchor="end" font-size="10" fill="#2c3e50">MosaicHunter</text>
                            <text x="170" y="578" text-anchor="start" font-size="10" fill="#2c3e50">14</text>
                            
                            <rect x="100" y="600" width="120" height="8" fill="#27ae60"/>
                            <text x="90" y="615" text-anchor="end" font-size="10" fill="#2c3e50">MosaicForecast</text>
                            <text x="230" y="608" text-anchor="start" font-size="10" fill="#2c3e50">30</text>
                            
                            <!-- Scale indicators -->
                            <line x1="80" y1="100" x2="80" y2="230" stroke="#bdc3c7" stroke-width="1"/>
                            <line x1="80" y1="310" x2="80" y2="440" stroke="#bdc3c7" stroke-width="1"/>
                            <line x1="80" y1="510" x2="80" y2="610" stroke="#bdc3c7" stroke-width="1"/>
                            
                            <!-- Scale labels -->
                            <text x="75" y="100" text-anchor="end" font-size="8" fill="#7f8c8d">100%</text>
                            <text x="75" y="150" text-anchor="end" font-size="8" fill="#7f8c8d">50%</text>
                            <text x="75" y="200" text-anchor="end" font-size="8" fill="#7f8c8d">0%</text>
                            
                            <text x="75" y="310" text-anchor="end" font-size="8" fill="#7f8c8d">100%</text>
                            <text x="75" y="360" text-anchor="end" font-size="8" fill="#7f8c8d">50%</text>
                            <text x="75" y="410" text-anchor="end" font-size="8" fill="#7f8c8d">0%</text>
                            
                            <text x="75" y="510" text-anchor="end" font-size="8" fill="#7f8c8d">50</text>
                            <text x="75" y="560" text-anchor="end" font-size="8" fill="#7f8c8d">25</text>
                            <text x="75" y="610" text-anchor="end" font-size="8" fill="#7f8c8d">0</text>
                        </svg>
                    </div>

                    <h3>Comparison with Existing Methods</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Non-Repeat Precision</th>
                                <th>Repeat Precision</th>
                                <th>Low VAF Detection</th>
                                <th>Genome Coverage</th>
                                <th>Control Required</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>MuTect2</strong></td>
                                <td>8.9%</td>
                                <td>1%</td>
                                <td>Limited</td>
                                <td>Full genome</td>
                                <td>Yes</td>
                            </tr>
                            <tr>
                                <td><strong>GATK-HC-p2</strong></td>
                                <td>20%</td>
                                <td>19%</td>
                                <td>0 variants</td>
                                <td>Full genome</td>
                                <td>No</td>
                            </tr>
                            <tr>
                                <td><strong>GATK-HC-p5</strong></td>
                                <td>62%</td>
                                <td>58%</td>
                                <td>4 variants</td>
                                <td>Full genome</td>
                                <td>No</td>
                            </tr>
                            <tr>
                                <td><strong>MosaicHunter</strong></td>
                                <td>59%</td>
                                <td>N/A</td>
                                <td>14 variants</td>
                                <td>Non-repeat only</td>
                                <td>No</td>
                            </tr>
                            <tr>
                                <td><strong>MosaicForecast-Phase</strong></td>
                                <td>76%</td>
                                <td>50%</td>
                                <td>30 variants</td>
                                <td>Full genome</td>
                                <td>No</td>
                            </tr>
                            <tr>
                                <td><strong>MosaicForecast-Refine</strong></td>
                                <td>85%</td>
                                <td>77%</td>
                                <td>30 variants</td>
                                <td>Full genome</td>
                                <td>No</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>Performance Across Different VAF Ranges</h3>
                    <div class="method-box">
                        <h4>📊 VAF Sensitivity Analysis:</h4>
                        <ul>
                            <li><strong>VAF 0.02-0.05:</strong> MosaicForecast detects 30/41 variants (73%)</li>
                            <li><strong>VAF 0.05-0.2:</strong> High sensitivity maintained across range</li>
                            <li><strong>VAF 0.2-0.5:</strong> Performance degrades as VAF approaches 0.5</li>
                            <li><strong>VAF >0.5:</strong> Case-control approach recommended</li>
                        </ul>
                    </div>

                    <h3>Read Depth Performance</h3>
                    <div class="svg-container">
                        <svg width="800" height="400" viewBox="0 0 800 400">
                            <!-- Background -->
                            <rect width="800" height="400" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Performance vs Read Depth</text>
                            
                            <!-- X-axis -->
                            <line x1="100" y1="350" x2="700" y2="350" stroke="#34495e" stroke-width="2"/>
                            <text x="100" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">50x</text>
                            <text x="200" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">100x</text>
                            <text x="300" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">150x</text>
                            <text x="400" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">200x</text>
                            <text x="500" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">250x</text>
                            
                            <!-- Y-axis -->
                            <line x1="100" y1="50" x2="100" y2="350" stroke="#34495e" stroke-width="2"/>
                            <text x="80" y="50" text-anchor="end" font-size="12" fill="#2c3e50">100%</text>
                            <text x="80" y="125" text-anchor="end" font-size="12" fill="#2c3e50">75%</text>
                            <text x="80" y="200" text-anchor="end" font-size="12" fill="#2c3e50">50%</text>
                            <text x="80" y="275" text-anchor="end" font-size="12" fill="#2c3e50">25%</text>
                            <text x="80" y="350" text-anchor="end" font-size="12" fill="#2c3e50">0%</text>
                            
                            <!-- Sensitivity curve -->
                            <path d="M100 300 L200 280 L300 250 L400 220 L500 200" stroke="#3498db" stroke-width="3" fill="none"/>
                            <circle cx="100" cy="300" r="4" fill="#3498db"/>
                            <circle cx="200" cy="280" r="4" fill="#3498db"/>
                            <circle cx="300" cy="250" r="4" fill="#3498db"/>
                            <circle cx="400" cy="220" r="4" fill="#3498db"/>
                            <circle cx="500" cy="200" r="4" fill="#3498db"/>
                            
                            <!-- Precision curve -->
                            <path d="M100 320 L200 310 L300 300 L400 290 L500 280" stroke="#e74c3c" stroke-width="3" fill="none"/>
                            <circle cx="100" cy="320" r="4" fill="#e74c3c"/>
                            <circle cx="200" cy="310" r="4" fill="#e74c3c"/>
                            <circle cx="300" cy="300" r="4" fill="#e74c3c"/>
                            <circle cx="400" cy="290" r="4" fill="#e74c3c"/>
                            <circle cx="500" cy="280" r="4" fill="#e74c3c"/>
                            
                            <!-- Legend -->
                            <rect x="550" y="80" width="15" height="15" fill="#3498db"/>
                            <text x="575" y="92" font-size="12" fill="#2c3e50">Sensitivity</text>
                            <rect x="550" y="110" width="15" height="15" fill="#e74c3c"/>
                            <text x="575" y="122" font-size="12" fill="#2c3e50">Precision</text>
                            
                            <!-- Performance labels -->
                            <text x="400" y="400" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Read Depth (Coverage)</text>
                            <text x="50" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50" transform="rotate(-90, 50, 200)">Performance (%)</text>
                        </svg>
                    </div>

                    <h3>Key Performance Insights</h3>
                    <div class="results-box">
                        <h4>🎯 Performance Highlights:</h4>
                        <ul>
                            <li><strong>7-43x Precision Improvement:</strong> Dramatic increase over existing methods</li>
                            <li><strong>Genome-Wide Coverage:</strong> Works in both repeat and non-repeat regions</li>
                            <li><strong>Low VAF Detection:</strong> Superior sensitivity for variants with VAF ≤5%</li>
                            <li><strong>Read Depth Robustness:</strong> Maintains performance from 50x to 250x coverage</li>
                            <li><strong>No Control Required:</strong> Works with single samples</li>
                        </ul>
                    </div>

                    <div class="highlight">
                        <h4>💡 Performance Advantages:</h4>
                        <ul>
                            <li><strong>Superior Precision:</strong> 85% validation rate vs 8.9% for MuTect2</li>
                            <li><strong>Better Sensitivity:</strong> Detects 30 low-VAF variants vs 4 for GATK-HC-p5</li>
                            <li><strong>Repeat Region Coverage:</strong> 77% precision in repeat regions vs 1% for MuTect2</li>
                            <li><strong>Consistent Performance:</strong> Robust across different individuals and read depths</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="validation" class="tab-content">
                <div class="section">
                    <h2>🔬 Validation Results</h2>
                    
                    <div class="highlight">
                        <h3>Multi-Modal Validation Strategy</h3>
                        <p>MosaicForecast's performance was validated using three orthogonal approaches: single-cell sequencing, trio analysis, and targeted deep sequencing, providing comprehensive evidence of the method's accuracy.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="500" viewBox="0 0 900 500">
                            <!-- Background -->
                            <rect width="900" height="500" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">Multi-Modal Validation Approaches</text>
                            
                            <!-- Single-Cell Validation -->
                            <rect x="50" y="80" width="250" height="300" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="10"/>
                            <text x="175" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Single-Cell Sequencing</text>
                            
                            <!-- Single cells -->
                            <circle cx="100" cy="150" r="15" fill="#3498db"/>
                            <circle cx="130" cy="150" r="15" fill="#3498db"/>
                            <circle cx="160" cy="150" r="15" fill="#e74c3c"/>
                            <circle cx="190" cy="150" r="15" fill="#3498db"/>
                            <circle cx="220" cy="150" r="15" fill="#e74c3c"/>
                            
                            <text x="175" y="180" text-anchor="middle" font-size="12" fill="#2c3e50">3 Individuals</text>
                            <text x="175" y="195" text-anchor="middle" font-size="12" fill="#2c3e50">1465, 4638, 4643</text>
                            
                            <!-- Lineage tree -->
                            <line x1="175" y1="220" x2="175" y2="240" stroke="#34495e" stroke-width="2"/>
                            <line x1="175" y1="240" x2="140" y2="260" stroke="#34495e" stroke-width="2"/>
                            <line x1="175" y1="240" x2="210" y2="260" stroke="#34495e" stroke-width="2"/>
                            
                            <circle cx="140" cy="280" r="8" fill="#3498db"/>
                            <circle cx="210" cy="280" r="8" fill="#e74c3c"/>
                            
                            <text x="175" y="310" text-anchor="middle" font-size="10" fill="#7f8c8d">Lineage Analysis</text>
                            <text x="175" y="325" text-anchor="middle" font-size="10" fill="#7f8c8d">Clade Assignment</text>
                            
                            <!-- Trio Validation -->
                            <rect x="350" y="80" width="250" height="300" fill="#fef9e7" stroke="#f39c12" stroke-width="3" rx="10"/>
                            <text x="475" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Trio Analysis</text>
                            
                            <!-- Family structure -->
                            <circle cx="425" cy="180" r="20" fill="#3498db"/>
                            <text x="425" y="185" text-anchor="middle" font-size="10" fill="white">Father</text>
                            
                            <circle cx="525" cy="180" r="20" fill="#e74c3c"/>
                            <text x="525" y="185" text-anchor="middle" font-size="10" fill="white">Mother</text>
                            
                            <circle cx="475" cy="240" r="20" fill="#27ae60"/>
                            <text x="475" y="245" text-anchor="middle" font-size="10" fill="white">Child</text>
                            
                            <!-- Connection lines -->
                            <line x1="425" y1="200" x2="475" y2="220" stroke="#34495e" stroke-width="2"/>
                            <line x1="525" y1="200" x2="475" y2="220" stroke="#34495e" stroke-width="2"/>
                            
                            <text x="475" y="280" text-anchor="middle" font-size="12" fill="#2c3e50">2 Individuals</text>
                            <text x="475" y="295" text-anchor="middle" font-size="12" fill="#2c3e50">UMB5939, UMB5771</text>
                            
                            <text x="475" y="320" text-anchor="middle" font-size="10" fill="#7f8c8d">Parent-Child</text>
                            <text x="475" y="335" text-anchor="middle" font-size="10" fill="#7f8c8d">Inheritance Check</text>
                            
                            <!-- Targeted Sequencing -->
                            <rect x="650" y="80" width="200" height="300" fill="#fdf2f2" stroke="#e74c3c" stroke-width="3" rx="10"/>
                            <text x="750" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">IonTorrent Deep Seq</text>
                            
                            <!-- PCR tubes -->
                            <rect x="680" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            <rect x="700" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            <rect x="720" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            <rect x="740" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            <rect x="760" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            <rect x="780" y="150" width="15" height="25" fill="#bdc3c7" rx="2"/>
                            
                            <text x="750" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">75 Individuals</text>
                            <text x="750" y="205" text-anchor="middle" font-size="12" fill="#2c3e50">~30,000x Coverage</text>
                            
                            <!-- Sequencing reads -->
                            <rect x="680" y="230" width="100" height="4" fill="#3498db"/>
                            <rect x="680" y="240" width="80" height="4" fill="#e74c3c"/>
                            <rect x="680" y="250" width="90" height="4" fill="#27ae60"/>
                            <rect x="680" y="260" width="70" height="4" fill="#f39c12"/>
                            
                            <text x="750" y="285" text-anchor="middle" font-size="10" fill="#7f8c8d">Ultra-deep</text>
                            <text x="750" y="300" text-anchor="middle" font-size="10" fill="#7f8c8d">Validation</text>
                            
                            <!-- Validation results summary -->
                            <text x="450" y="420" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Validation Results Summary</text>
                            
                            <!-- Results bars -->
                            <rect x="100" y="450" width="120" height="20" fill="#27ae60"/>
                            <text x="110" y="463" font-size="10" fill="white">Single-Cell: 82% (78/95)</text>
                            
                            <rect x="100" y="475" width="140" height="20" fill="#f39c12"/>
                            <text x="110" y="488" font-size="10" fill="white">Trio Analysis: 94% (161/171)</text>
                            
                            <rect x="100" y="500" width="160" height="20" fill="#e74c3c"/>
                            <text x="110" y="513" font-size="10" fill="white">IonTorrent: 94% (PCR-free)</text>
                        </svg>
                    </div>

                    <h3>Single-Cell Sequencing Validation</h3>
                    <div class="method-box">
                        <h4>🔬 Single-Cell Approach:</h4>
                        <ul>
                            <li><strong>Sample Size:</strong> 3 individuals with comprehensive single-cell WGS data</li>
                            <li><strong>Validation Method:</strong> Lineage tree construction and clade assignment</li>
                            <li><strong>Validation Rate:</strong> 82% (78 of 95 variants confirmed)</li>
                            <li><strong>Advantage:</strong> Provides definitive evidence of somatic origin</li>
                        </ul>
                    </div>

                    <h3>Trio Analysis Validation</h3>
                    <div class="formula">
                        <h4>👨‍👩‍👧‍👦 Trio Validation Strategy:</h4>
                        <p>Two individuals (UMB5939 and UMB5771) had both parents sequenced (~50x coverage). Variants were classified as:</p>
                        <ul>
                            <li><strong>Mosaic:</strong> Present in child, absent from both parents</li>
                            <li><strong>CNV/Repeat:</strong> Present in either parent with <20% VAF</li>
                            <li><strong>Heterozygous:</strong> Present in either parent with ~50% VAF</li>
                        </ul>
                        <p><strong>Validation Rate:</strong> 94% (161 of 171 variants confirmed)</p>
                    </div>

                    <h3>Targeted Deep Sequencing Validation</h3>
                    <div class="results-box">
                        <h4>🧬 IonTorrent Deep Sequencing:</h4>
                        <ul>
                            <li><strong>Coverage:</strong> ~30,000x average depth</li>
                            <li><strong>Samples:</strong> 75 individuals evaluated</li>
                            <li><strong>PCR-free Libraries:</strong> 94% validation rate (161/171)</li>
                            <li><strong>PCR-based Libraries:</strong> 61% validation rate (42/68)</li>
                            <li><strong>Quality Control:</strong> Only variants present in case and absent from control</li>
                        </ul>
                    </div>

                    <h3>Validation Results by Region Type</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Validation Method</th>
                                <th>Non-Repeat Regions</th>
                                <th>Repeat Regions</th>
                                <th>Overall Rate</th>
                                <th>Sample Size</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Single-Cell</strong></td>
                                <td>85% (24/28)</td>
                                <td>77% (24/31)</td>
                                <td>82% (78/95)</td>
                                <td>3 individuals</td>
                            </tr>
                            <tr>
                                <td><strong>Trio Analysis</strong></td>
                                <td>95% (149/157)</td>
                                <td>86% (12/14)</td>
                                <td>94% (161/171)</td>
                                <td>2 individuals</td>
                            </tr>
                            <tr>
                                <td><strong>IonTorrent (PCR-free)</strong></td>
                                <td>87% (118/136)</td>
                                <td>85% (17/20)</td>
                                <td>94% (161/171)</td>
                                <td>64 samples</td>
                            </tr>
                            <tr>
                                <td><strong>IonTorrent (PCR-based)</strong></td>
                                <td>68% (42/62)</td>
                                <td>0% (0/6)</td>
                                <td>61% (42/68)</td>
                                <td>11 samples</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>Quality Control and Filtering</h3>
                    <div class="highlight">
                        <h4>🔍 Validation Quality Measures:</h4>
                        <ul>
                            <li><strong>Single-Cell:</strong> Lineage consistency and clade-specific mutations</li>
                            <li><strong>Trio:</strong> Mendelian inheritance patterns and VAF analysis</li>
                            <li><strong>IonTorrent:</strong> Case-control comparison and quality thresholds</li>
                            <li><strong>PCR Artifacts:</strong> G>T bias detection in PCR-based samples</li>
                            <li><strong>False Positive Filtering:</strong> Clustered variants and low-mappability regions</li>
                        </ul>
                    </div>

                    <h3>Validation Insights</h3>
                    <div class="method-box">
                        <h4>💡 Key Findings:</h4>
                        <ul>
                            <li><strong>High Accuracy:</strong> 82-94% validation rates across methods</li>
                            <li><strong>Region Robustness:</strong> Similar performance in repeat and non-repeat regions</li>
                            <li><strong>Library Quality:</strong> PCR-free libraries show superior validation rates</li>
                            <li><strong>VAF Independence:</strong> Performance maintained across different VAF ranges</li>
                            <li><strong>Individual Consistency:</strong> High validation rates across different individuals</li>
                        </ul>
                    </div>

                    <div class="results-box">
                        <h4>🎯 Validation Summary:</h4>
                        <p>The comprehensive validation strategy demonstrates MosaicForecast's high accuracy:</p>
                        <ul>
                            <li><strong>Single-Cell:</strong> 82% validation rate provides definitive somatic evidence</li>
                            <li><strong>Trio Analysis:</strong> 94% validation rate confirms inheritance patterns</li>
                            <li><strong>Deep Sequencing:</strong> 94% validation rate in PCR-free samples</li>
                            <li><strong>Overall Confidence:</strong> Multiple orthogonal methods confirm accuracy</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="advanced" class="tab-content">
                <div class="section">
                    <h2>🚀 Advanced Features</h2>
                    
                    <div class="highlight">
                        <h3>Beyond SNVs: Indel Detection and Complex Events</h3>
                        <p>MosaicForecast extends beyond single-nucleotide variant detection to identify mosaic insertions, deletions, and complex multi-nucleotide events, demonstrating the versatility of the machine learning approach.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="600" viewBox="0 0 900 600">
                            <!-- Background -->
                            <rect width="900" height="600" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">Advanced Variant Types Detection</text>
                            
                            <!-- SNV Section -->
                            <rect x="50" y="80" width="250" height="200" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="10"/>
                            <text x="175" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Single Nucleotide Variants</text>
                            
                            <!-- DNA sequence -->
                            <text x="175" y="140" text-anchor="middle" font-size="14" fill="#2c3e50">Reference: ATCGATCG</text>
                            <text x="175" y="160" text-anchor="middle" font-size="14" fill="#2c3e50">Mosaic: ATCGCTCG</text>
                            
                            <!-- Highlight mutation -->
                            <rect x="145" y="145" width="15" height="15" fill="#e74c3c" opacity="0.3"/>
                            <text x="152" y="155" text-anchor="middle" font-size="12" fill="#e74c3c">G→C</text>
                            
                            <text x="175" y="190" text-anchor="middle" font-size="12" fill="#7f8c8d">Validation Rate: 85%</text>
                            <text x="175" y="205" text-anchor="middle" font-size="12" fill="#7f8c8d">2,220 variants detected</text>
                            
                            <!-- Deletion Section -->
                            <rect x="325" y="80" width="250" height="200" fill="#fef9e7" stroke="#f39c12" stroke-width="3" rx="10"/>
                            <text x="450" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Mosaic Deletions</text>
                            
                            <!-- DNA sequence with deletion -->
                            <text x="450" y="140" text-anchor="middle" font-size="14" fill="#2c3e50">Reference: ATCGATCG</text>
                            <text x="450" y="160" text-anchor="middle" font-size="14" fill="#2c3e50">Mosaic: ATCG_CG</text>
                            
                            <!-- Highlight deletion -->
                            <rect x="415" y="145" width="30" height="15" fill="#e74c3c" opacity="0.3"/>
                            <text x="430" y="155" text-anchor="middle" font-size="12" fill="#e74c3c">-AT</text>
                            
                            <text x="450" y="190" text-anchor="middle" font-size="12" fill="#7f8c8d">Validation Rate: 75%</text>
                            <text x="450" y="205" text-anchor="middle" font-size="12" fill="#7f8c8d">102 variants detected</text>
                            
                            <!-- Insertion Section -->
                            <rect x="600" y="80" width="250" height="200" fill="#fdf2f2" stroke="#e74c3c" stroke-width="3" rx="10"/>
                            <text x="725" y="105" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">Mosaic Insertions</text>
                            
                            <!-- DNA sequence with insertion -->
                            <text x="725" y="140" text-anchor="middle" font-size="14" fill="#2c3e50">Reference: ATCGATCG</text>
                            <text x="725" y="160" text-anchor="middle" font-size="14" fill="#2c3e50">Mosaic: ATCGATATCG</text>
                            
                            <!-- Highlight insertion -->
                            <rect x="690" y="145" width="30" height="15" fill="#e74c3c" opacity="0.3"/>
                            <text x="705" y="155" text-anchor="middle" font-size="12" fill="#e74c3c">+AT</text>
                            
                            <text x="725" y="190" text-anchor="middle" font-size="12" fill="#7f8c8d">Validation Rate: 59%</text>
                            <text x="725" y="205" text-anchor="middle" font-size="12" fill="#7f8c8d">134 variants detected</text>
                            
                            <!-- Complex Events Section -->
                            <rect x="50" y="320" width="800" height="200" fill="#e8f4f8" stroke="#3498db" stroke-width="3" rx="10"/>
                            <text x="450" y="345" text-anchor="middle" font-size="18" font-weight="bold" fill="#3498db">Complex Mosaic Events</text>
                            
                            <!-- Multi-nucleotide variant -->
                            <text x="200" y="380" text-anchor="middle" font-size="14" fill="#2c3e50">Multi-Nucleotide Variant</text>
                            <text x="200" y="400" text-anchor="middle" font-size="12" fill="#7f8c8d">Reference: ATCGATCG</text>
                            <text x="200" y="415" text-anchor="middle" font-size="12" fill="#7f8c8d">Mosaic: ATCGCTCG</text>
                            <rect x="170" y="395" width="30" height="15" fill="#e74c3c" opacity="0.3"/>
                            <text x="185" y="405" text-anchor="middle" font-size="10" fill="#e74c3c">AT→CT</text>
                            
                            <!-- Clumped variants -->
                            <text x="450" y="380" text-anchor="middle" font-size="14" fill="#2c3e50">Clumped Variants</text>
                            <text x="450" y="400" text-anchor="middle" font-size="12" fill="#7f8c8d">Reference: ATCGATCG</text>
                            <text x="450" y="415" text-anchor="middle" font-size="12" fill="#7f8c8d">Mosaic: ATCGATCG</text>
                            <rect x="420" y="395" width="15" height="15" fill="#e74c3c" opacity="0.3"/>
                            <rect x="440" y="395" width="15" height="15" fill="#f39c12" opacity="0.3"/>
                            <text x="427" y="405" text-anchor="middle" font-size="10" fill="#e74c3c">G→C</text>
                            <text x="447" y="405" text-anchor="middle" font-size="10" fill="#f39c12">+T</text>
                            
                            <!-- Validation results -->
                            <text x="700" y="380" text-anchor="middle" font-size="14" fill="#2c3e50">Validation Results</text>
                            <text x="700" y="400" text-anchor="middle" font-size="12" fill="#7f8c8d">3 complex events</text>
                            <text x="700" y="415" text-anchor="middle" font-size="12" fill="#7f8c8d">2 confirmed in</text>
                            <text x="700" y="430" text-anchor="middle" font-size="12" fill="#7f8c8d">single cells</text>
                            
                            <!-- Performance comparison -->
                            <text x="450" y="480" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Performance Comparison</text>
                            
                            <!-- Performance bars -->
                            <rect x="100" y="510" width="120" height="15" fill="#27ae60"/>
                            <text x="110" y="520" text-anchor="start" font-size="10" fill="white">SNVs: 85%</text>
                            
                            <rect x="100" y="530" width="90" height="15" fill="#f39c12"/>
                            <text x="110" y="540" text-anchor="start" font-size="10" fill="white">Deletions: 75%</text>
                            
                            <rect x="100" y="550" width="70" height="15" fill="#e74c3c"/>
                            <text x="110" y="560" text-anchor="start" font-size="10" fill="white">Insertions: 59%</text>
                            
                            <rect x="100" y="570" width="60" height="15" fill="#3498db"/>
                            <text x="110" y="580" text-anchor="start" font-size="10" fill="white">Complex: 67%</text>
                        </svg>
                    </div>

                    <h3>Mosaic Indel Detection</h3>
                    <div class="method-box">
                        <h4>🔧 Indel Detection Approach:</h4>
                        <ul>
                            <li><strong>Input Source:</strong> MuTect2 indel calls with panel-of-normals filtering</li>
                            <li><strong>Feature Adaptation:</strong> Modified read-level features for indel-specific characteristics</li>
                            <li><strong>Quality Filters:</strong> Enhanced filtering for alignment uncertainty and clipping artifacts</li>
                            <li><strong>Validation Strategy:</strong> IonTorrent deep sequencing with multiple primer pairs</li>
                        </ul>
                    </div>

                    <h3>Deletion Detection Performance</h3>
                    <div class="formula">
                        <h4>📊 Deletion Results:</h4>
                        <ul>
                            <li><strong>Candidate Sites:</strong> 22,893 deletions from non-repeat regions</li>
                            <li><strong>Phasable Sites:</strong> 831 deletions with haplotype information</li>
                            <li><strong>High-Confidence Calls:</strong> 102 confident mosaic deletions</li>
                            <li><strong>Validation Rate:</strong> 75% (59 of 79 validated by IonTorrent)</li>
                            <li><strong>Single-Cell Confirmation:</strong> 2 of 2 variants confirmed in lineage analysis</li>
                        </ul>
                    </div>

                    <h3>Insertion Detection Performance</h3>
                    <div class="results-box">
                        <h4>📈 Insertion Results:</h4>
                        <ul>
                            <li><strong>Candidate Sites:</strong> 37,084 insertions from non-repeat regions</li>
                            <li><strong>High-Confidence Calls:</strong> 134 confident mosaic insertions</li>
                            <li><strong>PCR-free Validation:</strong> 59% (32 of 54 validated)</li>
                            <li><strong>PCR-based Validation:</strong> 0% (0 of 6 validated)</li>
                            <li><strong>Quality Control:</strong> IGV-based filtering for misalignment issues</li>
                        </ul>
                    </div>

                    <h3>Complex Mosaic Events</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Event Type</th>
                                <th>Description</th>
                                <th>Detection Method</th>
                                <th>Validation Rate</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Multi-Nucleotide Variants</strong></td>
                                <td>Two adjacent base substitutions</td>
                                <td>Modified SNV detection</td>
                                <td>67%</td>
                                <td>AT→CT, CG→TG</td>
                            </tr>
                            <tr>
                                <td><strong>Clumped Variants</strong></td>
                                <td>SNV + nearby insertion/deletion</td>
                                <td>Combined SNV/indel analysis</td>
                                <td>67%</td>
                                <td>G→C + insertion</td>
                            </tr>
                            <tr>
                                <td><strong>Complex Rearrangements</strong></td>
                                <td>Multiple linked variants</td>
                                <td>Haplotype-based detection</td>
                                <td>Limited data</td>
                                <td>Multiple linked changes</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>Feature Modifications for Indels</h3>
                    <div class="highlight">
                        <h4>🔧 Indel-Specific Adaptations:</h4>
                        <ul>
                            <li><strong>Alt Read Definition:</strong> Reads carrying alt allele OR clipped at mutant position</li>
                            <li><strong>Repeat Filtering:</strong> Exclude simple repeats and homopolymer regions</li>
                            <li><strong>Clipping Analysis:</strong> Filter sites with ≥10% reference reads being clipped</li>
                            <li><strong>Quality Assessment:</strong> Modified base quality comparison for indel boundaries</li>
                            <li><strong>Alignment Uncertainty:</strong> Enhanced detection of misalignment artifacts</li>
                        </ul>
                    </div>

                    <h3>Validation Challenges and Solutions</h3>
                    <div class="method-box">
                        <h4>⚠️ Indel Validation Challenges:</h4>
                        <ul>
                            <li><strong>PCR Artifacts:</strong> Replication slippage in PCR-based libraries</li>
                            <li><strong>Alignment Issues:</strong> Complex indel regions prone to misalignment</li>
                            <li><strong>Quality Degradation:</strong> Lower base quality around indel boundaries</li>
                            <li><strong>Validation Complexity:</strong> Multiple primer pairs required for confirmation</li>
                        </ul>
                        
                        <h4>✅ Solutions Implemented:</h4>
                        <ul>
                            <li><strong>Library Quality:</strong> Prefer PCR-free libraries for indel detection</li>
                            <li><strong>IGV Filtering:</strong> Manual inspection of read alignments</li>
                            <li><strong>Multiple Validation:</strong> Three primer pairs per indel site</li>
                            <li><strong>Control Comparison:</strong> Case vs control sample analysis</li>
                        </ul>
                    </div>

                    <h3>Performance Insights</h3>
                    <div class="results-box">
                        <h4>🎯 Key Findings:</h4>
                        <ul>
                            <li><strong>Deletion Superiority:</strong> 75% validation rate vs 59% for insertions</li>
                            <li><strong>Library Dependence:</strong> PCR-free libraries essential for accurate indel detection</li>
                            <li><strong>Complex Event Detection:</strong> 67% validation rate for multi-nucleotide variants</li>
                            <li><strong>Feature Robustness:</strong> Modified features maintain classification accuracy</li>
                            <li><strong>Genome Coverage:</strong> Indel detection limited to non-repeat regions</li>
                        </ul>
                    </div>

                    <div class="highlight">
                        <h4>💡 Advanced Features Summary:</h4>
                        <ul>
                            <li><strong>Comprehensive Detection:</strong> SNVs, indels, and complex events</li>
                            <li><strong>High Accuracy:</strong> 75% validation rate for deletions</li>
                            <li><strong>Quality Control:</strong> Robust filtering for alignment artifacts</li>
                            <li><strong>Validation Strategy:</strong> Multiple orthogonal validation approaches</li>
                            <li><strong>Clinical Relevance:</strong> Detection of diverse mosaic variant types</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="impact" class="tab-content">
                <div class="section">
                    <h2>🌟 Applications and Impact</h2>
                    
                    <div class="highlight">
                        <h3>Transformative Impact on Mosaic Variant Research</h3>
                        <p>MosaicForecast's breakthrough performance opens new avenues for understanding somatic mosaicism in development, disease, and aging, with far-reaching implications for precision medicine and genetic research.</p>
                    </div>

                    <div class="svg-container">
                        <svg width="900" height="600" viewBox="0 0 900 600">
                            <!-- Background -->
                            <rect width="900" height="600" fill="#f8f9fa"/>
                            
                            <!-- Title -->
                            <text x="450" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">MosaicForecast Impact Areas</text>
                            
                            <!-- Research Applications -->
                            <rect x="50" y="80" width="400" height="200" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="10"/>
                            <text x="250" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#27ae60">Research Applications</text>
                            
                            <!-- Development research -->
                            <circle cx="100" cy="150" r="20" fill="#3498db"/>
                            <text x="100" y="155" text-anchor="middle" font-size="10" fill="white">Development</text>
                            <text x="100" y="170" text-anchor="middle" font-size="8" fill="#7f8c8d">Lineage tracing</text>
                            
                            <circle cx="200" cy="150" r="20" fill="#e74c3c"/>
                            <text x="200" y="155" text-anchor="middle" font-size="10" fill="white">Aging</text>
                            <text x="200" y="170" text-anchor="middle" font-size="8" fill="#7f8c8d">Somatic evolution</text>
                            
                            <circle cx="300" cy="150" r="20" fill="#f39c12"/>
                            <text x="300" y="155" text-anchor="middle" font-size="10" fill="white">Disease</text>
                            <text x="300" y="170" text-anchor="middle" font-size="8" fill="#7f8c8d">Etiology</text>
                            
                            <circle cx="400" cy="150" r="20" fill="#9b59b6"/>
                            <text x="400" y="155" text-anchor="middle" font-size="10" fill="white">Evolution</text>
                            <text x="400" y="170" text-anchor="middle" font-size="8" fill="#7f8c8d">Mutation rates</text>
                            
                            <!-- Research benefits -->
                            <text x="250" y="200" text-anchor="middle" font-size="12" fill="#2c3e50">• High-precision detection</text>
                            <text x="250" y="215" text-anchor="middle" font-size="12" fill="#2c3e50">• No control required</text>
                            <text x="250" y="230" text-anchor="middle" font-size="12" fill="#2c3e50">• Genome-wide coverage</text>
                            <text x="250" y="245" text-anchor="middle" font-size="12" fill="#2c3e50">• Low VAF sensitivity</text>
                            
                            <!-- Clinical Applications -->
                            <rect x="450" y="80" width="400" height="200" fill="#fef9e7" stroke="#f39c12" stroke-width="3" rx="10"/>
                            <text x="650" y="105" text-anchor="middle" font-size="18" font-weight="bold" fill="#f39c12">Clinical Applications</text>
                            
                            <!-- Clinical areas -->
                            <rect x="480" y="130" width="80" height="40" fill="#3498db" rx="5"/>
                            <text x="520" y="150" text-anchor="middle" font-size="10" fill="white">Neurology</text>
                            <text x="520" y="160" text-anchor="middle" font-size="8" fill="white">Brain disorders</text>
                            
                            <rect x="580" y="130" width="80" height="40" fill="#e74c3c" rx="5"/>
                            <text x="620" y="150" text-anchor="middle" font-size="10" fill="white">Oncology</text>
                            <text x="620" y="160" text-anchor="middle" font-size="8" fill="white">Cancer risk</text>
                            
                            <rect x="680" y="130" width="80" height="40" fill="#27ae60" rx="5"/>
                            <text x="720" y="150" text-anchor="middle" font-size="10" fill="white">Cardiology</text>
                            <text x="720" y="160" text-anchor="middle" font-size="8" fill="white">Heart disease</text>
                            
                            <rect x="780" y="130" width="80" height="40" fill="#9b59b6" rx="5"/>
                            <text x="820" y="150" text-anchor="middle" font-size="10" fill="white">Immunology</text>
                            <text x="820" y="160" text-anchor="middle" font-size="8" fill="white">Autoimmune</text>
                            
                            <!-- Clinical benefits -->
                            <text x="650" y="200" text-anchor="middle" font-size="12" fill="#2c3e50">• Early detection</text>
                            <text x="650" y="215" text-anchor="middle" font-size="12" fill="#2c3e50">• Risk assessment</text>
                            <text x="650" y="230" text-anchor="middle" font-size="12" fill="#2c3e50">• Treatment monitoring</text>
                            <text x="650" y="245" text-anchor="middle" font-size="12" fill="#2c3e50">• Precision medicine</text>
                            
                            <!-- Technology Impact -->
                            <rect x="50" y="320" width="400" height="200" fill="#fdf2f2" stroke="#e74c3c" stroke-width="3" rx="10"/>
                            <text x="250" y="345" text-anchor="middle" font-size="18" font-weight="bold" fill="#e74c3c">Technology Impact</text>
                            
                            <!-- Performance improvements -->
                            <text x="100" y="380" text-anchor="start" font-size="14" font-weight="bold" fill="#2c3e50">Performance Gains:</text>
                            <text x="100" y="400" text-anchor="start" font-size="12" fill="#7f8c8d">• 7-43x precision improvement</text>
                            <text x="100" y="415" text-anchor="start" font-size="12" fill="#7f8c8d">• 85% validation rate</text>
                            <text x="100" y="430" text-anchor="start" font-size="12" fill="#7f8c8d">• Genome-wide coverage</text>
                            <text x="100" y="445" text-anchor="start" font-size="12" fill="#7f8c8d">• No matched control needed</text>
                            
                            <!-- Methodological advances -->
                            <text x="100" y="470" text-anchor="start" font-size="14" font-weight="bold" fill="#2c3e50">Methodological Advances:</text>
                            <text x="100" y="490" text-anchor="start" font-size="12" fill="#7f8c8d">• Read-based phasing</text>
                            <text x="100" y="505" text-anchor="start" font-size="12" fill="#7f8c8d">• Machine learning integration</text>
                            <text x="100" y="520" text-anchor="start" font-size="12" fill="#7f8c8d">• Multi-variant type detection</text>
                            
                            <!-- Future Directions -->
                            <rect x="450" y="320" width="400" height="200" fill="#e8f4f8" stroke="#3498db" stroke-width="3" rx="10"/>
                            <text x="650" y="345" text-anchor="middle" font-size="18" font-weight="bold" fill="#3498db">Future Directions</text>
                            
                            <!-- Future applications -->
                            <text x="500" y="380" text-anchor="start" font-size="14" font-weight="bold" fill="#2c3e50">Emerging Applications:</text>
                            <text x="500" y="400" text-anchor="start" font-size="12" fill="#7f8c8d">• Liquid biopsy analysis</text>
                            <text x="500" y="415" text-anchor="start" font-size="12" fill="#7f8c8d">• Single-cell genomics</text>
                            <text x="500" y="430" text-anchor="start" font-size="12" fill="#7f8c8d">• Longitudinal monitoring</text>
                            <text x="500" y="445" text-anchor="start" font-size="12" fill="#7f8c8d">• Population studies</text>
                            
                            <!-- Technology evolution -->
                            <text x="500" y="470" text-anchor="start" font-size="14" font-weight="bold" fill="#2c3e50">Technology Evolution:</text>
                            <text x="500" y="490" text-anchor="start" font-size="12" fill="#7f8c8d">• Long-read integration</text>
                            <text x="500" y="505" text-anchor="start" font-size="12" fill="#7f8c8d">• Real-time analysis</text>
                            <text x="500" y="520" text-anchor="start" font-size="12" fill="#7f8c8d">• Clinical deployment</text>
                            
                            <!-- Impact metrics -->
                            <text x="450" y="560" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Transformative Impact</text>
                            
                            <!-- Impact bars -->
                            <rect x="100" y="580" width="150" height="15" fill="#27ae60"/>
                            <text x="110" y="590" text-anchor="start" font-size="10" fill="white">Research: New insights into somatic evolution</text>
                            
                            <rect x="100" y="600" width="180" height="15" fill="#f39c12"/>
                            <text x="110" y="610" text-anchor="start" font-size="10" fill="white">Clinical: Early disease detection and monitoring</text>
                            
                            <rect x="100" y="620" width="160" height="15" fill="#e74c3c"/>
                            <text x="110" y="630" text-anchor="start" font-size="10" fill="white">Technology: 7-43x performance improvement</text>
                        </svg>
                    </div>

                    <h3>Research Applications</h3>
                    <div class="method-box">
                        <h4>🔬 Fundamental Research Impact:</h4>
                        <ul>
                            <li><strong>Developmental Biology:</strong> Lineage tracing and cell fate mapping</li>
                            <li><strong>Aging Research:</strong> Somatic mutation accumulation and cellular senescence</li>
                            <li><strong>Disease Etiology:</strong> Understanding somatic contributions to complex diseases</li>
                            <li><strong>Evolutionary Biology:</strong> Mutation rate estimation and selection analysis</li>
                            <li><strong>Population Genetics:</strong> Somatic variation in human populations</li>
                        </ul>
                    </div>

                    <h3>Clinical Applications</h3>
                    <div class="formula">
                        <h4>🏥 Clinical Impact Areas:</h4>
                        <ul>
                            <li><strong>Neurological Disorders:</strong> Autism spectrum disorder, epilepsy, neurodegeneration</li>
                            <li><strong>Cancer Risk Assessment:</strong> Early detection of oncogenic mutations</li>
                            <li><strong>Cardiovascular Disease:</strong> Mosaic mutations in heart tissue</li>
                            <li><strong>Autoimmune Conditions:</strong> Somatic variation in immune cells</li>
                            <li><strong>Precision Medicine:</strong> Personalized treatment based on somatic profiles</li>
                        </ul>
                    </div>

                    <h3>Technology and Methodology Impact</h3>
                    <div class="results-box">
                        <h4>⚡ Technical Breakthroughs:</h4>
                        <ul>
                            <li><strong>No Control Requirement:</strong> Enables analysis of single samples</li>
                            <li><strong>High Precision:</strong> 7-43x improvement over existing methods</li>
                            <li><strong>Genome-Wide Coverage:</strong> Works in repeat and non-repeat regions</li>
                            <li><strong>Low VAF Detection:</strong> Sensitive to variants with 1-2% allele fraction</li>
                            <li><strong>Multi-Variant Types:</strong> SNVs, indels, and complex events</li>
                        </ul>
                    </div>

                    <h3>Future Directions and Opportunities</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Application Area</th>
                                <th>Current Status</th>
                                <th>Future Potential</th>
                                <th>Expected Impact</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Liquid Biopsy</strong></td>
                                <td>Limited by low sensitivity</td>
                                <td>High-precision ctDNA analysis</td>
                                <td>Early cancer detection</td>
                            </tr>
                            <tr>
                                <td><strong>Single-Cell Genomics</strong></td>
                                <td>Expensive and complex</td>
                                <td>Bulk sequencing with single-cell resolution</td>
                                <td>Cell lineage reconstruction</td>
                            </tr>
                            <tr>
                                <td><strong>Longitudinal Monitoring</strong></td>
                                <td>Not feasible</td>
                                <td>Tracking somatic evolution over time</td>
                                <td>Disease progression monitoring</td>
                            </tr>
                            <tr>
                                <td><strong>Population Studies</strong></td>
                                <td>Limited scale</td>
                                <td>Large-scale somatic variation surveys</td>
                                <td>Population genetics insights</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>Clinical Translation Challenges</h3>
                    <div class="highlight">
                        <h4>⚠️ Implementation Considerations:</h4>
                        <ul>
                            <li><strong>Validation Requirements:</strong> Clinical-grade validation for diagnostic use</li>
                            <li><strong>Cost Considerations:</strong> High-depth sequencing requirements</li>
                            <li><strong>Interpretation Complexity:</strong> Clinical significance of mosaic variants</li>
                            <li><strong>Regulatory Approval:</strong> FDA/CE marking for clinical use</li>
                            <li><strong>Standardization:</strong> Clinical reporting standards and guidelines</li>
                        </ul>
                    </div>

                    <h3>Societal and Ethical Implications</h3>
                    <div class="method-box">
                        <h4>🌍 Broader Impact:</h4>
                        <ul>
                            <li><strong>Privacy Concerns:</strong> Somatic mutation data and genetic privacy</li>
                            <li><strong>Insurance Implications:</strong> Risk assessment and coverage decisions</li>
                            <li><strong>Informed Consent:</strong> Complex genetic information disclosure</li>
                            <li><strong>Data Sharing:</strong> Research collaboration and data protection</li>
                            <li><strong>Equity Issues:</strong> Access to advanced genetic testing</li>
                        </ul>
                    </div>

                    <h3>Economic and Commercial Impact</h3>
                    <div class="results-box">
                        <h4>💰 Commercial Opportunities:</h4>
                        <ul>
                            <li><strong>Diagnostic Services:</strong> Clinical testing for mosaic variants</li>
                            <li><strong>Research Tools:</strong> Software and analysis platforms</li>
                            <li><strong>Pharmaceutical Development:</strong> Drug target identification</li>
                            <li><strong>Precision Medicine:</strong> Personalized treatment strategies</li>
                            <li><strong>Biotechnology:</strong> Novel therapeutic approaches</li>
                        </ul>
                    </div>

                    <div class="highlight">
                        <h4>🎯 Summary of Impact:</h4>
                        <ul>
                            <li><strong>Scientific Advancement:</strong> New insights into somatic evolution and disease</li>
                            <li><strong>Clinical Innovation:</strong> Early detection and precision medicine applications</li>
                            <li><strong>Technical Breakthrough:</strong> 7-43x performance improvement over existing methods</li>
                            <li><strong>Methodological Innovation:</strong> Novel approach combining phasing and machine learning</li>
                            <li><strong>Future Potential:</strong> Foundation for next-generation genetic analysis</li>
                        </ul>
                    </div>

                    <div class="method-box">
                        <h4>🚀 Conclusion:</h4>
                        <p>MosaicForecast represents a paradigm shift in mosaic variant detection, enabling high-precision analysis without requiring matched controls. This breakthrough opens new frontiers in understanding somatic mosaicism's role in development, disease, and aging, with transformative implications for both research and clinical applications.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.getElementsByClassName('tab-content');
            for (let content of tabContents) {
                content.classList.remove('active');
            }
            
            // Remove active class from all tabs
            const tabs = document.getElementsByClassName('nav-tab');
            for (let tab of tabs) {
                tab.classList.remove('active');
            }
            
            // Show selected tab content and activate tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</body>
</html> 