<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Landmarks of Human Embryonic Development: Somatic Mutations as Endogenous Barcodes</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .method-box {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .method-box h4 {
            color: #0d47a1;
            margin-bottom: 15px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Landmarks of Human Embryonic Development</h1>
            <p>Using Somatic Mutations as Endogenous Barcodes to Trace Cell Lineages</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction and Study Overview</a></li>
                <li><a href="#experimental-design">2. Experimental Design and Methods</a></li>
                <li><a href="#snv-detection">3. Somatic SNV Detection Pipeline</a></li>
                <li><a href="#lineage-reconstruction">4. Lineage Tree Reconstruction</a></li>
                <li><a href="#embryonic-insights">5. Early Embryonic Development Insights</a></li>
                <li><a href="#brain-development">6. Brain Development and Founder Cells</a></li>
                <li><a href="#single-cell-analysis">7. Single-Cell Analysis Integration</a></li>
                <li><a href="#implications">8. Biological Implications and Future Directions</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction and Study Overview -->
        <div id="introduction" class="section">
            <h2>1. Introduction and Study Overview</h2>

            <div class="highlight">
                <strong>Revolutionary Approach:</strong> This groundbreaking study uses naturally occurring somatic mutations as "endogenous barcodes" to reconstruct human embryonic development with unprecedented resolution, revealing the cellular history inscribed in our DNA.
            </div>

            <h3>The Challenge of Human Lineage Tracing</h3>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        From Somatic Mutations to Developmental History
                    </text>

                    <!-- Embryonic development timeline -->
                    <g transform="translate(50, 60)">
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Human Embryonic Development Timeline
                        </text>

                        <!-- Timeline line -->
                        <line x1="50" y1="50" x2="650" y2="50" stroke="#2c3e50" stroke-width="3"/>

                        <!-- Zygote -->
                        <g transform="translate(50, 30)">
                            <circle cx="0" cy="20" r="15" fill="#3498db"/>
                            <text x="0" y="25" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Zygote</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Day 0</text>
                        </g>

                        <!-- 2-cell -->
                        <g transform="translate(150, 30)">
                            <circle cx="-8" cy="20" r="12" fill="#3498db"/>
                            <circle cx="8" cy="20" r="12" fill="#3498db"/>
                            <text x="-8" y="25" text-anchor="middle" font-size="8" fill="white">1</text>
                            <text x="8" y="25" text-anchor="middle" font-size="8" fill="white">2</text>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">2-cell</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Day 1</text>
                        </g>

                        <!-- 4-cell -->
                        <g transform="translate(250, 30)">
                            <circle cx="-8" cy="12" r="10" fill="#3498db"/>
                            <circle cx="8" cy="12" r="10" fill="#3498db"/>
                            <circle cx="-8" cy="28" r="10" fill="#3498db"/>
                            <circle cx="8" cy="28" r="10" fill="#3498db"/>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">4-cell</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Day 2</text>
                        </g>

                        <!-- Blastocyst -->
                        <g transform="translate(350, 30)">
                            <circle cx="0" cy="20" r="18" fill="#f39c12" opacity="0.3" stroke="#f39c12" stroke-width="2"/>
                            <circle cx="-5" cy="15" r="4" fill="#3498db"/>
                            <circle cx="5" cy="15" r="4" fill="#3498db"/>
                            <circle cx="0" cy="25" r="4" fill="#3498db"/>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Blastocyst</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Day 5-6</text>
                        </g>

                        <!-- Gastrulation -->
                        <g transform="translate(450, 30)">
                            <ellipse cx="0" cy="20" rx="20" ry="15" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2"/>
                            <text x="0" y="25" text-anchor="middle" font-size="8" fill="#2c3e50">~170 cells</text>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Gastrulation</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Day 14-21</text>
                        </g>

                        <!-- Organogenesis -->
                        <g transform="translate(550, 30)">
                            <rect x="-15" y="5" width="30" height="30" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2" rx="5"/>
                            <text x="0" y="25" text-anchor="middle" font-size="8" fill="#2c3e50">Organs</text>
                            <text x="0" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Organogenesis</text>
                            <text x="0" y="65" text-anchor="middle" font-size="9" fill="#666">Week 3-8</text>
                        </g>

                        <!-- Mutation accumulation -->
                        <g transform="translate(50, 100)">
                            <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                                Somatic Mutations as Endogenous Barcodes
                            </text>

                            <!-- Mutation markers -->
                            <g transform="translate(0, 40)">
                                <circle cx="100" cy="0" r="3" fill="#e74c3c"/>
                                <text x="100" y="20" text-anchor="middle" font-size="8" fill="#e74c3c">Mutation 1</text>

                                <circle cx="200" cy="0" r="3" fill="#e74c3c"/>
                                <text x="200" y="20" text-anchor="middle" font-size="8" fill="#e74c3c">Mutation 2</text>

                                <circle cx="300" cy="0" r="3" fill="#e74c3c"/>
                                <text x="300" y="20" text-anchor="middle" font-size="8" fill="#e74c3c">Mutation 3</text>

                                <circle cx="400" cy="0" r="3" fill="#e74c3c"/>
                                <text x="400" y="20" text-anchor="middle" font-size="8" fill="#e74c3c">Mutation 4</text>

                                <circle cx="500" cy="0" r="3" fill="#e74c3c"/>
                                <text x="500" y="20" text-anchor="middle" font-size="8" fill="#e74c3c">Mutation 5</text>
                            </g>
                        </g>
                    </g>

                    <!-- Study approach -->
                    <g transform="translate(100, 250)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Study Approach: Multi-Scale Analysis
                        </text>

                        <!-- Bulk sequencing -->
                        <g transform="translate(0, 40)">
                            <rect width="140" height="80" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Bulk Sequencing</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">250× WGS</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">17 organs</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">516 sSNVs</text>
                        </g>

                        <!-- Targeted sequencing -->
                        <g transform="translate(160, 40)">
                            <rect width="140" height="80" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Targeted Seq</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">25,000× depth</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">94 samples</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Validation</text>
                        </g>

                        <!-- Single-cell -->
                        <g transform="translate(320, 40)">
                            <rect width="140" height="80" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Single-Cell</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">1,228 cells</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Lineage trees</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Cell types</text>
                        </g>

                        <!-- Multi-omics -->
                        <g transform="translate(480, 40)">
                            <rect width="140" height="80" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Multi-omics</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">snRNA-seq</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">snATAC-seq</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">~100K cells</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Key Innovation: Somatic Mutations as Natural Barcodes</h3>

            <div class="info-box">
                <h4>Why Somatic Mutations Work as Lineage Tracers</h4>
                <ul class="step-list">
                    <li><strong>Permanent Markers:</strong> Once acquired, somatic mutations are inherited by all descendant cells</li>
                    <li><strong>Temporal Information:</strong> Earlier mutations appear in more cells (higher mosaic fraction)</li>
                    <li><strong>Spatial Information:</strong> Mutation patterns reveal tissue-specific lineages</li>
                    <li><strong>Unbiased Sampling:</strong> Most mutations are functionally neutral, avoiding selection bias</li>
                </ul>
            </div>

            <h3>Study Participants and Scope</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Individual</th>
                        <th>Age/Sex</th>
                        <th>Samples Analyzed</th>
                        <th>Key Findings</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>UMB1465</strong></td>
                        <td>17-year-old male</td>
                        <td>5 bulk WGS + 94 targeted samples from 17 organs</td>
                        <td>Complete lineage reconstruction, 297 sSNVs</td>
                    </tr>
                    <tr>
                        <td><strong>UMB4638</strong></td>
                        <td>15-year-old female</td>
                        <td>PFC + visual cortex samples</td>
                        <td>Brain-specific lineage patterns</td>
                    </tr>
                    <tr>
                        <td><strong>UMB4643</strong></td>
                        <td>42-year-old female</td>
                        <td>PFC + visual cortex samples</td>
                        <td>Adult brain lineage validation</td>
                    </tr>
                </tbody>
            </table>

            <h3>Revolutionary Findings Preview</h3>

            <div class="warning-box">
                <h4>Major Discoveries</h4>
                <ul>
                    <li><strong>Asymmetric Development:</strong> Early embryonic clones contribute unequally to different organs</li>
                    <li><strong>Gastrulation Timing:</strong> Effective progenitor pool of ~170 cells at gastrulation onset</li>
                    <li><strong>Brain Founders:</strong> ~50-100 founder cells establish the forebrain</li>
                    <li><strong>Cortical Organization:</strong> Progressive restriction of clones from frontal to posterior regions</li>
                    <li><strong>Stochastic Patterns:</strong> High variability in clonal amplification within tissues</li>
                </ul>
            </div>
        </div>

        <!-- Section 2: Experimental Design and Methods -->
        <div id="experimental-design" class="section">
            <h2>2. Experimental Design and Methods</h2>

            <div class="highlight">
                <strong>Multi-Scale Strategy:</strong> The study combines high-depth bulk sequencing, targeted validation, and single-cell analysis to achieve unprecedented resolution in human lineage tracing.
            </div>

            <h3>Sample Collection and Processing</h3>

            <div class="method-box">
                <h4>Tissue Sampling Protocol</h4>
                <ul class="step-list">
                    <li><strong>Source:</strong> Postmortem tissues from NIH Neurobiobank (no medical diagnosis)</li>
                    <li><strong>Preservation:</strong> Frozen at -80°C to maintain DNA integrity</li>
                    <li><strong>Sampling:</strong> Multiple 5mm³ pieces (~50-100mg) per organ/region</li>
                    <li><strong>DNA Extraction:</strong> Phenol-chloroform method with ethanol precipitation</li>
                </ul>
            </div>

            <h3>Sequencing Strategy</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Three-Tier Sequencing Approach
                    </text>

                    <!-- Tier 1: Bulk WGS -->
                    <g transform="translate(50, 60)">
                        <rect width="200" height="100" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Tier 1: Bulk WGS</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">250× coverage</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">5 samples (UMB1465)</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Discovery phase</text>
                        <text x="100" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">516 total sSNVs</text>
                    </g>

                    <!-- Tier 2: Targeted sequencing -->
                    <g transform="translate(300, 60)">
                        <rect width="200" height="100" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">Tier 2: Targeted Seq</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">25,000× coverage</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">94 samples, 17 organs</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Validation phase</text>
                        <text x="100" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">86% validation rate</text>
                    </g>

                    <!-- Tier 3: Single-cell -->
                    <g transform="translate(550, 60)">
                        <rect width="200" height="100" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Tier 3: Single-Cell</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">1,228 cells</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Targeted amplicons</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Lineage trees</text>
                        <text x="100" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">791 cells assigned</text>
                    </g>

                    <!-- Workflow arrows -->
                    <g>
                        <defs>
                            <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="250" y1="110" x2="300" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="500" y1="110" x2="550" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                    </g>

                    <!-- Technical details -->
                    <g transform="translate(100, 200)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Technical Specifications
                        </text>

                        <!-- Coverage comparison -->
                        <g transform="translate(0, 40)">
                            <rect width="150" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Coverage Depth</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">WGS: 250×</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Targeted: 25,000×</text>
                        </g>

                        <!-- Sensitivity -->
                        <g transform="translate(175, 40)">
                            <rect width="150" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Sensitivity</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">3-30% AAF: ~100%</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">1-3% AAF: ~80%</text>
                        </g>

                        <!-- Platform -->
                        <g transform="translate(350, 40)">
                            <rect width="150" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Platform</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Illumina HiSeqX</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Paired-end reads</text>
                        </g>

                        <!-- Quality control -->
                        <g transform="translate(525, 40)">
                            <rect width="150" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Quality Control</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">14 negative controls</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Technical triplicates</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Multi-Omics Integration</h3>

            <div class="info-box">
                <h4>Single-Cell Multi-Omics Approach</h4>
                <ul>
                    <li><strong>snRNA-seq:</strong> ~34,000 cells for transcriptional profiling</li>
                    <li><strong>snATAC-seq:</strong> ~65,000 cells for chromatin accessibility</li>
                    <li><strong>Targeted DNA-seq:</strong> 1,228 cells for lineage tracing</li>
                    <li><strong>Integration:</strong> Linking cell types with developmental lineages</li>
                </ul>
            </div>
        </div>

        <!-- Section 3: Somatic SNV Detection Pipeline -->
        <div id="snv-detection" class="section">
            <h2>3. Somatic SNV Detection Pipeline</h2>

            <div class="highlight">
                <strong>MosaicForecast Algorithm:</strong> Machine learning-powered detection of low-frequency somatic mutations with high precision and sensitivity.
            </div>

            <h3>Detection Workflow</h3>

            <div class="method-box">
                <h4>Step-by-Step Pipeline</h4>
                <ul class="step-list">
                    <li><strong>Alignment:</strong> BWA-MEM to GRCh37/hg19 reference genome</li>
                    <li><strong>Raw Calling:</strong> MuTect2 with panel-of-normals (72 individuals)</li>
                    <li><strong>Filtering:</strong> Remove common variants, segmental duplications, high AAF (>40%)</li>
                    <li><strong>ML Classification:</strong> MosaicForecast brain-trained model</li>
                    <li><strong>Validation:</strong> Single-cell lineage tree construction</li>
                    <li><strong>Final Set:</strong> 297 high-confidence sSNVs for UMB1465</li>
                </ul>
            </div>

            <h3>Mutation Characteristics</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Property</th>
                        <th>Observation</th>
                        <th>Biological Significance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Base Substitution</strong></td>
                        <td>55% C>T transitions</td>
                        <td>Clock-like Signature 1 (cytosine deamination)</td>
                    </tr>
                    <tr>
                        <td><strong>Functional Impact</strong></td>
                        <td>Only 2/297 exonic</td>
                        <td>Unbiased lineage markers (neutral evolution)</td>
                    </tr>
                    <tr>
                        <td><strong>Tissue Distribution</strong></td>
                        <td>22% in all tissues, 61% in ≥2 tissues</td>
                        <td>Early embryonic origin</td>
                    </tr>
                    <tr>
                        <td><strong>AAF Range</strong></td>
                        <td>1-30% for shared variants</td>
                        <td>Post-zygotic timing information</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 4: Lineage Tree Reconstruction -->
        <div id="lineage-reconstruction" class="section">
            <h2>4. Lineage Tree Reconstruction</h2>

            <div class="highlight">
                <strong>Phylogenetic Approach:</strong> Single-cell genotyping of somatic mutations enables reconstruction of embryonic cell division history with unprecedented detail.
            </div>

            <h3>Tree Building Methodology</h3>

            <div class="method-box">
                <h4>Lineage Tree Construction</h4>
                <ul class="step-list">
                    <li><strong>Single-Cell Genotyping:</strong> 20 neurons from published data + 1,228 new cells</li>
                    <li><strong>Mutation Calling:</strong> Bayesian approach with beta-distribution modeling</li>
                    <li><strong>Tree Building:</strong> Hierarchical clustering based on shared mutations</li>
                    <li><strong>Validation:</strong> Mosaic fraction consistency across clades</li>
                </ul>
            </div>

            <h3>Key Findings from Lineage Trees</h3>

            <div class="warning-box">
                <h4>Asymmetric Development Patterns</h4>
                <ul>
                    <li><strong>First Generation:</strong> Highly variable contributions (20:80 to 50:50 ratios)</li>
                    <li><strong>Third Generation:</strong> 8 major clones (c1-c8) with unequal organ contributions</li>
                    <li><strong>Mosaic Fractions:</strong> Deviate from expected 2-fold reduction per division</li>
                    <li><strong>Tissue Specificity:</strong> Some clones absent from specific organs</li>
                </ul>
            </div>
        </div>

        <!-- Section 5: Early Embryonic Development Insights -->
        <div id="embryonic-insights" class="section">
            <h2>5. Early Embryonic Development Insights</h2>

            <div class="highlight">
                <strong>Gastrulation Timing:</strong> The study provides the first direct evidence for the effective progenitor pool size (~170 cells) at the onset of human gastrulation.
            </div>

            <h3>Asymmetric Clonal Contributions</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Asymmetric Development: From Symmetry to Complexity
                    </text>

                    <!-- Expected vs Observed -->
                    <g transform="translate(100, 60)">
                        <!-- Expected symmetric -->
                        <g transform="translate(0, 0)">
                            <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Expected (Symmetric)</text>

                            <!-- Generation 1 -->
                            <g transform="translate(50, 40)">
                                <circle cx="0" cy="0" r="15" fill="#3498db"/>
                                <text x="0" y="5" text-anchor="middle" font-size="10" fill="white">50%</text>
                                <circle cx="100" cy="0" r="15" fill="#3498db"/>
                                <text x="100" y="5" text-anchor="middle" font-size="10" fill="white">50%</text>
                                <text x="50" y="30" text-anchor="middle" font-size="10" fill="#2c3e50">Generation 1</text>
                            </g>

                            <!-- Generation 2 -->
                            <g transform="translate(25, 80)">
                                <circle cx="0" cy="0" r="12" fill="#3498db"/>
                                <text x="0" y="4" text-anchor="middle" font-size="8" fill="white">25%</text>
                                <circle cx="50" cy="0" r="12" fill="#3498db"/>
                                <text x="50" y="4" text-anchor="middle" font-size="8" fill="white">25%</text>
                                <circle cx="100" cy="0" r="12" fill="#3498db"/>
                                <text x="100" y="4" text-anchor="middle" font-size="8" fill="white">25%</text>
                                <circle cx="150" cy="0" r="12" fill="#3498db"/>
                                <text x="150" y="4" text-anchor="middle" font-size="8" fill="white">25%</text>
                                <text x="75" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">Generation 2</text>
                            </g>
                        </g>

                        <!-- Observed asymmetric -->
                        <g transform="translate(400, 0)">
                            <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Observed (Asymmetric)</text>

                            <!-- Generation 1 -->
                            <g transform="translate(30, 40)">
                                <circle cx="0" cy="0" r="20" fill="#e74c3c"/>
                                <text x="0" y="5" text-anchor="middle" font-size="10" fill="white">70%</text>
                                <circle cx="120" cy="0" r="10" fill="#e74c3c"/>
                                <text x="120" y="4" text-anchor="middle" font-size="8" fill="white">30%</text>
                                <text x="60" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Generation 1</text>
                            </g>

                            <!-- Generation 2 -->
                            <g transform="translate(0, 80)">
                                <circle cx="0" cy="0" r="18" fill="#e74c3c"/>
                                <text x="0" y="5" text-anchor="middle" font-size="9" fill="white">40%</text>
                                <circle cx="60" cy="0" r="14" fill="#e74c3c"/>
                                <text x="60" y="4" text-anchor="middle" font-size="8" fill="white">30%</text>
                                <circle cx="110" cy="0" r="8" fill="#e74c3c"/>
                                <text x="110" y="3" text-anchor="middle" font-size="7" fill="white">15%</text>
                                <circle cx="140" cy="0" r="8" fill="#e74c3c"/>
                                <text x="140" y="3" text-anchor="middle" font-size="7" fill="white">15%</text>
                                <text x="70" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">Generation 2</text>
                            </g>
                        </g>
                    </g>

                    <!-- Germ layer contributions -->
                    <g transform="translate(100, 200)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Germ Layer Contributions
                        </text>

                        <!-- Ectoderm -->
                        <g transform="translate(0, 40)">
                            <rect width="150" height="80" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Ectoderm</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Brain, Skin</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Variable clonal</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">contributions</text>
                        </g>

                        <!-- Mesoderm -->
                        <g transform="translate(175, 40)">
                            <rect width="150" height="80" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Mesoderm</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Heart, Muscle</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Up to several-fold</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">differences</text>
                        </g>

                        <!-- Endoderm -->
                        <g transform="translate(350, 40)">
                            <rect width="150" height="80" fill="#f39c12" opacity="0.2" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Endoderm</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Liver, Pancreas</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Asymmetric</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">segregation</text>
                        </g>

                        <!-- Gastrulation marker -->
                        <g transform="translate(525, 40)">
                            <rect width="150" height="80" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Gastrulation</text>
                            <text x="75" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">~170 cells</text>
                            <text x="75" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">0.6% MF threshold</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Day 14-21</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Quantitative Estimates</h3>

            <div class="info-box">
                <h4>Key Developmental Parameters</h4>
                <ul>
                    <li><strong>Gastrulation Onset:</strong> ~170 epiblast cells (0.6% MF threshold)</li>
                    <li><strong>First Generation Asymmetry:</strong> 20:80 to 50:50 ratios across individuals</li>
                    <li><strong>Clonal Amplification:</strong> Highly stochastic within tissues</li>
                    <li><strong>Extraembryonic Segregation:</strong> As early as 2-4 cell stage</li>
                </ul>
            </div>
        </div>

        <!-- Section 6: Brain Development and Founder Cells -->
        <div id="brain-development" class="section">
            <h2>6. Brain Development and Founder Cells</h2>

            <div class="highlight">
                <strong>Forebrain Founders:</strong> Analysis reveals ~50-100 founder cells establish the forebrain, with progressive spatial restriction during cortical development.
            </div>

            <h3>CNS-Specific Lineage Analysis</h3>

            <div class="method-box">
                <h4>Brain Founder Cell Estimation</h4>
                <ul class="step-list">
                    <li><strong>CNS-Specific sSNVs:</strong> 14 variants present in brain but not other organs</li>
                    <li><strong>Forebrain Enrichment:</strong> 10 variants significantly enriched in forebrain</li>
                    <li><strong>Single-Cell Validation:</strong> 1,228 cortical cells confirm early lineage markers</li>
                    <li><strong>Founder Estimate:</strong> ~50-100 cells based on highest MF variants (~2.2%)</li>
                </ul>
            </div>

            <h3>Cortical Organization Patterns</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Cell Generation</th>
                        <th>Distribution Pattern</th>
                        <th>Spatial Restriction</th>
                        <th>Biological Significance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>1st-4th</strong></td>
                        <td>All rostro-caudal sections</td>
                        <td>None</td>
                        <td>Early cortical founders</td>
                    </tr>
                    <tr>
                        <td><strong>5th+</strong></td>
                        <td>Progressive frontal restriction</td>
                        <td>Moderate</td>
                        <td>Regional specification</td>
                    </tr>
                    <tr>
                        <td><strong>6th+</strong></td>
                        <td>Prefrontal cortex only</td>
                        <td>High</td>
                        <td>Local amplification</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 7: Single-Cell Analysis Integration -->
        <div id="single-cell-analysis" class="section">
            <h2>7. Single-Cell Analysis Integration</h2>

            <div class="highlight">
                <strong>Multi-Modal Integration:</strong> Combining DNA lineage tracing with RNA-seq and ATAC-seq reveals cell-type-specific developmental patterns.
            </div>

            <h3>Technical Challenges and Solutions</h3>

            <div class="warning-box">
                <h4>Single-Cell Limitations</h4>
                <ul>
                    <li><strong>Coverage Sparsity:</strong> Only 5.6% snRNA-seq and 12.8% snATAC-seq cells covered sSNV sites</li>
                    <li><strong>Allele Dropout:</strong> MDA amplification bias requires beta-distribution modeling</li>
                    <li><strong>Late Variants:</strong> Low-frequency mutations difficult to detect reliably</li>
                    <li><strong>Cell Type Assignment:</strong> Limited by mutation coverage in individual cells</li>
                </ul>
            </div>

            <h3>Cell Type Classification</h3>

            <div class="info-box">
                <h4>Seven Major Brain Cell Types Identified</h4>
                <ul>
                    <li><strong>Excitatory Neurons:</strong> Multiple subtypes with distinct lineage contributions</li>
                    <li><strong>Inhibitory Neurons:</strong> GABAergic interneurons</li>
                    <li><strong>Astrocytes:</strong> Glial support cells</li>
                    <li><strong>Oligodendrocytes:</strong> Myelinating cells</li>
                    <li><strong>Microglia:</strong> Immune cells</li>
                    <li><strong>Endothelial Cells:</strong> Vascular components</li>
                    <li><strong>Other Glia:</strong> Additional support cell types</li>
                </ul>
            </div>
        </div>

        <!-- Section 8: Biological Implications and Future Directions -->
        <div id="implications" class="section">
            <h2>8. Biological Implications and Future Directions</h2>

            <div class="highlight">
                <strong>Paradigm Shift:</strong> This work establishes somatic mutations as a powerful tool for understanding human development and disease, opening new avenues for developmental biology research.
            </div>

            <h3>Major Biological Insights</h3>

            <div class="info-box">
                <h4>Revolutionary Discoveries</h4>
                <ul class="step-list">
                    <li><strong>Early Asymmetry:</strong> Human development is asymmetric from the earliest cell divisions</li>
                    <li><strong>Stochastic Amplification:</strong> High variability in clonal expansion within all tissues</li>
                    <li><strong>Quantitative Estimates:</strong> First direct measurements of human developmental parameters</li>
                    <li><strong>Spatial Organization:</strong> Progressive restriction of lineages during organogenesis</li>
                </ul>
            </div>

            <h3>Clinical and Research Applications</h3>

            <div class="method-box">
                <h4>Future Directions</h4>
                <ul>
                    <li><strong>Disease Studies:</strong> Understanding somatic mosaicism in neurological disorders</li>
                    <li><strong>Cancer Research:</strong> Tracing clonal evolution in tumor development</li>
                    <li><strong>Aging Studies:</strong> Investigating clonal dynamics throughout lifespan</li>
                    <li><strong>Therapeutic Applications:</strong> Informing cell therapy and regenerative medicine</li>
                    <li><strong>Evolutionary Biology:</strong> Comparing developmental patterns across species</li>
                </ul>
            </div>

            <h3>Technical Advances Enabled</h3>

            <div class="warning-box">
                <h4>Methodological Contributions</h4>
                <ul>
                    <li><strong>MosaicForecast:</strong> Machine learning for mosaic variant detection</li>
                    <li><strong>Multi-Scale Integration:</strong> Combining bulk and single-cell approaches</li>
                    <li><strong>Lineage Tree Methods:</strong> Phylogenetic reconstruction from somatic mutations</li>
                    <li><strong>Quantitative Framework:</strong> Statistical methods for developmental parameter estimation</li>
                </ul>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
