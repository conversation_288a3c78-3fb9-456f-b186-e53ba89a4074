<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Haplotype Phasing: Methods and Developments</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .outline {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .outline h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .outline li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .outline li:last-child {
            border-bottom: none;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .performance-metric {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-weight: bold;
        }
        
        .performance-metric.good {
            background: #27ae60;
        }
        
        .performance-metric.medium {
            background: #f39c12;
        }
        
        .nav-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .nav-button {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
        
        .haplotype {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .chromosome {
            stroke: #34495e;
            stroke-width: 3;
            fill: none;
        }
        
        .snp-marker {
            fill: #e74c3c;
            stroke: #c0392b;
            stroke-width: 1;
        }
        
        .phase-line {
            stroke: #27ae60;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Haplotype Phasing: Methods and Developments</h1>
            <p>Computational and Experimental Approaches for Determining Chromosome Phase</p>
        </div>
        
        <div class="outline">
            <h2>📋 Presentation Outline</h2>
            <ul>
                <li><strong>1. Introduction & Fundamentals</strong> - What is haplotype phasing and why it matters</li>
                <li><strong>2. Computational Methods for Unrelated Individuals</strong> - Statistical approaches and algorithms</li>
                <li><strong>3. Family-Based Phasing</strong> - Using identity-by-descent and Mendelian constraints</li>
                <li><strong>4. Advanced Statistical Models</strong> - HMM and coalescent-based methods</li>
                <li><strong>5. Identity-by-Descent Approaches</strong> - Long-range phasing and population structure</li>
                <li><strong>6. Experimental Phasing Methods</strong> - Laboratory-based chromosome separation techniques</li>
                <li><strong>7. Performance Evaluation</strong> - Accuracy metrics and method comparisons</li>
                <li><strong>8. Applications & Future Directions</strong> - Clinical applications and emerging technologies</li>
            </ul>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-button" onclick="showSection(1)">Start Presentation</button>
        </div>

        <!-- Section 1: Introduction & Fundamentals -->
        <div id="section1" class="section hidden">
            <h2>1. Introduction & Fundamentals</h2>

            <div class="highlight">
                <strong>Core Challenge:</strong> Determining which alleles are co-located on the same chromosome (haplotype phase) from unphased genotype data, enabling applications from disease association to population genetics.
            </div>

            <h3>What is Haplotype Phasing?</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        From Unphased Genotypes to Phased Haplotypes
                    </text>

                    <!-- Unphased genotype data -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Unphased Genotype Data
                        </text>

                        <!-- SNP positions -->
                        <g transform="translate(0, 40)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">SNP1:</text>
                            <text x="50" y="15" font-size="12" fill="#e74c3c">A/T</text>
                            <text x="0" y="35" font-size="12" fill="#2c3e50">SNP2:</text>
                            <text x="50" y="35" font-size="12" fill="#e74c3c">G/C</text>
                            <text x="0" y="55" font-size="12" fill="#2c3e50">SNP3:</text>
                            <text x="50" y="55" font-size="12" fill="#e74c3c">T/A</text>
                            <text x="0" y="75" font-size="12" fill="#2c3e50">SNP4:</text>
                            <text x="50" y="75" font-size="12" fill="#e74c3c">C/G</text>
                        </g>

                        <text x="150" y="140" text-anchor="middle" font-size="11" fill="#666">
                            Which alleles are on the same chromosome?
                        </text>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(350, 150)">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">PHASING</text>
                    </g>

                    <!-- Phased haplotype data -->
                    <g transform="translate(500, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Phased Haplotype Data
                        </text>

                        <!-- Maternal haplotype -->
                        <g transform="translate(20, 40)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#3498db">Maternal:</text>
                            <line x1="70" y1="10" x2="200" y2="10" class="chromosome"/>
                            <circle cx="90" cy="10" r="4" class="snp-marker"/>
                            <text x="90" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">A</text>
                            <circle cx="120" cy="10" r="4" class="snp-marker"/>
                            <text x="120" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">G</text>
                            <circle cx="150" cy="10" r="4" class="snp-marker"/>
                            <text x="150" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">T</text>
                            <circle cx="180" cy="10" r="4" class="snp-marker"/>
                            <text x="180" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">C</text>
                        </g>

                        <!-- Paternal haplotype -->
                        <g transform="translate(20, 80)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#e74c3c">Paternal:</text>
                            <line x1="70" y1="10" x2="200" y2="10" class="chromosome"/>
                            <circle cx="90" cy="10" r="4" class="snp-marker"/>
                            <text x="90" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">T</text>
                            <circle cx="120" cy="10" r="4" class="snp-marker"/>
                            <text x="120" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">C</text>
                            <circle cx="150" cy="10" r="4" class="snp-marker"/>
                            <text x="150" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">A</text>
                            <circle cx="180" cy="10" r="4" class="snp-marker"/>
                            <text x="180" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">G</text>
                        </g>

                        <text x="150" y="140" text-anchor="middle" font-size="11" fill="#666">
                            Alleles assigned to specific chromosomes
                        </text>
                    </g>

                    <!-- Ambiguity illustration -->
                    <g transform="translate(100, 220)">
                        <rect width="600" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Phasing Ambiguity: Multiple Possible Configurations
                        </text>

                        <!-- Configuration A -->
                        <g transform="translate(50, 50)">
                            <text x="100" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Configuration A</text>
                            <text x="0" y="35" font-size="11" fill="#2c3e50">Hap1: A-G-T-C</text>
                            <text x="0" y="50" font-size="11" fill="#2c3e50">Hap2: T-C-A-G</text>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#27ae60">Probability: 85%</text>
                        </g>

                        <!-- Configuration B -->
                        <g transform="translate(250, 50)">
                            <text x="100" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Configuration B</text>
                            <text x="0" y="35" font-size="11" fill="#2c3e50">Hap1: A-C-T-C</text>
                            <text x="0" y="50" font-size="11" fill="#2c3e50">Hap2: T-G-A-G</text>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#f39c12">Probability: 12%</text>
                        </g>

                        <!-- Configuration C -->
                        <g transform="translate(450, 50)">
                            <text x="100" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Configuration C</text>
                            <text x="0" y="35" font-size="11" fill="#2c3e50">Hap1: A-G-A-G</text>
                            <text x="0" y="50" font-size="11" fill="#2c3e50">Hap2: T-C-T-C</text>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#e74c3c">Probability: 3%</text>
                        </g>

                        <text x="300" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">
                            Statistical methods estimate probabilities of different phase configurations
                        </text>
                    </g>
                </svg>
            </div>

            <h3>Why Haplotype Phasing Matters</h3>

            <div class="algorithm-box">
                <h4>Critical Applications</h4>
                <div class="step">
                    <strong>Disease Association:</strong> Haplotypes may show stronger disease associations than individual SNPs
                </div>
                <div class="step">
                    <strong>Imputation:</strong> Phased reference panels enable accurate imputation of untyped variants
                </div>
                <div class="step">
                    <strong>Population Genetics:</strong> Understanding demographic history, selection signatures, and recombination
                </div>
                <div class="step">
                    <strong>Pharmacogenomics:</strong> Drug response may depend on specific haplotype combinations
                </div>
                <div class="step">
                    <strong>Rare Disease Analysis:</strong> Distinguishing compound heterozygosity from cis variants
                </div>
            </div>

            <h3>The Scale Challenge</h3>

            <div class="math-box">
                <h4>Computational Complexity</h4>
                <p><strong>Number of possible phase configurations:</strong></p>

                <p>For an individual heterozygous at n SNPs:</p>
                $$\text{Configurations} = 2^{n-1}$$

                <p><strong>Examples:</strong></p>
                <ul>
                    <li>10 heterozygous SNPs: 512 configurations</li>
                    <li>20 heterozygous SNPs: 524,288 configurations</li>
                    <li>100 heterozygous SNPs: ~6.3 × 10²⁹ configurations</li>
                </ul>

                <p><strong>Hardy-Weinberg Principle for haplotype pairs:</strong></p>
                $$P(\text{haplotype pair}) = 2 \times P(\text{hap}_1) \times P(\text{hap}_2)$$

                <p>The factor of 2 accounts for both possible maternal/paternal assignments</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">Next: Computational Methods →</button>
            </div>
        </div>

        <!-- Section 2: Computational Methods for Unrelated Individuals -->
        <div id="section2" class="section hidden">
            <h2>2. Computational Methods for Unrelated Individuals</h2>

            <div class="highlight">
                <strong>Statistical Foundation:</strong> Computational phasing pools information across individuals to estimate haplotype frequencies and infer the most likely phase configurations using population-based models.
            </div>

            <h3>Historical Development</h3>

            <div class="svg-container">
                <svg width="800" height="300" viewBox="0 0 800 300">
                    <!-- Background -->
                    <rect width="800" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Evolution of Computational Phasing Methods
                    </text>

                    <!-- Timeline -->
                    <line x1="100" y1="250" x2="700" y2="250" stroke="#34495e" stroke-width="3"/>

                    <!-- Clark's Algorithm (1990) -->
                    <g transform="translate(150, 60)">
                        <circle cx="0" cy="190" r="8" fill="#e74c3c"/>
                        <line x1="0" y1="190" x2="0" y2="250" stroke="#e74c3c" stroke-width="2"/>
                        <rect width="120" height="80" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Clark's Algorithm</text>
                        <text x="60" y="40" text-anchor="middle" font-size="10" fill="#2c3e50">1990</text>
                        <text x="60" y="55" text-anchor="middle" font-size="9" fill="#2c3e50">Parsimony-based</text>
                        <text x="60" y="70" text-anchor="middle" font-size="9" fill="#2c3e50">Unambiguous haps</text>
                        <text x="0" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">1990</text>
                    </g>

                    <!-- EM Algorithm (1995) -->
                    <g transform="translate(280, 60)">
                        <circle cx="0" cy="190" r="8" fill="#f39c12"/>
                        <line x1="0" y1="190" x2="0" y2="250" stroke="#f39c12" stroke-width="2"/>
                        <rect width="120" height="80" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">EM Algorithm</text>
                        <text x="60" y="40" text-anchor="middle" font-size="10" fill="#2c3e50">1995</text>
                        <text x="60" y="55" text-anchor="middle" font-size="9" fill="#2c3e50">Maximum likelihood</text>
                        <text x="60" y="70" text-anchor="middle" font-size="9" fill="#2c3e50">Small regions</text>
                        <text x="0" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">1995</text>
                    </g>

                    <!-- PHASE (2001) -->
                    <g transform="translate(410, 60)">
                        <circle cx="0" cy="190" r="8" fill="#3498db"/>
                        <line x1="0" y1="190" x2="0" y2="250" stroke="#3498db" stroke-width="2"/>
                        <rect width="120" height="80" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">PHASE</text>
                        <text x="60" y="40" text-anchor="middle" font-size="10" fill="#2c3e50">2001</text>
                        <text x="60" y="55" text-anchor="middle" font-size="9" fill="#2c3e50">Coalescent model</text>
                        <text x="60" y="70" text-anchor="middle" font-size="9" fill="#2c3e50">Gold standard</text>
                        <text x="0" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">2001</text>
                    </g>

                    <!-- Modern Methods (2008+) -->
                    <g transform="translate(540, 60)">
                        <circle cx="0" cy="190" r="8" fill="#27ae60"/>
                        <line x1="0" y1="190" x2="0" y2="250" stroke="#27ae60" stroke-width="2"/>
                        <rect width="120" height="80" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Modern Era</text>
                        <text x="60" y="40" text-anchor="middle" font-size="10" fill="#2c3e50">2008+</text>
                        <text x="60" y="55" text-anchor="middle" font-size="9" fill="#2c3e50">BEAGLE, MACH</text>
                        <text x="60" y="70" text-anchor="middle" font-size="9" fill="#2c3e50">Genome-wide</text>
                        <text x="0" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">2008</text>
                    </g>
                </svg>
            </div>

            <h3>Clark's Algorithm</h3>

            <div class="math-box">
                <h4>Parsimony-Based Approach</h4>
                <p><strong>Core Principle:</strong> Use unambiguous haplotypes to resolve ambiguous ones</p>

                <p><strong>Unambiguous haplotypes:</strong> Individuals with ≤1 heterozygous marker</p>
                <ul>
                    <li>Homozygous at all sites: phase is trivial</li>
                    <li>Heterozygous at one site: two possible haplotypes known</li>
                </ul>

                <p><strong>Algorithm Steps:</strong></p>
                <ol>
                    <li>Identify all unambiguous haplotypes in sample</li>
                    <li>For each ambiguous individual, check which haplotype pairs are consistent with observed genotypes</li>
                    <li>Choose solution using fewest unique haplotypes (parsimony)</li>
                </ol>

                <p><strong>Limitation:</strong> Only works for tightly linked polymorphisms</p>
            </div>

            <h3>EM Algorithm</h3>

            <div class="math-box">
                <h4>Maximum Likelihood Estimation</h4>
                <p><strong>Likelihood Function:</strong></p>

                $$L(\theta) = \prod_{i=1}^{n} P(G_i | \theta)$$

                <p>Where G_i is the genotype of individual i, θ represents haplotype frequencies</p>

                <p><strong>E-step:</strong> Calculate expected haplotype counts</p>
                $$E[n_{hj}] = \sum_{i=1}^{n} P(h_j \in \text{individual } i | G_i, \theta^{(t)})$$

                <p><strong>M-step:</strong> Update haplotype frequency estimates</p>
                $$\theta_j^{(t+1)} = \frac{E[n_{hj}]}{2n}$$

                <p><strong>Convergence:</strong> Iterate until likelihood stabilizes</p>
            </div>

            <h3>Modern HMM-Based Methods</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Year</th>
                        <th>Key Innovation</th>
                        <th>Best Use Case</th>
                        <th>Limitations</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>PHASE</strong></td>
                        <td>2001</td>
                        <td>Coalescent model</td>
                        <td>Small regions, high accuracy</td>
                        <td>Very slow, ≤100 markers</td>
                    </tr>
                    <tr>
                        <td><strong>fastPHASE</strong></td>
                        <td>2006</td>
                        <td>Haplotype clustering</td>
                        <td>Genome-wide arrays</td>
                        <td>Less accurate for large samples</td>
                    </tr>
                    <tr>
                        <td><strong>BEAGLE</strong></td>
                        <td>2008</td>
                        <td>Adaptive clustering</td>
                        <td>Large samples (>1000)</td>
                        <td>Needs ≥100 markers</td>
                    </tr>
                    <tr>
                        <td><strong>MACH</strong></td>
                        <td>2010</td>
                        <td>Template selection</td>
                        <td>Medium samples</td>
                        <td>Slower than BEAGLE</td>
                    </tr>
                    <tr>
                        <td><strong>IMPUTE2</strong></td>
                        <td>2009</td>
                        <td>Smart template choice</td>
                        <td>Imputation + phasing</td>
                        <td>Complex parameter tuning</td>
                    </tr>
                </tbody>
            </table>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(1)">← Previous</button>
                <button class="nav-button" onclick="showSection(3)">Next: Family-Based Phasing →</button>
            </div>
        </div>

        <!-- Section 3: Family-Based Phasing -->
        <div id="section3" class="section hidden">
            <h2>3. Family-Based Phasing</h2>

            <div class="highlight">
                <strong>Identity-by-Descent Power:</strong> Family relationships provide much more informative constraints than population frequencies, enabling near-perfect phasing over long chromosomal regions.
            </div>

            <h3>IBD-Based Phasing Principle</h3>

            <div class="math-box">
                <h4>Mendelian Constraints</h4>
                <p><strong>Parent-Offspring Relationship:</strong></p>
                <p>Must share exactly one allele identical-by-descent at every position</p>

                <p><strong>Phase Determination:</strong></p>
                <p>If either individual is homozygous at a site, the shared allele is known</p>

                <p><strong>Trio Phasing:</strong></p>
                <p>Phase unknown only when all three individuals (mother-father-child) are heterozygous</p>

                <p><strong>Probability of ambiguous site in trio:</strong></p>
                $$P(\text{ambiguous}) = P(\text{all het}) = (2pq)^3$$

                <p>Where p and q are allele frequencies. For p=q=0.5: P ≈ 12.5%</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">← Previous</button>
                <button class="nav-button" onclick="showSection(4)">Next: Statistical Models →</button>
            </div>
        </div>

        <!-- Section 4: Advanced Statistical Models -->
        <div id="section4" class="section hidden">
            <h2>4. Advanced Statistical Models</h2>

            <div class="highlight">
                <strong>Coalescent Framework:</strong> Modern methods model haplotype evolution through mutation and recombination, using Hidden Markov Models to capture local dependencies efficiently.
            </div>

            <h3>Li & Stephens Model</h3>

            <div class="math-box">
                <h4>Approximate Coalescent</h4>
                <p><strong>Core Idea:</strong> Each haplotype is a mosaic of other haplotypes due to recombination</p>

                <p><strong>Transition Probability:</strong></p>
                $$P(\text{copy from hap } j \text{ at pos } \ell+1 | \text{copied from hap } i \text{ at pos } \ell) = \begin{cases}
                1-\rho_\ell & \text{if } i = j \\
                \frac{\rho_\ell}{n-1} & \text{if } i \neq j
                \end{cases}$$

                <p>Where ρ_ℓ is recombination probability between positions ℓ and ℓ+1</p>

                <p><strong>Emission Probability:</strong></p>
                $$P(\text{observe allele } a | \text{copy from template with allele } b) = \begin{cases}
                1-\mu & \text{if } a = b \\
                \mu & \text{if } a \neq b
                \end{cases}$$

                <p>Where μ is mutation probability</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(3)">← Previous</button>
                <button class="nav-button" onclick="showSection(5)">Next: IBD Approaches →</button>
            </div>
        </div>

        <!-- Section 5: Identity-by-Descent Approaches -->
        <div id="section5" class="section hidden">
            <h2>5. Identity-by-Descent Approaches</h2>

            <div class="highlight">
                <strong>Long-Range Phasing:</strong> Even "unrelated" individuals share IBD segments from recent common ancestors, enabling rule-based phasing in isolated populations like Iceland.
            </div>

            <h3>Kong et al. Long-Range Phasing</h3>

            <div class="algorithm-box">
                <h4>Icelandic Population Success</h4>
                <div class="step">
                    <strong>IBD Detection:</strong> Long segments (≥10 Mb) where individuals share alleles at all markers
                </div>
                <div class="step">
                    <strong>Population Coverage:</strong> >10% of Iceland genotyped, enabling IBD detection for most individuals
                </div>
                <div class="step">
                    <strong>Phasing Success:</strong> 90-95% of heterozygous markers successfully phased
                </div>
                <div class="step">
                    <strong>Applications:</strong> Parent-of-origin effects, rare variant imputation, recombination mapping
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(4)">← Previous</button>
                <button class="nav-button" onclick="showSection(6)">Next: Experimental Methods →</button>
            </div>
        </div>

        <!-- Section 6: Experimental Phasing Methods -->
        <div id="section6" class="section hidden">
            <h2>6. Experimental Phasing Methods</h2>

            <div class="highlight">
                <strong>Physical Separation:</strong> Laboratory methods physically separate homologous chromosomes before sequencing, providing direct phase information independent of computational inference.
            </div>

            <h3>Chromosome Separation Techniques</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Approach</th>
                        <th>Advantages</th>
                        <th>Limitations</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Microdissection</strong></td>
                        <td>Physical separation of metaphase chromosomes</td>
                        <td>Direct, no computation needed</td>
                        <td>Labor intensive, low throughput</td>
                    </tr>
                    <tr>
                        <td><strong>FACS</strong></td>
                        <td>Flow cytometry chromosome sorting</td>
                        <td>Automated separation</td>
                        <td>Requires specialized equipment</td>
                    </tr>
                    <tr>
                        <td><strong>Microfluidics</strong></td>
                        <td>Single-cell chromosome partitioning</td>
                        <td>Precise control</td>
                        <td>Complex device fabrication</td>
                    </tr>
                    <tr>
                        <td><strong>Fosmid Libraries</strong></td>
                        <td>Long-range haplotype cloning</td>
                        <td>Scalable, cost-effective</td>
                        <td>Limited to ~40kb inserts</td>
                    </tr>
                </tbody>
            </table>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(5)">← Previous</button>
                <button class="nav-button" onclick="showSection(7)">Next: Performance Evaluation →</button>
            </div>
        </div>

        <!-- Section 7: Performance Evaluation -->
        <div id="section7" class="section hidden">
            <h2>7. Performance Evaluation</h2>

            <div class="highlight">
                <strong>Accuracy Metrics:</strong> Switch error rate is the most informative metric, measuring the number of phase switches needed to correct inferred haplotypes to true phase.
            </div>

            <h3>Key Accuracy Metrics</h3>

            <div class="math-box">
                <h4>Switch Error Rate</h4>
                <p><strong>Definition:</strong> Number of switches required to correct inferred phase</p>

                $$\text{Switch Error Rate} = \frac{\text{Number of switches required}}{\text{Number of heterozygous markers} - 1}$$

                <p><strong>Haplotype Accuracy:</strong></p>
                $$\text{Haplotype Accuracy} = \frac{\text{Correctly phased haplotypes}}{\text{Total haplotypes}}$$

                <p><strong>Imputation Accuracy:</strong></p>
                $$\text{Imputation Accuracy} = \frac{\text{Correctly imputed alleles}}{\text{Total masked alleles}}$$

                <p>This metric can be applied without gold-standard phase data</p>
            </div>

            <h3>Factors Affecting Accuracy</h3>

            <div class="algorithm-box">
                <h4>Key Performance Factors</h4>
                <div class="step">
                    <strong>Sample Size:</strong> Larger samples provide better haplotype frequency estimates
                </div>
                <div class="step">
                    <strong>Marker Density:</strong> Denser markers improve local accuracy but increase error opportunities
                </div>
                <div class="step">
                    <strong>Population Structure:</strong> African populations more challenging due to higher diversity
                </div>
                <div class="step">
                    <strong>Relatedness:</strong> Even cryptic relatedness substantially improves accuracy
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(6)">← Previous</button>
                <button class="nav-button" onclick="showSection(8)">Next: Applications & Future →</button>
            </div>
        </div>

        <!-- Section 8: Applications & Future Directions -->
        <div id="section8" class="section hidden">
            <h2>8. Applications & Future Directions</h2>

            <div class="highlight">
                <strong>Transformative Impact:</strong> Accurate haplotype phasing enables precision medicine, population genetics insights, and improved understanding of human genetic variation and disease.
            </div>

            <h3>Clinical Applications</h3>

            <div class="algorithm-box">
                <h4>Medical Genetics Applications</h4>
                <div class="step">
                    <strong>Compound Heterozygosity:</strong> Distinguishing cis vs trans variants in recessive diseases
                </div>
                <div class="step">
                    <strong>Pharmacogenomics:</strong> Haplotype-based drug response prediction
                </div>
                <div class="step">
                    <strong>Cancer Genomics:</strong> Allele-specific expression and loss of heterozygosity
                </div>
                <div class="step">
                    <strong>Reproductive Genetics:</strong> Preimplantation genetic diagnosis and carrier screening
                </div>
            </div>

            <h3>Population Genetics</h3>

            <div class="math-box">
                <h4>Evolutionary Insights</h4>
                <p><strong>Selection Detection:</strong> Extended haplotype homozygosity (EHH)</p>

                <p><strong>Demographic History:</strong> Haplotype diversity patterns reveal population bottlenecks and expansions</p>

                <p><strong>Recombination Mapping:</strong> Fine-scale recombination rate variation</p>

                <p><strong>Linkage Disequilibrium:</strong></p>
                $$D' = \frac{|D|}{\min(p_A p_b, p_a p_B)}$$

                <p>Where D = p_{AB} - p_A p_B (linkage disequilibrium coefficient)</p>
            </div>

            <h3>Future Directions</h3>

            <div class="algorithm-box">
                <h4>Emerging Technologies</h4>
                <div class="step">
                    <strong>Long-Read Sequencing:</strong> PacBio and Oxford Nanopore enable direct haplotype sequencing
                </div>
                <div class="step">
                    <strong>Single-Cell Methods:</strong> Chromosome-specific amplification and sequencing
                </div>
                <div class="step">
                    <strong>Hi-C Phasing:</strong> Chromosome conformation capture for long-range phase information
                </div>
                <div class="step">
                    <strong>Machine Learning:</strong> Deep learning approaches for complex phasing scenarios
                </div>
            </div>

            <div class="highlight">
                <strong>Future Vision:</strong> Integration of computational and experimental approaches will enable routine, cost-effective, genome-wide haplotype determination, revolutionizing personalized medicine and population genetics research.
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(7)">← Previous</button>
                <button class="nav-button" onclick="showSection(1)">Return to Start</button>
            </div>
        </div>

        <script>
            function showSection(sectionNum) {
                // Hide all sections
                for (let i = 1; i <= 8; i++) {
                    const section = document.getElementById('section' + i);
                    if (section) {
                        section.classList.add('hidden');
                    }
                }
                
                // Show the requested section
                const targetSection = document.getElementById('section' + sectionNum);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
            
            // Initialize MathJax after page load
            window.addEventListener('load', function() {
                if (window.MathJax) {
                    MathJax.typesetPromise();
                }
            });
        </script>
    </div>
</body>
</html>
