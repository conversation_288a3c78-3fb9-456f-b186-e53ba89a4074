<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Structural Variants: The Long and Short of It</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .nav {
            background: #34495e;
            padding: 1rem 2rem;
            border-bottom: 3px solid #3498db;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .nav a:hover {
            background-color: #3498db;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            border-left: 4px solid #3498db;
            padding-left: 2rem;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .section h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .key-concept {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        
        .formula-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 2rem;
            margin: 1.5rem 0;
            text-align: center;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            border-left: 4px solid #4299e1;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .performance-table th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 1rem;
            text-align: left;
        }
        
        .performance-table td {
            padding: 0.8rem 1rem;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .performance-table tr:hover {
            background: #f8f9fa;
        }
        
        .sv-type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .sv-type-card {
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s;
        }
        
        .sv-type-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .sv-type-card.deletion {
            border-color: #e74c3c;
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        }
        
        .sv-type-card.duplication {
            border-color: #f39c12;
            background: linear-gradient(135deg, #fffaf0 0%, #fbd38d 100%);
        }
        
        .sv-type-card.insertion {
            border-color: #2ecc71;
            background: linear-gradient(135deg, #f0fff4 0%, #9ae6b4 100%);
        }
        
        .sv-type-card.inversion {
            border-color: #9b59b6;
            background: linear-gradient(135deg, #faf5ff 0%, #d6bcfa 100%);
        }
        
        .sv-type-card.translocation {
            border-color: #3498db;
            background: linear-gradient(135deg, #f0f8ff 0%, #90cdf4 100%);
        }
        
        .sv-type-card.complex {
            border-color: #34495e;
            background: linear-gradient(135deg, #f7fafc 0%, #a0aec0 100%);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .content {
                padding: 1rem;
            }
            
            .feature-grid, .sv-type-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Structural Variants</h1>
            <p class="subtitle">The Long and Short of It: Understanding Genomic Structural Variation</p>
        </header>
        
        <nav class="nav">
            <ul>
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#types">SV Types</a></li>
                <li><a href="#evidence">Evidence Categories</a></li>
                <li><a href="#detection">Detection Methods</a></li>
                <li><a href="#challenges">Challenges</a></li>
                <li><a href="#applications">Applications</a></li>
                <li><a href="#future">Future</a></li>
            </ul>
        </nav>
        
        <main class="content">
            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>Introduction to Structural Variants</h2>
                
                <div class="key-concept">
                    <strong>Definition:</strong> Structural variants (SVs) are large genomic alterations, typically defined as encompassing at least 50 base pairs. These include deletions, duplications, insertions, inversions, and translocations, as well as complex rearrangements.
                </div>
                
                <p>Structural variants represent a major class of genetic variation that has been increasingly recognized as crucial to understanding human evolution, disease, and population diversity. While single nucleotide variants (SNVs) account for only 0.1% of genomic variation between humans, structural variants increase this to 1.5% - making them 15 times more significant in terms of genomic diversity.</p>
                
                <div class="svg-container">
                    <h3>Genomic Variation Spectrum</h3>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Scale bar -->
                        <line x1="50" y1="250" x2="750" y2="250" stroke="#34495e" stroke-width="2"/>
                        <text x="400" y="270" text-anchor="middle" fill="#2c3e50" font-size="12">Genomic Variation Scale (bp)</text>
                        
                        <!-- SNVs -->
                        <rect x="50" y="200" width="30" height="40" fill="#3498db" rx="3"/>
                        <text x="65" y="225" text-anchor="middle" fill="white" font-size="10">SNV</text>
                        <text x="65" y="240" text-anchor="middle" fill="white" font-size="8">1-3 bp</text>
                        
                        <!-- Indels -->
                        <rect x="100" y="190" width="40" height="50" fill="#2ecc71" rx="3"/>
                        <text x="120" y="215" text-anchor="middle" fill="white" font-size="10">Indel</text>
                        <text x="120" y="230" text-anchor="middle" fill="white" font-size="8">1-50 bp</text>
                        
                        <!-- SVs -->
                        <rect x="160" y="150" width="60" height="90" fill="#e74c3c" rx="5"/>
                        <text x="190" y="175" text-anchor="middle" fill="white" font-size="12" font-weight="bold">SV</text>
                        <text x="190" y="190" text-anchor="middle" fill="white" font-size="10">50+ bp</text>
                        <text x="190" y="205" text-anchor="middle" fill="white" font-size="8">to Mb</text>
                        
                        <!-- Large SVs -->
                        <rect x="240" y="100" width="80" height="140" fill="#9b59b6" rx="5"/>
                        <text x="280" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Large SV</text>
                        <text x="280" y="140" text-anchor="middle" fill="white" font-size="10">kb to Mb</text>
                        
                        <!-- Chromosomal -->
                        <rect x="340" y="50" width="100" height="190" fill="#f39c12" rx="5"/>
                        <text x="390" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Chromosomal</text>
                        <text x="390" y="90" text-anchor="middle" fill="white" font-size="10">Mb to entire</text>
                        <text x="390" y="105" text-anchor="middle" fill="white" font-size="10">chromosomes</text>
                        
                        <!-- Scale markers -->
                        <text x="50" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">1 bp</text>
                        <text x="150" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">50 bp</text>
                        <text x="280" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">1 kb</text>
                        <text x="390" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">1 Mb</text>
                        <text x="750" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">100+ Mb</text>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Structural Variants in Context</text>
                        
                        <!-- Impact indicator -->
                        <g transform="translate(600, 100)">
                            <text x="0" y="0" fill="#2c3e50" font-size="14" font-weight="bold">Impact on Variation:</text>
                            <text x="0" y="20" fill="#e74c3c" font-size="12">• SNVs: 0.1%</text>
                            <text x="0" y="35" fill="#e74c3c" font-size="12">• SVs: 1.5%</text>
                            <text x="0" y="50" fill="#e74c3c" font-size="12">• 15x more significant!</text>
                        </g>
                    </svg>
                </div>
                
                <h3>Why Structural Variants Matter</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🏥 Disease Impact</h4>
                        <p><strong>Neurological:</strong> ATTCC repeat extensions in Parkinson's, CAG expansions in Huntington's disease</p>
                        <p><strong>Cancer:</strong> Gene fusions, amplifications, and regulatory element alterations</p>
                        <p><strong>Mendelian:</strong> Deletions/duplications causing syndromes like Coffin-Siris</p>
                    </div>
                    <div class="feature-card">
                        <h4>🧬 Evolutionary Significance</h4>
                        <p><strong>Population Diversity:</strong> Major contributor to ethnic and population differences</p>
                        <p><strong>Species Evolution:</strong> Large-scale chromosome evolution and speciation</p>
                        <p><strong>Adaptation:</strong> Environmental adaptation in plants and animals</p>
                    </div>
                    <div class="feature-card">
                        <h4>🌱 Agricultural Impact</h4>
                        <p><strong>Crop Improvement:</strong> Aluminum resistance in maize, boron tolerance in barley</p>
                        <p><strong>Fruit Quality:</strong> Anthocyanin concentration in blood oranges</p>
                        <p><strong>Yield Enhancement:</strong> Tomato fruit yield through tandem duplications</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔬 Research Challenges</h4>
                        <p><strong>Detection Difficulty:</strong> Much harder to identify than SNVs</p>
                        <p><strong>Size Range:</strong> Can be larger than sequencing read lengths</p>
                        <p><strong>Complexity:</strong> Multiple overlapping or nested events</p>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <h3>Key Statistics</h3>
                    <ul>
                        <li><strong>Size Range:</strong> 50 bp to entire chromosomes</li>
                        <li><strong>Prevalence:</strong> 1.5% of genomic variation between humans</li>
                        <li><strong>Detection Rate:</strong> 10-70% with current methods</li>
                        <li><strong>False Positive Rate:</strong> Up to 89% in some studies</li>
                        <li><strong>Clinical Impact:</strong> Associated with numerous diseases and conditions</li>
                    </ul>
                </div>
                         </section>
             
             <!-- SV Types Section -->
             <section id="types" class="section">
                 <h2>Types of Structural Variants</h2>
                 
                 <p>Structural variants can be classified into several major categories based on their nature and impact on the genome. Understanding these types is crucial for both detection and interpretation.</p>
                 
                 <div class="svg-container">
                     <h3>Structural Variant Classification</h3>
                     <svg width="900" height="500" viewBox="0 0 900 500">
                         <!-- Main categories -->
                         <g>
                             <!-- Copy Number Variants -->
                             <rect x="50" y="50" width="200" height="80" fill="#e74c3c" rx="10"/>
                             <text x="150" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Copy Number Variants</text>
                             <text x="150" y="95" text-anchor="middle" fill="white" font-size="12">(Unbalanced SVs)</text>
                             <text x="150" y="110" text-anchor="middle" fill="white" font-size="10">DNA gained or lost</text>
                             
                             <!-- Balanced SVs -->
                             <rect x="300" y="50" width="200" height="80" fill="#3498db" rx="10"/>
                             <text x="400" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Balanced SVs</text>
                             <text x="400" y="95" text-anchor="middle" fill="white" font-size="12">(Rearrangements)</text>
                             <text x="400" y="110" text-anchor="middle" fill="white" font-size="10">No net DNA change</text>
                             
                             <!-- Complex SVs -->
                             <rect x="550" y="50" width="200" height="80" fill="#9b59b6" rx="10"/>
                             <text x="650" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Complex SVs</text>
                             <text x="650" y="95" text-anchor="middle" fill="white" font-size="12">(Multiple events)</text>
                             <text x="650" y="110" text-anchor="middle" fill="white" font-size="10">Combined variants</text>
                         </g>
                         
                         <!-- CNV subtypes -->
                         <g>
                             <!-- Deletion -->
                             <rect x="30" y="180" width="120" height="60" fill="#e74c3c" opacity="0.8" rx="8"/>
                             <text x="90" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Deletion</text>
                             <text x="90" y="215" text-anchor="middle" fill="white" font-size="10">(DEL)</text>
                             <text x="90" y="230" text-anchor="middle" fill="white" font-size="9">DNA lost</text>
                             
                             <!-- Duplication -->
                             <rect x="170" y="180" width="120" height="60" fill="#f39c12" opacity="0.8" rx="8"/>
                             <text x="230" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Duplication</text>
                             <text x="230" y="215" text-anchor="middle" fill="white" font-size="10">(DUP)</text>
                             <text x="230" y="230" text-anchor="middle" fill="white" font-size="9">DNA gained</text>
                             
                             <!-- MCNV -->
                             <rect x="110" y="260" width="120" height="60" fill="#e67e22" opacity="0.8" rx="8"/>
                             <text x="170" y="280" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Multiallelic CNV</text>
                             <text x="170" y="295" text-anchor="middle" fill="white" font-size="10">(MCNV)</text>
                             <text x="170" y="310" text-anchor="middle" fill="white" font-size="9">Multiple alleles</text>
                         </g>
                         
                         <!-- Balanced SV subtypes -->
                         <g>
                             <!-- Insertion -->
                             <rect x="320" y="180" width="120" height="60" fill="#2ecc71" opacity="0.8" rx="8"/>
                             <text x="380" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Insertion</text>
                             <text x="380" y="215" text-anchor="middle" fill="white" font-size="10">(INS)</text>
                             <text x="380" y="230" text-anchor="middle" fill="white" font-size="9">Novel sequence</text>
                             
                             <!-- Inversion -->
                             <rect x="460" y="180" width="120" height="60" fill="#9b59b6" opacity="0.8" rx="8"/>
                             <text x="520" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Inversion</text>
                             <text x="520" y="215" text-anchor="middle" fill="white" font-size="10">(INV)</text>
                             <text x="520" y="230" text-anchor="middle" fill="white" font-size="9">Orientation flip</text>
                             
                             <!-- Translocation -->
                             <rect x="390" y="260" width="120" height="60" fill="#3498db" opacity="0.8" rx="8"/>
                             <text x="450" y="280" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Translocation</text>
                             <text x="450" y="295" text-anchor="middle" fill="white" font-size="10">(CTX)</text>
                             <text x="450" y="310" text-anchor="middle" fill="white" font-size="9">Chromosome swap</text>
                         </g>
                         
                         <!-- Complex and Unresolved -->
                         <g>
                             <!-- Complex -->
                             <rect x="600" y="180" width="120" height="60" fill="#34495e" opacity="0.8" rx="8"/>
                             <text x="660" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Complex</text>
                             <text x="660" y="215" text-anchor="middle" fill="white" font-size="10">(CPX)</text>
                             <text x="660" y="230" text-anchor="middle" fill="white" font-size="9">Multiple types</text>
                             
                             <!-- Breakends -->
                             <rect x="600" y="260" width="120" height="60" fill="#95a5a6" opacity="0.8" rx="8"/>
                             <text x="660" y="280" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Breakends</text>
                             <text x="660" y="295" text-anchor="middle" fill="white" font-size="10">(BND)</text>
                             <text x="660" y="310" text-anchor="middle" fill="white" font-size="9">Unresolved</text>
                         </g>
                         
                         <!-- Connecting lines -->
                         <defs>
                             <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                 <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                             </marker>
                         </defs>
                         
                         <!-- CNV connections -->
                         <line x1="150" y1="130" x2="90" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="150" y1="130" x2="230" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="150" y1="130" x2="170" y2="260" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         
                         <!-- Balanced connections -->
                         <line x1="400" y1="130" x2="380" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="400" y1="130" x2="520" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="400" y1="130" x2="450" y2="260" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         
                         <!-- Complex connections -->
                         <line x1="650" y1="130" x2="660" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="650" y1="130" x2="660" y2="260" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         
                         <!-- Title -->
                         <text x="450" y="30" text-anchor="middle" fill="#2c3e50" font-size="20" font-weight="bold">Structural Variant Classification</text>
                         
                         <!-- Examples -->
                         <g transform="translate(50, 380)">
                             <text x="0" y="0" fill="#2c3e50" font-size="14" font-weight="bold">Examples:</text>
                             <text x="0" y="20" fill="#e74c3c" font-size="12">• Deletion: BRCA1/2 in cancer</text>
                             <text x="0" y="35" fill="#f39c12" font-size="12">• Duplication: AMTE1 in maize</text>
                             <text x="0" y="50" fill="#2ecc71" font-size="12">• Insertion: LINE1, ALU elements</text>
                             <text x="0" y="65" fill="#9b59b6" font-size="12">• Inversion: Chromosome 17</text>
                             <text x="0" y="80" fill="#3498db" font-size="12">• Translocation: Philadelphia chromosome</text>
                         </g>
                     </svg>
                 </div>
                 
                 <h3>Detailed SV Type Descriptions</h3>
                 <div class="sv-type-grid">
                     <div class="sv-type-card deletion">
                         <h4>🗑️ Deletion (DEL)</h4>
                         <p><strong>Definition:</strong> A segment of DNA is lost compared to the reference sequence</p>
                         <p><strong>Impact:</strong> Gene loss, regulatory disruption</p>
                         <p><strong>Detection:</strong> Reduced read depth, larger insert sizes</p>
                         <p><strong>Example:</strong> BRCA1/2 deletions in breast cancer</p>
                     </div>
                     
                     <div class="sv-type-card duplication">
                         <h4>📋 Duplication (DUP)</h4>
                         <p><strong>Definition:</strong> Large genomic region copied one or more times</p>
                         <p><strong>Types:</strong> Tandem (adjacent) or dispersed (separate locations)</p>
                         <p><strong>Impact:</strong> Gene dosage increase, overexpression</p>
                         <p><strong>Example:</strong> AMTE1 triplication in maize</p>
                     </div>
                     
                     <div class="sv-type-card insertion">
                         <h4>➕ Insertion (INS)</h4>
                         <p><strong>Definition:</strong> Novel DNA sequence inserted into genome</p>
                         <p><strong>Types:</strong> LINE1, SVA, ALU mobile elements</p>
                         <p><strong>Impact:</strong> Gene disruption, regulatory changes</p>
                         <p><strong>Example:</strong> TAF1 retrotransposon insertion</p>
                     </div>
                     
                     <div class="sv-type-card inversion">
                         <h4>🔄 Inversion (INV)</h4>
                         <p><strong>Definition:</strong> Orientation of DNA segment flipped</p>
                         <p><strong>Impact:</strong> Gene orientation changes, regulatory disruption</p>
                         <p><strong>Detection:</strong> Abnormal read orientations</p>
                         <p><strong>Example:</strong> Chromosome 17 inversions</p>
                     </div>
                     
                     <div class="sv-type-card translocation">
                         <h4>🔄 Translocation (CTX)</h4>
                         <p><strong>Definition:</strong> Chromosomal segment moves to new location</p>
                         <p><strong>Types:</strong> Reciprocal or nonreciprocal</p>
                         <p><strong>Impact:</strong> Gene fusions, regulatory changes</p>
                         <p><strong>Example:</strong> Philadelphia chromosome (BCR-ABL)</p>
                     </div>
                     
                     <div class="sv-type-card complex">
                         <h4>🎭 Complex (CPX)</h4>
                         <p><strong>Definition:</strong> Multiple SV types occurring together</p>
                         <p><strong>Impact:</strong> Complex genomic rearrangements</p>
                         <p><strong>Detection:</strong> Multiple evidence types required</p>
                         <p><strong>Example:</strong> Chromothripsis in cancer</p>
                     </div>
                 </div>
                 
                 <div class="svg-container">
                     <h3>SV Detection Signatures</h3>
                     <svg width="900" height="400" viewBox="0 0 900 400">
                         <!-- Reference genome -->
                         <rect x="50" y="100" width="800" height="30" fill="#95a5a6" stroke="#7f8c8d"/>
                         <text x="450" y="120" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Reference Genome</text>
                         
                         <!-- SV examples -->
                         <g>
                             <!-- Deletion -->
                             <rect x="100" y="150" width="200" height="30" fill="#e74c3c" opacity="0.7"/>
                             <text x="200" y="170" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Deletion</text>
                             <text x="200" y="185" text-anchor="middle" fill="white" font-size="10">Larger insert size</text>
                             <text x="200" y="200" text-anchor="middle" fill="white" font-size="10">Reduced coverage</text>
                             
                             <!-- Duplication -->
                             <rect x="350" y="150" width="200" height="30" fill="#f39c12" opacity="0.7"/>
                             <text x="450" y="170" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Duplication</text>
                             <text x="450" y="185" text-anchor="middle" fill="white" font-size="10">Increased coverage</text>
                             <text x="450" y="200" text-anchor="middle" fill="white" font-size="10">Abnormal orientations</text>
                             
                             <!-- Inversion -->
                             <rect x="600" y="150" width="200" height="30" fill="#9b59b6" opacity="0.7"/>
                             <text x="700" y="170" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Inversion</text>
                             <text x="700" y="185" text-anchor="middle" fill="white" font-size="10">Flipped orientations</text>
                             <text x="700" y="200" text-anchor="middle" fill="white" font-size="10">Split reads</text>
                         </g>
                         
                         <!-- Read patterns -->
                         <g>
                             <!-- Normal reads -->
                             <rect x="50" y="250" width="60" height="15" fill="#3498db" opacity="0.8"/>
                             <rect x="150" y="250" width="60" height="15" fill="#3498db" opacity="0.8"/>
                             <text x="110" y="245" text-anchor="middle" fill="#2c3e50" font-size="10">Normal</text>
                             
                             <!-- Deletion reads -->
                             <rect x="100" y="280" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <rect x="220" y="280" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <text x="160" y="275" text-anchor="middle" fill="#2c3e50" font-size="10">Larger gap</text>
                             
                             <!-- Duplication reads -->
                             <rect x="350" y="280" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <rect x="420" y="280" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <rect x="490" y="280" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <text x="420" y="275" text-anchor="middle" fill="#2c3e50" font-size="10">Extra copies</text>
                             
                             <!-- Inversion reads -->
                             <rect x="600" y="280" width="60" height="15" fill="#9b59b6" opacity="0.8"/>
                             <rect x="680" y="280" width="60" height="15" fill="#9b59b6" opacity="0.8"/>
                             <text x="640" y="275" text-anchor="middle" fill="#2c3e50" font-size="10">Flipped</text>
                         </g>
                         
                         <!-- Coverage plots -->
                         <g>
                             <!-- Normal coverage -->
                             <line x1="50" y1="350" x2="850" y2="350" stroke="#34495e" stroke-width="2"/>
                             <rect x="50" y="330" width="800" height="20" fill="#3498db" opacity="0.3"/>
                             <text x="450" y="325" text-anchor="middle" fill="#2c3e50" font-size="12">Normal Coverage</text>
                             
                             <!-- Deletion coverage -->
                             <line x1="50" y1="380" x2="850" y2="380" stroke="#34495e" stroke-width="2"/>
                             <rect x="50" y="360" width="800" height="20" fill="#3498db" opacity="0.3"/>
                             <rect x="100" y="360" width="200" height="20" fill="#e74c3c" opacity="0.5"/>
                             <text x="450" y="355" text-anchor="middle" fill="#2c3e50" font-size="12">Deletion: Reduced Coverage</text>
                         </g>
                         
                         <!-- Title -->
                         <text x="450" y="30" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Detection Signatures for Different SV Types</text>
                     </svg>
                 </div>
             </section>
             
             <!-- Evidence Categories Section -->
             <section id="evidence" class="section">
                 <h2>Evidence Categories for SV Detection</h2>
                 
                 <p>Structural variant detection relies on multiple types of evidence from sequencing data. Understanding these evidence categories is crucial for interpreting SV calls and understanding the limitations of different detection methods.</p>
                 
                 <div class="svg-container">
                     <h3>Evidence Categories Overview</h3>
                     <svg width="900" height="500" viewBox="0 0 900 500">
                         <!-- Reference genome -->
                         <rect x="50" y="100" width="800" height="30" fill="#95a5a6" stroke="#7f8c8d"/>
                         <text x="450" y="120" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Reference Genome</text>
                         
                         <!-- Evidence types -->
                         <g>
                             <!-- Paired-end evidence -->
                             <rect x="50" y="200" width="200" height="80" fill="#3498db" rx="10"/>
                             <text x="150" y="225" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Paired-End (PE)</text>
                             <text x="150" y="245" text-anchor="middle" fill="white" font-size="12">Insert size & orientation</text>
                             <text x="150" y="260" text-anchor="middle" fill="white" font-size="10">Distance between mates</text>
                             
                             <!-- Split read evidence -->
                             <rect x="350" y="200" width="200" height="80" fill="#e74c3c" rx="10"/>
                             <text x="450" y="225" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Split Reads (SR)</text>
                             <text x="450" y="245" text-anchor="middle" fill="white" font-size="12">Breakpoint resolution</text>
                             <text x="450" y="260" text-anchor="middle" fill="white" font-size="10">Precise coordinates</text>
                             
                             <!-- Read depth evidence -->
                             <rect x="650" y="200" width="200" height="80" fill="#2ecc71" rx="10"/>
                             <text x="750" y="225" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Read Depth (RD)</text>
                             <text x="750" y="245" text-anchor="middle" fill="white" font-size="12">Coverage analysis</text>
                             <text x="750" y="260" text-anchor="middle" fill="white" font-size="10">Copy number changes</text>
                         </g>
                         
                         <!-- Paired-end example -->
                         <g>
                             <!-- Normal PE -->
                             <rect x="50" y="320" width="60" height="15" fill="#3498db" opacity="0.8"/>
                             <rect x="150" y="320" width="60" height="15" fill="#3498db" opacity="0.8"/>
                             <line x1="110" y1="327" x2="150" y2="327" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                             <text x="100" y="315" text-anchor="middle" fill="#2c3e50" font-size="10">Normal</text>
                             <text x="100" y="345" text-anchor="middle" fill="#2c3e50" font-size="10">~500bp</text>
                             
                             <!-- Deletion PE -->
                             <rect x="50" y="370" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <rect x="220" y="370" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <line x1="110" y1="377" x2="220" y2="377" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                             <text x="135" y="365" text-anchor="middle" fill="#2c3e50" font-size="10">Deletion</text>
                             <text x="135" y="395" text-anchor="middle" fill="#2c3e50" font-size="10">~700bp</text>
                             
                             <!-- Duplication PE -->
                             <rect x="50" y="420" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <rect x="120" y="420" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <rect x="190" y="420" width="60" height="15" fill="#f39c12" opacity="0.8"/>
                             <text x="120" y="415" text-anchor="middle" fill="#2c3e50" font-size="10">Duplication</text>
                             <text x="120" y="445" text-anchor="middle" fill="#2c3e50" font-size="10">Abnormal</text>
                         </g>
                         
                         <!-- Split read example -->
                         <g>
                             <!-- Normal read -->
                             <rect x="350" y="320" width="120" height="15" fill="#3498db" opacity="0.8"/>
                             <text x="410" y="315" text-anchor="middle" fill="#2c3e50" font-size="10">Normal Read</text>
                             
                             <!-- Split read -->
                             <rect x="350" y="370" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <rect x="450" y="370" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                             <line x1="410" y1="377" x2="450" y2="377" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                             <text x="410" y="365" text-anchor="middle" fill="#2c3e50" font-size="10">Split Read</text>
                             <text x="410" y="395" text-anchor="middle" fill="#2c3e50" font-size="10">Breakpoint</text>
                         </g>
                         
                         <!-- Read depth example -->
                         <g>
                             <!-- Normal coverage -->
                             <line x1="650" y1="320" x2="850" y2="320" stroke="#34495e" stroke-width="2"/>
                             <rect x="650" y="300" width="200" height="20" fill="#3498db" opacity="0.3"/>
                             <text x="750" y="295" text-anchor="middle" fill="#2c3e50" font-size="10">Normal Coverage</text>
                             
                             <!-- Deletion coverage -->
                             <line x1="650" y1="370" x2="850" y2="370" stroke="#34495e" stroke-width="2"/>
                             <rect x="650" y="350" width="200" height="20" fill="#3498db" opacity="0.3"/>
                             <rect x="700" y="350" width="100" height="20" fill="#e74c3c" opacity="0.5"/>
                             <text x="750" y="345" text-anchor="middle" fill="#2c3e50" font-size="10">Deletion: Reduced Coverage</text>
                             
                             <!-- Duplication coverage -->
                             <line x1="650" y1="420" x2="850" y2="420" stroke="#34495e" stroke-width="2"/>
                             <rect x="650" y="400" width="200" height="20" fill="#3498db" opacity="0.3"/>
                             <rect x="700" y="400" width="100" height="20" fill="#f39c12" opacity="0.7"/>
                             <text x="750" y="395" text-anchor="middle" fill="#2c3e50" font-size="10">Duplication: Increased Coverage</text>
                         </g>
                         
                         <!-- Title -->
                         <text x="450" y="30" text-anchor="middle" fill="#2c3e50" font-size="20" font-weight="bold">Evidence Categories for SV Detection</text>
                         
                         <!-- BAF explanation -->
                         <g transform="translate(50, 480)">
                             <text x="0" y="0" fill="#2c3e50" font-size="14" font-weight="bold">B-Allele Frequency (BAF):</text>
                             <text x="0" y="20" fill="#9b59b6" font-size="12">• Normal diploid: 0.5 (heterozygous) or 1.0 (homozygous)</text>
                             <text x="0" y="35" fill="#9b59b6" font-size="12">• Deletion: 0 or 1 (no 0.5)</text>
                             <text x="0" y="50" fill="#9b59b6" font-size="12">• Duplication: 0, 0.33, 0.66, or 1</text>
                         </g>
                     </svg>
                 </div>
                 
                 <h3>Detailed Evidence Type Descriptions</h3>
                 <div class="feature-grid">
                     <div class="feature-card">
                         <h4>🔗 Paired-End Evidence (PE)</h4>
                         <p><strong>Principle:</strong> Uses the expected distance and orientation between paired sequencing reads</p>
                         <p><strong>Detection:</strong> Abnormal insert sizes or orientations indicate SVs</p>
                         <p><strong>Advantages:</strong> Cost-effective, widely available data</p>
                         <p><strong>Limitations:</strong> Limited resolution, size-dependent sensitivity</p>
                         <p><strong>Examples:</strong> Deletions show larger insert sizes, duplications show abnormal orientations</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>✂️ Split Read Evidence (SR)</h4>
                         <p><strong>Principle:</strong> Reads that align to multiple locations or have gaps</p>
                         <p><strong>Detection:</strong> Precise breakpoint coordinates and SV types</p>
                         <p><strong>Advantages:</strong> High resolution, precise breakpoint detection</p>
                         <p><strong>Limitations:</strong> Requires high-quality reads, size-dependent</p>
                         <p><strong>Examples:</strong> Deletions show reads mapping to distant locations</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>📊 Read Depth Evidence (RD)</h4>
                         <p><strong>Principle:</strong> Changes in sequencing coverage indicate copy number changes</p>
                         <p><strong>Detection:</strong> Deletions show reduced coverage, duplications show increased coverage</p>
                         <p><strong>Advantages:</strong> Detects copy number changes regardless of size</p>
                         <p><strong>Limitations:</strong> Cannot detect balanced rearrangements</p>
                         <p><strong>Examples:</strong> Heterozygous deletions show ~50% coverage reduction</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>🧬 B-Allele Frequency (BAF)</h4>
                         <p><strong>Principle:</strong> Allele fraction changes indicate copy number alterations</p>
                         <p><strong>Detection:</strong> Deviations from expected 0.5 or 1.0 ratios</p>
                         <p><strong>Advantages:</strong> Provides copy number state information</p>
                         <p><strong>Limitations:</strong> Requires heterozygous sites, complex interpretation</p>
                         <p><strong>Examples:</strong> Deletions eliminate heterozygous sites, duplications create new ratios</p>
                     </div>
                 </div>
                 
                 <div class="formula-box">
                     <h4>B-Allele Frequency Calculation</h4>
                     <p>For a diploid genome, BAF is calculated as:</p>
                     $$BAF = \frac{\text{Alternate allele count}}{\text{Total allele count}}$$
                     
                     <p>Expected values:</p>
                     <ul style="text-align: left; display: inline-block;">
                         <li><strong>Normal heterozygous:</strong> BAF = 0.5</li>
                         <li><strong>Normal homozygous:</strong> BAF = 0.0 or 1.0</li>
                         <li><strong>Heterozygous deletion:</strong> BAF = 0.0 or 1.0 (no 0.5)</li>
                         <li><strong>Duplication (3 copies):</strong> BAF = 0.33, 0.66, or 1.0</li>
                     </ul>
                 </div>
                 
                 <div class="key-concept">
                     <strong>Key Insight:</strong> No single evidence type can detect all SV types reliably. The most robust SV detection combines multiple evidence types, with each providing complementary information about different aspects of structural variation.
                 </div>
             </section>
             
             <!-- Detection Methods Section -->
             <section id="detection" class="section">
                 <h2>Detection Methods and Technologies</h2>
                 
                 <p>Multiple approaches have been developed to detect structural variants, each with different strengths and limitations. The choice of method depends on the specific research question, available data, and computational resources.</p>
                 
                 <div class="svg-container">
                     <h3>SV Detection Method Comparison</h3>
                     <svg width="900" height="600" viewBox="0 0 900 600">
                         <!-- Method categories -->
                         <g>
                             <!-- Short-read mapping -->
                             <rect x="50" y="50" width="250" height="100" fill="#3498db" rx="10"/>
                             <text x="175" y="75" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Short-Read Mapping</text>
                             <text x="175" y="95" text-anchor="middle" fill="white" font-size="12">Cost-effective, widely used</text>
                             <text x="175" y="110" text-anchor="middle" fill="white" font-size="10">Recall: 10-70%</text>
                             <text x="175" y="125" text-anchor="middle" fill="white" font-size="10">False positive: up to 89%</text>
                             <text x="175" y="140" text-anchor="middle" fill="white" font-size="10">Size limit: ~50bp-10kb</text>
                             
                             <!-- Long-read mapping -->
                             <rect x="325" y="50" width="250" height="100" fill="#2ecc71" rx="10"/>
                             <text x="450" y="75" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Long-Read Mapping</text>
                             <text x="450" y="95" text-anchor="middle" fill="white" font-size="12">High resolution, better accuracy</text>
                             <text x="450" y="110" text-anchor="middle" fill="white" font-size="10">Recall: 70-90%</text>
                             <text x="450" y="125" text-anchor="middle" fill="white" font-size="10">False positive: 10-30%</text>
                             <text x="450" y="140" text-anchor="middle" fill="white" font-size="10">Size limit: 50bp-100kb+</text>
                             
                             <!-- De novo assembly -->
                             <rect x="600" y="50" width="250" height="100" fill="#e74c3c" rx="10"/>
                             <text x="725" y="75" text-anchor="middle" fill="white" font-size="16" font-weight="bold">De Novo Assembly</text>
                             <text x="725" y="95" text-anchor="middle" fill="white" font-size="12">Complete genome reconstruction</text>
                             <text x="725" y="110" text-anchor="middle" fill="white" font-size="10">Recall: 80-95%</text>
                             <text x="725" y="125" text-anchor="middle" fill="white" font-size="10">False positive: 5-20%</text>
                             <text x="725" y="140" text-anchor="middle" fill="white" font-size="10">Size limit: All sizes</text>
                         </g>
                         
                         <!-- Technology comparison -->
                         <g>
                             <!-- Short-read technologies -->
                             <rect x="50" y="200" width="250" height="80" fill="#3498db" opacity="0.7" rx="8"/>
                             <text x="175" y="220" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Short-Read Technologies</text>
                             <text x="175" y="240" text-anchor="middle" fill="white" font-size="12">• Illumina (100-300bp)</text>
                             <text x="175" y="255" text-anchor="middle" fill="white" font-size="12">• Cost: $100-500 per sample</text>
                             <text x="175" y="270" text-anchor="middle" fill="white" font-size="12">• Error rate: <0.1%</text>
                             
                             <!-- Long-read technologies -->
                             <rect x="325" y="200" width="250" height="80" fill="#2ecc71" opacity="0.7" rx="8"/>
                             <text x="450" y="220" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Long-Read Technologies</text>
                             <text x="450" y="240" text-anchor="middle" fill="white" font-size="12">• PacBio (5-50kb)</text>
                             <text x="450" y="255" text-anchor="middle" fill="white" font-size="12">• Oxford Nanopore (up to 2Mb)</text>
                             <text x="450" y="270" text-anchor="middle" fill="white" font-size="12">• Error rate: 8-20%</text>
                             
                             <!-- Alternative technologies -->
                             <rect x="600" y="200" width="250" height="80" fill="#f39c12" opacity="0.7" rx="8"/>
                             <text x="725" y="220" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Alternative Technologies</text>
                             <text x="725" y="240" text-anchor="middle" fill="white" font-size="12">• 10x Genomics (linked reads)</text>
                             <text x="725" y="255" text-anchor="middle" fill="white" font-size="12">• Hi-C (3D proximity)</text>
                             <text x="725" y="270" text-anchor="middle" fill="white" font-size="12">• Optical mapping</text>
                         </g>
                         
                         <!-- Popular tools -->
                         <g>
                             <!-- Short-read tools -->
                             <rect x="50" y="320" width="250" height="120" fill="#3498db" opacity="0.5" rx="8"/>
                             <text x="175" y="340" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Short-Read Tools</text>
                             <text x="175" y="360" text-anchor="middle" fill="white" font-size="11">• BreakDancer</text>
                             <text x="175" y="375" text-anchor="middle" fill="white" font-size="11">• DELLY</text>
                             <text x="175" y="390" text-anchor="middle" fill="white" font-size="11">• LUMPY</text>
                             <text x="175" y="405" text-anchor="middle" fill="white" font-size="11">• Manta</text>
                             <text x="175" y="420" text-anchor="middle" fill="white" font-size="11">• GRIDSS</text>
                             <text x="175" y="435" text-anchor="middle" fill="white" font-size="11">• Meta-methods: SURVIVOR</text>
                             
                             <!-- Long-read tools -->
                             <rect x="325" y="320" width="250" height="120" fill="#2ecc71" opacity="0.5" rx="8"/>
                             <text x="450" y="340" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Long-Read Tools</text>
                             <text x="450" y="360" text-anchor="middle" fill="white" font-size="11">• Sniffles (universal)</text>
                             <text x="450" y="375" text-anchor="middle" fill="white" font-size="11">• PBHoney (PacBio)</text>
                             <text x="450" y="390" text-anchor="middle" fill="white" font-size="11">• PBSV (PacBio)</text>
                             <text x="450" y="405" text-anchor="middle" fill="white" font-size="11">• SMRT-SV (PacBio)</text>
                             <text x="450" y="420" text-anchor="middle" fill="white" font-size="11">• NanoSV (ONT)</text>
                             <text x="450" y="435" text-anchor="middle" fill="white" font-size="11">• NGMLR (ONT)</text>
                             
                             <!-- Assembly tools -->
                             <rect x="600" y="320" width="250" height="120" fill="#e74c3c" opacity="0.5" rx="8"/>
                             <text x="725" y="340" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Assembly Tools</text>
                             <text x="725" y="360" text-anchor="middle" fill="white" font-size="11">• Cortex</text>
                             <text x="725" y="375" text-anchor="middle" fill="white" font-size="11">• SGVar</text>
                             <text x="725" y="390" text-anchor="middle" fill="white" font-size="11">• Assemblytics</text>
                             <text x="725" y="405" text-anchor="middle" fill="white" font-size="11">• paftools.js</text>
                             <text x="725" y="420" text-anchor="middle" fill="white" font-size="11">• SMARTie-SV</text>
                             <text x="725" y="435" text-anchor="middle" fill="white" font-size="11">• Trio-based methods</text>
                         </g>
                         
                         <!-- Cost vs performance -->
                         <g>
                             <text x="450" y="480" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Cost vs Performance Trade-offs</text>
                             
                             <!-- Cost axis -->
                             <line x1="100" y1="520" x2="800" y2="520" stroke="#34495e" stroke-width="2"/>
                             <text x="450" y="540" text-anchor="middle" fill="#2c3e50" font-size="12">Cost (Low → High)</text>
                             
                             <!-- Performance axis -->
                             <line x1="80" y1="500" x2="80" y2="520" stroke="#34495e" stroke-width="2"/>
                             <text x="60" y="510" text-anchor="middle" fill="#2c3e50" font-size="12" transform="rotate(-90, 60, 510)">Performance (Low → High)</text>
                             
                             <!-- Data points -->
                             <circle cx="150" cy="510" r="8" fill="#3498db"/>
                             <text x="150" y="530" text-anchor="middle" fill="#2c3e50" font-size="10">Short-read</text>
                             
                             <circle cx="300" cy="505" r="8" fill="#2ecc71"/>
                             <text x="300" y="530" text-anchor="middle" fill="#2c3e50" font-size="10">Long-read</text>
                             
                             <circle cx="600" cy="500" r="8" fill="#e74c3c"/>
                             <text x="600" y="530" text-anchor="middle" fill="#2c3e50" font-size="10">Assembly</text>
                         </g>
                         
                         <!-- Title -->
                         <text x="450" y="30" text-anchor="middle" fill="#2c3e50" font-size="20" font-weight="bold">SV Detection Methods Comparison</text>
                     </svg>
                 </div>
                 
                 <h3>Method Selection Guidelines</h3>
                 <div class="feature-grid">
                     <div class="feature-card">
                         <h4>🔬 Research Applications</h4>
                         <p><strong>Population Studies:</strong> Short-read mapping for cost-effective large cohorts</p>
                         <p><strong>Clinical Research:</strong> Long-read mapping for high accuracy</p>
                         <p><strong>Novel Discovery:</strong> De novo assembly for complete characterization</p>
                         <p><strong>Validation Studies:</strong> Multiple methods for confirmation</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>💰 Cost Considerations</h4>
                         <p><strong>Short-read:</strong> $100-500 per sample</p>
                         <p><strong>Long-read:</strong> $1000-5000 per sample</p>
                         <p><strong>Assembly:</strong> $2000-10000 per sample</p>
                         <p><strong>Computational:</strong> Assembly requires most resources</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>🎯 Accuracy Requirements</h4>
                         <p><strong>High Precision:</strong> Long-read or assembly methods</p>
                         <p><strong>High Recall:</strong> Meta-methods combining multiple tools</p>
                         <p><strong>Clinical Use:</strong> Validated pipelines with known performance</p>
                         <p><strong>Research Use:</strong> Can tolerate higher false positive rates</p>
                     </div>
                     
                     <div class="feature-card">
                         <h4>⚡ Performance Characteristics</h4>
                         <p><strong>Speed:</strong> Short-read methods fastest</p>
                         <p><strong>Memory:</strong> Assembly methods most demanding</p>
                         <p><strong>Scalability:</strong> Short-read scales to thousands of samples</p>
                         <p><strong>Resolution:</strong> Long-read provides best breakpoint accuracy</p>
                     </div>
                 </div>
                 
                 <div class="performance-table">
                     <table>
                         <thead>
                             <tr>
                                 <th>Method</th>
                                 <th>Recall (%)</th>
                                 <th>Precision (%)</th>
                                 <th>Cost per Sample</th>
                                 <th>Best For</th>
                                 <th>Limitations</th>
                             </tr>
                         </thead>
                         <tbody>
                             <tr>
                                 <td><strong>Short-read mapping</strong></td>
                                 <td>10-70</td>
                                 <td>30-90</td>
                                 <td>$100-500</td>
                                 <td>Large cohorts, known SVs</td>
                                 <td>Misses large insertions, complex SVs</td>
                             </tr>
                             <tr>
                                 <td><strong>Long-read mapping</strong></td>
                                 <td>70-90</td>
                                 <td>70-90</td>
                                 <td>$1000-5000</td>
                                 <td>High accuracy, novel SVs</td>
                                 <td>Higher cost, error rates</td>
                             </tr>
                             <tr>
                                 <td><strong>De novo assembly</strong></td>
                                 <td>80-95</td>
                                 <td>80-95</td>
                                 <td>$2000-10000</td>
                                 <td>Complete characterization</td>
                                 <td>Very expensive, computational intensive</td>
                             </tr>
                             <tr>
                                 <td><strong>Meta-methods</strong></td>
                                 <td>60-80</td>
                                 <td>70-85</td>
                                 <td>Variable</td>
                                 <td>Balanced approach</td>
                                 <td>Complex interpretation</td>
                             </tr>
                         </tbody>
                     </table>
                 </div>
             </section> 