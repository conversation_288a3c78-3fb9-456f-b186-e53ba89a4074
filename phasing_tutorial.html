<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whole-Genome Haplotyping: A Comprehensive Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .haplotype {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .chromosome {
            stroke: #34495e;
            stroke-width: 3;
            fill: none;
        }
        
        .snp-marker {
            fill: #e74c3c;
            stroke: #c0392b;
            stroke-width: 1;
        }
        
        .phase-line {
            stroke: #27ae60;
            stroke-width: 2;
            fill: none;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Whole-Genome Haplotyping</h1>
            <p>A Comprehensive Tutorial on Methods and Clinical Applications</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction to Haplotyping</a></li>
                <li><a href="#molecular">2. Molecular Haplotyping Methods</a></li>
                <li><a href="#assembly">3. Haplotype Assembly Techniques</a></li>
                <li><a href="#genetic">4. Genetic Analysis Approaches</a></li>
                <li><a href="#population">5. Population-Based Inference</a></li>
                <li><a href="#combination">6. Combination Strategies</a></li>
                <li><a href="#clinical">7. Clinical Applications</a></li>
                <li><a href="#quality">8. Quality Metrics and Evaluation</a></li>
                <li><a href="#future">9. Future Directions</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction to Haplotyping -->
        <div id="introduction" class="section">
            <h2>1. Introduction to Haplotyping</h2>

            <div class="highlight">
                <strong>Key Concept:</strong> Haplotyping (or phasing) is the process of determining which alleles are co-located on the same physical chromosome, transforming unphased genotype data into phased haplotype information essential for personalized medicine.
            </div>

            <h3>What is a Haplotype?</h3>

            <div class="math-box">
                <h4>Definition and Scope</h4>
                <p><strong>Haplotype:</strong> A contiguous subset of information contained in a molecule of DNA - the actual sequence inherited from one parent spanning one or more genes in a specific genomic region.</p>

                <p><strong>Chromosome Haplotype:</strong> The longest possible haplotype - the full sequence of a chromosome that an individual inherited from one parent.</p>

                <p><strong>Mathematical Representation:</strong></p>
                <p>For a diploid individual with variants at positions 1, 2, ..., n:</p>
                <ul>
                    <li><strong>Genotype:</strong> G = {(a₁,b₁), (a₂,b₂), ..., (aₙ,bₙ)}</li>
                    <li><strong>Haplotype Pair:</strong> H = {H₁, H₂} where H₁ = {a₁, a₂, ..., aₙ} and H₂ = {b₁, b₂, ..., bₙ}</li>
                </ul>

                <p><strong>Phase Ambiguity:</strong> For k heterozygous sites, there are 2^(k-1) possible phase configurations</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        From Genotypes to Haplotypes: The Phasing Challenge
                    </text>

                    <!-- Unphased genotype representation -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Unphased Genotype Data
                        </text>

                        <!-- Chromosome pair (unphased) -->
                        <g transform="translate(50, 40)">
                            <line x1="0" y1="20" x2="200" y2="20" stroke="#95a5a6" stroke-width="8"/>
                            <line x1="0" y1="40" x2="200" y2="40" stroke="#95a5a6" stroke-width="8"/>

                            <!-- SNP positions -->
                            <circle cx="40" cy="20" r="6" fill="#e74c3c"/>
                            <circle cx="40" cy="40" r="6" fill="#3498db"/>
                            <text x="40" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">A/T</text>

                            <circle cx="80" cy="20" r="6" fill="#e74c3c"/>
                            <circle cx="80" cy="40" r="6" fill="#3498db"/>
                            <text x="80" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">G/C</text>

                            <circle cx="120" cy="20" r="6" fill="#e74c3c"/>
                            <circle cx="120" cy="40" r="6" fill="#3498db"/>
                            <text x="120" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">T/A</text>

                            <circle cx="160" cy="20" r="6" fill="#e74c3c"/>
                            <circle cx="160" cy="40" r="6" fill="#3498db"/>
                            <text x="160" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">C/G</text>
                        </g>

                        <text x="150" y="120" text-anchor="middle" font-size="12" fill="#666">
                            Which alleles are on the same chromosome?
                        </text>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(350, 180)">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">PHASING</text>
                    </g>

                    <!-- Phased haplotype representation -->
                    <g transform="translate(500, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Phased Haplotype Data
                        </text>

                        <!-- Maternal haplotype -->
                        <g transform="translate(50, 40)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#3498db">Maternal:</text>
                            <line x1="70" y1="10" x2="270" y2="10" class="chromosome"/>
                            <circle cx="90" cy="10" r="4" fill="#e74c3c"/>
                            <text x="90" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">A</text>
                            <circle cx="130" cy="10" r="4" fill="#e74c3c"/>
                            <text x="130" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">G</text>
                            <circle cx="170" cy="10" r="4" fill="#e74c3c"/>
                            <text x="170" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">T</text>
                            <circle cx="210" cy="10" r="4" fill="#e74c3c"/>
                            <text x="210" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">C</text>
                        </g>

                        <!-- Paternal haplotype -->
                        <g transform="translate(50, 80)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#e74c3c">Paternal:</text>
                            <line x1="70" y1="10" x2="270" y2="10" class="chromosome"/>
                            <circle cx="90" cy="10" r="4" fill="#3498db"/>
                            <text x="90" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">T</text>
                            <circle cx="130" cy="10" r="4" fill="#3498db"/>
                            <text x="130" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">C</text>
                            <circle cx="170" cy="10" r="4" fill="#3498db"/>
                            <text x="170" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">A</text>
                            <circle cx="210" cy="10" r="4" fill="#3498db"/>
                            <text x="210" y="25" text-anchor="middle" font-size="10" fill="#2c3e50">G</text>
                        </g>

                        <text x="150" y="120" text-anchor="middle" font-size="12" fill="#666">
                            Alleles assigned to specific chromosomes
                        </text>
                    </g>

                    <!-- Three main approaches -->
                    <g transform="translate(100, 250)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Three Main Phasing Approaches
                        </text>

                        <!-- Molecular -->
                        <g transform="translate(0, 40)">
                            <rect width="180" height="100" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Molecular</text>
                            <text x="90" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Direct observation</text>
                            <text x="90" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Single molecules</text>
                            <text x="90" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">High confidence</text>
                            <text x="90" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">Limited range</text>
                        </g>

                        <!-- Genetic -->
                        <g transform="translate(210, 40)">
                            <rect width="180" height="100" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">Genetic</text>
                            <text x="90" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Family pedigrees</text>
                            <text x="90" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Mendelian rules</text>
                            <text x="90" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Chromosome-length</text>
                            <text x="90" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">Requires families</text>
                        </g>

                        <!-- Population -->
                        <g transform="translate(420, 40)">
                            <rect width="180" height="100" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Population</text>
                            <text x="90" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Statistical inference</text>
                            <text x="90" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Haplotype frequencies</text>
                            <text x="90" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Common variants</text>
                            <text x="90" y="90" text-anchor="middle" font-size="11" fill="#2c3e50">Short blocks</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Why Haplotyping Matters</h3>

            <div class="info-box">
                <h4>Critical Applications in Genomic Medicine</h4>
                <ul class="step-list">
                    <li><strong>Compound Heterozygosity:</strong> Distinguishing whether two deleterious variants are in cis (same chromosome) or trans (different chromosomes) - crucial for recessive disease diagnosis</li>
                    <li><strong>Regulatory-Coding Interactions:</strong> Understanding how regulatory variants affect the expression of specific coding alleles</li>
                    <li><strong>Pharmacogenomics:</strong> Drug response often depends on specific haplotype combinations rather than individual variants</li>
                    <li><strong>Cancer Genomics:</strong> Detecting loss of heterozygosity and allele-specific expression in tumors</li>
                    <li><strong>Transplantation Medicine:</strong> HLA haplotyping for organ and tissue compatibility</li>
                </ul>
            </div>

            <h3>The Scale of the Challenge</h3>

            <div class="math-box">
                <h4>Computational Complexity</h4>
                <p><strong>Exponential Growth:</strong> The number of possible phase configurations grows exponentially with the number of heterozygous sites.</p>

                <p><strong>For k heterozygous SNPs:</strong></p>
                $$\text{Possible configurations} = 2^{k-1}$$

                <p><strong>Real-world examples:</strong></p>
                <ul>
                    <li>10 heterozygous SNPs: 512 configurations</li>
                    <li>20 heterozygous SNPs: 524,288 configurations</li>
                    <li>1,000 heterozygous SNPs: ~10^300 configurations</li>
                </ul>

                <p><strong>Human genome context:</strong> A typical individual is heterozygous at ~3-5 million sites, making brute-force enumeration computationally impossible.</p>
            </div>

            <div class="warning-box">
                <h4>Historical Context and Current Challenges</h4>
                <p>The shift from long-read sequencing (Human Genome Project era) to short-read sequencing for cost efficiency inadvertently made haplotyping more difficult. Current efforts focus on:</p>
                <ul>
                    <li>Developing clever computational methods to extract phase information from short reads</li>
                    <li>Advancing long-read sequencing technologies</li>
                    <li>Creating hybrid approaches that combine multiple data sources</li>
                    <li>Improving sequencing accuracy to reduce phase errors</li>
                </ul>
            </div>
        </div>

        <!-- Section 2: Molecular Haplotyping Methods -->
        <div id="molecular" class="section">
            <h2>2. Molecular Haplotyping Methods</h2>

            <div class="highlight">
                <strong>Direct Observation Principle:</strong> Molecular haplotyping involves the direct observation of alleles on single DNA molecules, providing the highest confidence phasing information but with limitations in range and throughput.
            </div>

            <h3>Same-Read Molecular Haplotyping</h3>

            <div class="math-box">
                <h4>Fundamental Approach</h4>
                <p><strong>Core Principle:</strong> When two variants are observed in the same physical read or paired reads from the same molecule, they can be directly phased with high confidence.</p>

                <p><strong>Phasing Range:</strong> Limited by sequencing read length</p>
                <ul>
                    <li><strong>Short reads (Illumina):</strong> 150-300 bp</li>
                    <li><strong>Long reads (PacBio/Nanopore):</strong> 10-100 kb</li>
                    <li><strong>Ultra-long reads:</strong> >1 Mb possible</li>
                </ul>

                <p><strong>Error Sources:</strong> Primarily sequencing errors rather than algorithmic inference errors</p>

                <p><strong>Confidence Level:</strong> Very high - direct molecular observation</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Molecular Haplotyping: From Single Reads to Chromosome Separation
                    </text>

                    <!-- Same-read phasing -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Same-Read Phasing
                        </text>

                        <!-- DNA molecule -->
                        <line x1="50" y1="50" x2="250" y2="50" stroke="#34495e" stroke-width="4"/>

                        <!-- Sequencing read -->
                        <rect x="80" y="40" width="120" height="20" fill="#3498db" opacity="0.7" stroke="#2980b9" stroke-width="2"/>
                        <text x="140" y="55" text-anchor="middle" font-size="10" fill="white">Sequencing Read</text>

                        <!-- SNPs within read -->
                        <circle cx="100" cy="50" r="4" fill="#e74c3c"/>
                        <text x="100" y="75" text-anchor="middle" font-size="9" fill="#e74c3c">A</text>
                        <circle cx="140" cy="50" r="4" fill="#e74c3c"/>
                        <text x="140" y="75" text-anchor="middle" font-size="9" fill="#e74c3c">G</text>
                        <circle cx="180" cy="50" r="4" fill="#e74c3c"/>
                        <text x="180" y="75" text-anchor="middle" font-size="9" fill="#e74c3c">T</text>

                        <!-- SNPs outside read -->
                        <circle cx="220" cy="50" r="4" fill="#95a5a6"/>
                        <text x="220" y="75" text-anchor="middle" font-size="9" fill="#95a5a6">?</text>

                        <text x="150" y="95" text-anchor="middle" font-size="11" fill="#666">
                            Direct phasing within read length
                        </text>
                    </g>

                    <!-- Long-read phasing -->
                    <g transform="translate(400, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Long-Read Phasing
                        </text>

                        <!-- DNA molecule -->
                        <line x1="50" y1="50" x2="250" y2="50" stroke="#34495e" stroke-width="4"/>

                        <!-- Long sequencing read -->
                        <rect x="60" y="40" width="180" height="20" fill="#27ae60" opacity="0.7" stroke="#229954" stroke-width="2"/>
                        <text x="150" y="55" text-anchor="middle" font-size="10" fill="white">Long Read (10-100kb)</text>

                        <!-- Multiple SNPs within long read -->
                        <circle cx="80" cy="50" r="4" fill="#e74c3c"/>
                        <circle cx="110" cy="50" r="4" fill="#e74c3c"/>
                        <circle cx="140" cy="50" r="4" fill="#e74c3c"/>
                        <circle cx="170" cy="50" r="4" fill="#e74c3c"/>
                        <circle cx="200" cy="50" r="4" fill="#e74c3c"/>
                        <circle cx="230" cy="50" r="4" fill="#e74c3c"/>

                        <text x="150" y="95" text-anchor="middle" font-size="11" fill="#666">
                            Extended phasing range
                        </text>
                    </g>

                    <!-- Chromosome separation methods -->
                    <g transform="translate(100, 180)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Chromosome Separation Techniques
                        </text>

                        <!-- Microdissection -->
                        <g transform="translate(0, 40)">
                            <rect width="140" height="80" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Microdissection</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Manual isolation</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Metaphase cells</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Labor intensive</text>
                        </g>

                        <!-- FACS -->
                        <g transform="translate(160, 40)">
                            <rect width="140" height="80" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">FACS</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Flow cytometry</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Automated sorting</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">High throughput</text>
                        </g>

                        <!-- Microfluidics -->
                        <g transform="translate(320, 40)">
                            <rect width="140" height="80" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Microfluidics</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Single-cell capture</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Precise control</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Complex devices</text>
                        </g>

                        <!-- Sperm sequencing -->
                        <g transform="translate(480, 40)">
                            <rect width="140" height="80" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Sperm Sequencing</text>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">Naturally haploid</text>
                            <text x="70" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Males only</text>
                            <text x="70" y="65" text-anchor="middle" font-size="10" fill="#2c3e50">Simple approach</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Advanced Molecular Technologies</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Technology</th>
                        <th>Company</th>
                        <th>Approach</th>
                        <th>Range</th>
                        <th>Key Advantage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Optical Mapping</strong></td>
                        <td>BioNano Genomics</td>
                        <td>Fluorescent labeling + nanochannels</td>
                        <td>Genome-wide</td>
                        <td>Structural variation detection</td>
                    </tr>
                    <tr>
                        <td><strong>Solid-State Detection</strong></td>
                        <td>NabSys</td>
                        <td>Tagged DNA through nanodetectors</td>
                        <td>Very long range</td>
                        <td>Single molecule analysis</td>
                    </tr>
                    <tr>
                        <td><strong>Hi-C Phasing</strong></td>
                        <td>Various</td>
                        <td>Chromatin cross-linking</td>
                        <td>Chromosome-wide</td>
                        <td>Exploits nuclear organization</td>
                    </tr>
                    <tr>
                        <td><strong>Long Fragment Read</strong></td>
                        <td>Complete Genomics</td>
                        <td>DNA dilution + short reads</td>
                        <td>~1 Mb</td>
                        <td>Cost-effective scaling</td>
                    </tr>
                    <tr>
                        <td><strong>Moleculo</strong></td>
                        <td>Illumina</td>
                        <td>SLRH + population data</td>
                        <td>Multi-gene</td>
                        <td>Hybrid approach</td>
                    </tr>
                </tbody>
            </table>

            <h3>Dilution-Based Methods</h3>

            <div class="info-box">
                <h4>Extreme Dilution Strategy</h4>
                <p><strong>Principle:</strong> Dilute genomic DNA to very low concentrations so that each reaction contains only a few long DNA molecules, then sequence with short reads.</p>

                <ol class="step-list">
                    <li><strong>DNA Fragmentation:</strong> Generate long DNA fragments (10-100 kb)</li>
                    <li><strong>Extreme Dilution:</strong> Dilute to ~1-10 molecules per reaction well</li>
                    <li><strong>Amplification:</strong> Amplify individual molecules in separate reactions</li>
                    <li><strong>Short-Read Sequencing:</strong> Sequence amplified products with standard platforms</li>
                    <li><strong>Assembly:</strong> Assemble overlapping fragments into longer haplotypes</li>
                </ol>

                <p><strong>Advantages:</strong></p>
                <ul>
                    <li>Uses existing short-read infrastructure</li>
                    <li>Achieves long-range phasing (up to 1 Mb)</li>
                    <li>Avoids repetitive sequence complications</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Limitations of Molecular Methods</h4>
                <ul>
                    <li><strong>Coverage Requirements:</strong> Need sufficient depth to detect heterozygous positions</li>
                    <li><strong>Repetitive Sequences:</strong> Difficult to phase through long repeats</li>
                    <li><strong>Technical Complexity:</strong> Many methods require specialized equipment</li>
                    <li><strong>Cost Considerations:</strong> Often more expensive than computational approaches</li>
                    <li><strong>Variant Type Limitations:</strong> Some methods cannot assay all variant types (e.g., trinucleotide repeats)</li>
                </ul>
            </div>
        </div>

        <!-- Section 3: Haplotype Assembly Techniques -->
        <div id="assembly" class="section">
            <h2>3. Haplotype Assembly Techniques</h2>

            <div class="highlight">
                <strong>Assembly Principle:</strong> Overlapping molecular haplotypes can be computationally assembled into longer haplotypes, extending the range beyond individual sequencing reads while introducing potential inference errors.
            </div>

            <h3>The SNP Haplotyping Problem</h3>

            <div class="math-box">
                <h4>Mathematical Formulation</h4>
                <p><strong>Input:</strong> Set of sequencing reads R = {r₁, r₂, ..., rₙ} covering heterozygous SNP positions</p>
                <p><strong>Constraint:</strong> All reads must be partitioned into exactly two bins (maternal and paternal)</p>
                <p><strong>Objective:</strong> Minimize the number of SNP calls that contradict the partition</p>

                <p><strong>Minimum Error Correction (MEC) Problem:</strong></p>
                $$\text{MEC} = \min \sum_{i=1}^{n} \sum_{j=1}^{m} w_{ij} \cdot x_{ij}$$

                <p>Where:</p>
                <ul>
                    <li>w_{ij} = weight of disagreement between read i and haplotype j</li>
                    <li>x_{ij} = 1 if read i contradicts haplotype j, 0 otherwise</li>
                </ul>
            </div>

            <h3>Key Assembly Algorithms</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Algorithm</th>
                        <th>Approach</th>
                        <th>Strengths</th>
                        <th>Best Use Case</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>HASH</strong></td>
                        <td>MCMC + graph partitioning</td>
                        <td>Handles complex scenarios</td>
                        <td>High-coverage data</td>
                    </tr>
                    <tr>
                        <td><strong>HapCut</strong></td>
                        <td>Max-cut optimization</td>
                        <td>Optimal MEC solution</td>
                        <td>Fragment-based data</td>
                    </tr>
                    <tr>
                        <td><strong>ReFHap</strong></td>
                        <td>Reliable and fast</td>
                        <td>Good accuracy/speed balance</td>
                        <td>Fosmid pooling</td>
                    </tr>
                    <tr>
                        <td><strong>H-BOP</strong></td>
                        <td>Branch and bound</td>
                        <td>Improved speed</td>
                        <td>Large datasets</td>
                    </tr>
                    <tr>
                        <td><strong>MixSH</strong></td>
                        <td>Mixed integer programming</td>
                        <td>High error tolerance</td>
                        <td>Noisy data</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 4: Genetic Analysis Approaches -->
        <div id="genetic" class="section">
            <h2>4. Genetic Analysis Approaches</h2>

            <div class="highlight">
                <strong>Mendelian Power:</strong> Family pedigrees provide deterministic phasing rules based on inheritance patterns, enabling chromosome-length haplotypes with near-perfect accuracy.
            </div>

            <h3>Family-Based Phasing Rules</h3>

            <div class="math-box">
                <h4>Trio Phasing Logic</h4>
                <p><strong>Basic Rule:</strong> In a parent-child trio, the child inherits exactly one allele from each parent at every position.</p>

                <p><strong>Phasing Scenarios:</strong></p>
                <ul>
                    <li><strong>Parent homozygous:</strong> Inherited allele is known → phase determined</li>
                    <li><strong>Child homozygous:</strong> Both parents contributed same allele → phase determined</li>
                    <li><strong>All heterozygous:</strong> Phase ambiguous → requires additional information</li>
                </ul>

                <p><strong>Probability of ambiguous site in trio:</strong></p>
                $$P(\text{ambiguous}) = P(\text{all heterozygous}) = (2pq)^3$$

                <p>For common variants (p = q = 0.5): P ≈ 12.5%</p>
            </div>

            <h3>Quartet Analysis</h3>

            <div class="info-box">
                <h4>Family Quartet Advantages</h4>
                <p><strong>Configuration:</strong> Two parents + two children</p>

                <ol class="step-list">
                    <li><strong>Inheritance State Vectors:</strong> Track which parental chromosome each child inherited</li>
                    <li><strong>Recombination Detection:</strong> Identify crossover points between siblings</li>
                    <li><strong>Error Detection:</strong> Mendelian inheritance errors and state consistency errors</li>
                    <li><strong>Complete Phasing:</strong> Achieve chromosome-length haplotypes for all family members</li>
                </ol>

                <p><strong>Haploscribe Software:</strong> Implements parsimony approach with Hidden Markov Models for quartet phasing</p>
            </div>
        </div>

        <!-- Section 5: Population-Based Inference -->
        <div id="population" class="section">
            <h2>5. Population-Based Inference</h2>

            <div class="highlight">
                <strong>Statistical Foundation:</strong> Population methods leverage shared ancestry and haplotype frequency patterns to infer the most likely phase configurations, but are limited to common variants and short blocks.
            </div>

            <h3>Core Algorithms</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Algorithm</th>
                        <th>Scalability</th>
                        <th>Accuracy</th>
                        <th>Best Application</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>PHASE</strong></td>
                        <td>Coalescent model</td>
                        <td>≤100 markers</td>
                        <td>Very high</td>
                        <td>Small regions</td>
                    </tr>
                    <tr>
                        <td><strong>fastPHASE</strong></td>
                        <td>Haplotype clustering</td>
                        <td>Genome-wide</td>
                        <td>Good</td>
                        <td>Arrays</td>
                    </tr>
                    <tr>
                        <td><strong>BEAGLE</strong></td>
                        <td>Adaptive clustering</td>
                        <td>Very large</td>
                        <td>Good</td>
                        <td>Large cohorts</td>
                    </tr>
                    <tr>
                        <td><strong>SHAPEIT</strong></td>
                        <td>Segmented HMM</td>
                        <td>Chromosome-wide</td>
                        <td>Very good</td>
                        <td>Population studies</td>
                    </tr>
                    <tr>
                        <td><strong>IMPUTE2</strong></td>
                        <td>Template selection</td>
                        <td>Large</td>
                        <td>Very good</td>
                        <td>Imputation + phasing</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning-box">
                <h4>Population Method Limitations</h4>
                <ul>
                    <li><strong>Common Variants Only:</strong> Cannot phase rare or private variants</li>
                    <li><strong>Short Range:</strong> Limited to thousands of bases due to recombination</li>
                    <li><strong>Population Matching:</strong> Requires well-characterized reference populations</li>
                    <li><strong>Error Rates:</strong> Too high for medical applications (~1-5% switch error rate)</li>
                    <li><strong>Rare Combinations:</strong> Incorrectly phases medically important rare haplotypes</li>
                </ul>
            </div>
        </div>

        <!-- Section 6: Combination Strategies -->
        <div id="combination" class="section">
            <h2>6. Combination Strategies</h2>

            <div class="highlight">
                <strong>Hybrid Approaches:</strong> Combining molecular, genetic, and population methods leverages the strengths of each approach while minimizing individual weaknesses.
            </div>

            <h3>Successful Hybrid Methods</h3>

            <div class="info-box">
                <h4>Integration Strategies</h4>
                <ul class="step-list">
                    <li><strong>HARSH:</strong> Combines haplotype assembly with population reference datasets</li>
                    <li><strong>Prism:</strong> Statistical long-read haplotyping with population information</li>
                    <li><strong>PPHS:</strong> Perfect phylogeny model combining population and molecular data</li>
                    <li><strong>SHAPEIT2:</strong> Incorporates sequence read information into population models</li>
                </ul>
            </div>
        </div>

        <!-- Section 7: Clinical Applications -->
        <div id="clinical" class="section">
            <h2>7. Clinical Applications</h2>

            <div class="highlight">
                <strong>Medical Impact:</strong> Whole-genome haplotyping enables precision medicine by revealing compound heterozygosity, regulatory-coding interactions, and allele-specific effects crucial for diagnosis and treatment.
            </div>

            <h3>Key Clinical Applications</h3>

            <div class="info-box">
                <h4>Diagnostic Applications</h4>
                <ul class="step-list">
                    <li><strong>Compound Heterozygosity:</strong> Identifying recessive disease causes (e.g., cystic fibrosis, Miller syndrome)</li>
                    <li><strong>HLA Typing:</strong> Organ transplantation compatibility and autoimmune disease risk</li>
                    <li><strong>Pharmacogenomics:</strong> Drug response prediction based on haplotype combinations</li>
                    <li><strong>Cancer Genomics:</strong> Loss of heterozygosity detection and allele-specific expression</li>
                    <li><strong>Prenatal Diagnosis:</strong> Fetal haplotype inference from maternal plasma</li>
                </ul>
            </div>

            <h3>Regulatory-Coding Interactions</h3>

            <div class="math-box">
                <h4>Cis-Regulatory Effects</h4>
                <p><strong>Scenario:</strong> Regulatory variant affects expression of coding variant on same chromosome</p>

                <p><strong>Possible Outcomes:</strong></p>
                <ul>
                    <li><strong>Protective:</strong> High expression of normal protein + low expression of defective protein</li>
                    <li><strong>Pathogenic:</strong> Low expression of normal protein + high expression of defective protein</li>
                </ul>

                <p><strong>Clinical Example:</strong> Bernard-Soulier syndrome with GP1BB promoter mutation in cis with coding deletion</p>
            </div>
        </div>

        <!-- Section 8: Quality Metrics -->
        <div id="quality" class="section">
            <h2>8. Quality Metrics and Evaluation</h2>

            <div class="highlight">
                <strong>Standardized Assessment:</strong> Quality metrics are essential for comparing methods and ensuring clinical-grade accuracy, with switch error rate being the most informative measure.
            </div>

            <h3>Key Quality Metrics</h3>

            <div class="math-box">
                <h4>Switch Error Rate</h4>
                <p><strong>Definition:</strong> Proportion of positions where the phase relationship with the previous heterozygous site is incorrect</p>

                $$\text{Switch Error Rate} = \frac{\text{Number of phase switches required}}{\text{Number of heterozygous sites} - 1}$$

                <p><strong>Types of Switch Errors:</strong></p>
                <ul>
                    <li><strong>High-frequency:</strong> Isolated single-site errors (less impact on long-range accuracy)</li>
                    <li><strong>Low-frequency:</strong> Block-level errors (major impact on chromosome-scale accuracy)</li>
                </ul>

                <p><strong>Completeness:</strong></p>
                $$\text{Completeness} = \frac{\text{Number of phased positions}}{\text{Total number of heterozygous positions}}$$
            </div>
        </div>

        <!-- Section 9: Future Directions -->
        <div id="future" class="section">
            <h2>9. Future Directions</h2>

            <div class="highlight">
                <strong>Technological Convergence:</strong> The future of haplotyping lies in integrating improved sequencing technologies, advanced algorithms, and comprehensive clinical databases to enable routine medical applications.
            </div>

            <h3>Emerging Technologies</h3>

            <div class="info-box">
                <h4>Next-Generation Approaches</h4>
                <ul class="step-list">
                    <li><strong>Long-Read Sequencing:</strong> PacBio HiFi and Oxford Nanopore achieving >99% accuracy</li>
                    <li><strong>Single-Cell Methods:</strong> Chromosome-specific amplification and sequencing</li>
                    <li><strong>Hi-C Integration:</strong> Chromatin conformation for long-range phase information</li>
                    <li><strong>Machine Learning:</strong> Deep learning for complex phasing scenarios</li>
                    <li><strong>Real-Time Analysis:</strong> Streaming algorithms for immediate clinical results</li>
                </ul>
            </div>

            <h3>Clinical Implementation Needs</h3>

            <div class="warning-box">
                <h4>Requirements for Medical Adoption</h4>
                <ul>
                    <li><strong>Error Rate Reduction:</strong> Focus on improving sequencing accuracy rather than just algorithms</li>
                    <li><strong>Standardization:</strong> Common quality metrics and evaluation frameworks</li>
                    <li><strong>Cost Reduction:</strong> Commoditization of haplotyping technologies</li>
                    <li><strong>Clinical Databases:</strong> Haplotype-disease association repositories (like ClinVar for variants)</li>
                    <li><strong>Regulatory Approval:</strong> FDA/CE marking for clinical diagnostic applications</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>The Paradigm Shift</h4>
                <p><strong>Current View:</strong> 22 autosomes + 2 sex chromosomes = 24 chromosomes</p>
                <p><strong>Future View:</strong> 44 autosomes + 2 sex chromosomes = 46 haplotypes</p>

                <p><strong>Medical Transformation:</strong></p>
                <p>From: "What is the function of gene X in this patient?"</p>
                <p>To: "What are the functions of each allele of gene X in this patient?"</p>

                <p><strong>Impact:</strong> Enable true personalized medicine based on individual haplotype profiles</p>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Initialize MathJax after page load
        window.addEventListener('load', function() {
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        });
    </script>
</body>
</html>
