<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MuTect: Sensitive Detection of Somatic Point Mutations</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .outline {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .outline h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .outline li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .outline li:last-child {
            border-bottom: none;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .performance-metric {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-weight: bold;
        }
        
        .performance-metric.good {
            background: #27ae60;
        }
        
        .performance-metric.medium {
            background: #f39c12;
        }
        
        .nav-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .nav-button {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MuTect: Sensitive Detection of Somatic Point Mutations</h1>
            <p>Mathematical Methods and Background for Cancer Genomics</p>
        </div>
        
        <div class="outline">
            <h2>📋 Presentation Outline</h2>
            <ul>
                <li><strong>1. Introduction & Problem Statement</strong> - Why detecting somatic mutations is challenging</li>
                <li><strong>2. Mathematical Foundation</strong> - Bayesian classification and likelihood models</li>
                <li><strong>3. Core Algorithm</strong> - Variant detection using LOD scores</li>
                <li><strong>4. Filtering Methods</strong> - Reducing false positives through systematic filters</li>
                <li><strong>5. Benchmarking Approaches</strong> - Virtual tumors and down-sampling validation</li>
                <li><strong>6. Performance Analysis</strong> - Sensitivity and specificity metrics</li>
                <li><strong>7. Comparison with Other Methods</strong> - Competitive analysis</li>
                <li><strong>8. Clinical Applications</strong> - Real-world impact and validation</li>
            </ul>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-button" onclick="showSection(1)">Start Presentation</button>
        </div>
        
        <!-- Section 1: Introduction -->
        <div id="section1" class="section hidden">
            <h2>1. Introduction & Problem Statement</h2>
            
            <div class="highlight">
                <strong>Key Challenge:</strong> Detecting rare somatic mutations (0.1-100 per megabase) in impure and heterogeneous cancer samples where mutations may be present in only a small fraction of cells.
            </div>
            
            <h3>The Mutation Detection Challenge</h3>
            <p>Somatic single-nucleotide substitutions are crucial for understanding cancer, but they present several detection challenges:</p>
            
            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Challenges in Somatic Mutation Detection
                    </text>
                    
                    <!-- Challenge 1: Low Frequency -->
                    <g transform="translate(50, 80)">
                        <rect width="150" height="80" fill="#e74c3c" rx="10" opacity="0.8"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Low Frequency</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">0.1-100 mutations</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">per megabase</text>
                    </g>
                    
                    <!-- Challenge 2: Tumor Heterogeneity -->
                    <g transform="translate(250, 80)">
                        <rect width="150" height="80" fill="#f39c12" rx="10" opacity="0.8"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Tumor Heterogeneity</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">Subclonal mutations</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">Variable allele fractions</text>
                    </g>
                    
                    <!-- Challenge 3: Normal Contamination -->
                    <g transform="translate(450, 80)">
                        <rect width="150" height="80" fill="#9b59b6" rx="10" opacity="0.8"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Normal Contamination</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">Mixed cell populations</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">Reduced purity</text>
                    </g>
                    
                    <!-- Challenge 4: Sequencing Errors -->
                    <g transform="translate(650, 80)">
                        <rect width="100" height="80" fill="#34495e" rx="10" opacity="0.8"/>
                        <text x="50" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Sequencing</text>
                        <text x="50" y="40" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Errors</text>
                        <text x="50" y="60" text-anchor="middle" font-size="10" fill="white">False positives</text>
                    </g>
                    
                    <!-- Allele Fraction Visualization -->
                    <g transform="translate(50, 200)">
                        <text x="350" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Allele Fraction Impact on Detection
                        </text>
                        
                        <!-- High Allele Fraction (Easy to detect) -->
                        <g transform="translate(0, 40)">
                            <text x="0" y="15" font-size="12" fill="#27ae60">High AF (0.4-0.5): Easy Detection</text>
                            <rect x="0" y="20" width="200" height="20" fill="#27ae60" opacity="0.3" stroke="#27ae60"/>
                            <rect x="0" y="20" width="100" height="20" fill="#27ae60"/>
                            <text x="105" y="35" font-size="10" fill="#2c3e50">50% mutant reads</text>
                        </g>
                        
                        <!-- Medium Allele Fraction -->
                        <g transform="translate(0, 80)">
                            <text x="0" y="15" font-size="12" fill="#f39c12">Medium AF (0.1-0.3): Moderate Challenge</text>
                            <rect x="0" y="20" width="200" height="20" fill="#f39c12" opacity="0.3" stroke="#f39c12"/>
                            <rect x="0" y="20" width="40" height="20" fill="#f39c12"/>
                            <text x="45" y="35" font-size="10" fill="#2c3e50">20% mutant reads</text>
                        </g>
                        
                        <!-- Low Allele Fraction (Hard to detect) -->
                        <g transform="translate(0, 120)">
                            <text x="0" y="15" font-size="12" fill="#e74c3c">Low AF (0.05-0.1): High Challenge</text>
                            <rect x="0" y="20" width="200" height="20" fill="#e74c3c" opacity="0.3" stroke="#e74c3c"/>
                            <rect x="0" y="20" width="10" height="20" fill="#e74c3c"/>
                            <text x="15" y="35" font-size="10" fill="#2c3e50">5% mutant reads</text>
                        </g>
                    </g>
                </svg>
            </div>
            
            <div class="algorithm-box">
                <h4>MuTect's Solution Approach</h4>
                <div class="step">
                    <strong>1. Bayesian Classification:</strong> Uses probabilistic models to distinguish true mutations from sequencing errors
                </div>
                <div class="step">
                    <strong>2. Allele Fraction Modeling:</strong> Explicitly models variable allele fractions instead of assuming 50%
                </div>
                <div class="step">
                    <strong>3. Systematic Filtering:</strong> Applies carefully tuned filters to eliminate false positives
                </div>
                <div class="step">
                    <strong>4. Benchmarking:</strong> Uses novel validation approaches with real sequencing data
                </div>
            </div>
            
            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">Next: Mathematical Foundation →</button>
            </div>
        </div>
        
        <!-- Section 2: Mathematical Foundation -->
        <div id="section2" class="section hidden">
            <h2>2. Mathematical Foundation</h2>

            <div class="highlight">
                <strong>Core Principle:</strong> MuTect uses Bayesian classification to compare two competing models at each genomic position: a reference model (no mutation) vs. a variant model (mutation present).
            </div>

            <h3>Fundamental Problem Setup</h3>

            <div class="math-box">
                <h4>The Statistical Challenge</h4>
                <p>At each genomic position, we must distinguish between:</p>
                <ul>
                    <li><strong>True somatic mutations:</strong> Real biological changes in tumor DNA</li>
                    <li><strong>Sequencing artifacts:</strong> Technical errors that mimic mutations</li>
                </ul>

                <p><strong>Key Insight:</strong> This is a classic hypothesis testing problem where we compare the likelihood of observing our sequencing data under two competing explanations.</p>

                <p><strong>Mathematical Framework:</strong> Bayesian model comparison using likelihood ratios</p>
            </div>

            <h3>Detailed Model Definitions</h3>

            <div class="math-box">
                <h4>Data Representation</h4>
                <p>For each genomic site, we observe:</p>
                <ul>
                    <li><strong>r:</strong> Reference allele ∈ {A, C, G, T}</li>
                    <li><strong>d:</strong> Total number of reads covering the site</li>
                    <li><strong>b₁, b₂, ..., b_d:</strong> Called bases from each read</li>
                    <li><strong>q₁, q₂, ..., q_d:</strong> Phred quality scores for each base call</li>
                </ul>

                <p><strong>Error Probability Conversion:</strong></p>
                $$e_i = 10^{-q_i/10}$$

                <p>This converts Phred scores to error probabilities. For example:</p>
                <ul>
                    <li>Q20 → e = 0.01 (1% error rate)</li>
                    <li>Q30 → e = 0.001 (0.1% error rate)</li>
                    <li>Q40 → e = 0.0001 (0.01% error rate)</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>Model M₀: Reference (Null) Model</h4>
                <p><strong>Assumption:</strong> No true variant exists at this position</p>
                <p><strong>Explanation for non-reference bases:</strong> All are sequencing errors</p>

                <p><strong>Probability of observing base b_i:</strong></p>
                $$P(b_i | M_0, e_i, r) = \begin{cases}
                1 - e_i & \text{if } b_i = r \text{ (correct call)} \\
                \frac{e_i}{3} & \text{if } b_i \neq r \text{ (error to any of 3 other bases)}
                \end{cases}$$

                <p><strong>Key Insight:</strong> Under M₀, errors are equally likely to any of the three non-reference bases</p>
            </div>

            <div class="math-box">
                <h4>Model M^m_f: Variant (Alternative) Model</h4>
                <p><strong>Assumption:</strong> True variant allele m exists at allele fraction f</p>
                <p><strong>Dual Source Model:</strong> Observed bases come from either:</p>
                <ol>
                    <li>True variant alleles (fraction f)</li>
                    <li>Sequencing errors (affecting all reads)</li>
                </ol>

                <p><strong>Probability of observing base b_i:</strong></p>
                $$P(b_i | M^m_f, e_i, r, m, f) = \begin{cases}
                f \cdot \frac{e_i}{3} + (1-f) \cdot (1-e_i) & \text{if } b_i = r \\
                f \cdot (1-e_i) + (1-f) \cdot \frac{e_i}{3} & \text{if } b_i = m \\
                \frac{e_i}{3} & \text{if } b_i \notin \{r, m\}
                \end{cases}$$

                <p><strong>Interpretation of each case:</strong></p>
                <ul>
                    <li><strong>Case 1 (b_i = r):</strong> Reference base observed due to: (1-f) fraction of true reference reads with correct sequencing, plus f fraction of variant reads with error back to reference</li>
                    <li><strong>Case 2 (b_i = m):</strong> Variant base observed due to: f fraction of true variant reads with correct sequencing, plus (1-f) fraction of reference reads with error to variant</li>
                    <li><strong>Case 3 (other):</strong> Neither reference nor variant - must be sequencing error</li>
                </ul>
            </div>

            <h3>Likelihood Functions - Detailed Derivation</h3>

            <div class="math-box">
                <h4>Independence Assumption</h4>
                <p><strong>Critical Assumption:</strong> Sequencing errors are independent across reads</p>
                <p>This allows us to multiply individual read probabilities:</p>

                $$L[M^m_f] = P(\{b_i\}_{i=1}^d | \{e_i\}_{i=1}^d, r, m, f) = \prod_{i=1}^{d} P(b_i | e_i, r, m, f)$$

                <p><strong>Why this matters:</strong> Without independence, the likelihood calculation becomes intractable</p>
            </div>

            <div class="math-box">
                <h4>Reference Model Likelihood</h4>
                <p>Under the null model M₀ (no variant), the likelihood is:</p>

                $$L[M_0] = \prod_{i=1}^{d} P(b_i | e_i, r) = \prod_{i=1}^{d} \begin{cases}
                1 - e_i & \text{if } b_i = r \\
                \frac{e_i}{3} & \text{if } b_i \neq r
                \end{cases}$$

                <p><strong>Computational Note:</strong> M₀ is equivalent to M^m_f with f = 0</p>
            </div>

            <div class="math-box">
                <h4>Variant Model Likelihood - Complete Form</h4>
                <p>For the variant model M^m_f:</p>

                $$L[M^m_f] = \prod_{i=1}^{d} P(b_i | e_i, r, m, f)$$

                <p>Where each term depends on what base was observed:</p>

                $$P(b_i | e_i, r, m, f) = \begin{cases}
                f \cdot \frac{e_i}{3} + (1-f) \cdot (1-e_i) & \text{if } b_i = r \\
                f \cdot (1-e_i) + (1-f) \cdot \frac{e_i}{3} & \text{if } b_i = m \\
                \frac{e_i}{3} & \text{if } b_i \notin \{r, m\}
                \end{cases}$$

                <p><strong>Intuitive Explanation:</strong></p>
                <ul>
                    <li><strong>Reference base (b_i = r):</strong> Could be from (1-f) true reference molecules read correctly, OR from f variant molecules with sequencing error back to reference</li>
                    <li><strong>Variant base (b_i = m):</strong> Could be from f true variant molecules read correctly, OR from (1-f) reference molecules with sequencing error to variant</li>
                    <li><strong>Other bases:</strong> Must be sequencing errors (equally likely to any non-reference base)</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>Practical Example</h4>
                <p><strong>Scenario:</strong> Reference = A, Candidate variant = T, Allele fraction = 0.3</p>
                <p><strong>Observed read:</strong> Base = T, Quality = Q30 (e = 0.001)</p>

                <p><strong>Under M₀:</strong> P(T | A, e=0.001) = 0.001/3 ≈ 0.00033</p>
                <p><strong>Under M^T_{0.3}:</strong> P(T | A, T, f=0.3, e=0.001) = 0.3 × (1-0.001) + 0.7 × 0.001/3 ≈ 0.2997</p>

                <p><strong>Likelihood Ratio:</strong> 0.2997/0.00033 ≈ 908 (strong evidence for variant)</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Bayesian Model Comparison Framework
                    </text>

                    <!-- Reference Model -->
                    <g transform="translate(50, 60)">
                        <rect width="300" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Reference Model (M₀)</text>
                        <text x="150" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">No mutation present</text>
                        <text x="150" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">All non-reference bases = errors</text>

                        <!-- DNA representation -->
                        <g transform="translate(50, 75)">
                            <circle cx="20" cy="15" r="8" fill="#3498db"/>
                            <text x="20" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <circle cx="50" cy="15" r="8" fill="#3498db"/>
                            <text x="50" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <circle cx="80" cy="15" r="8" fill="#e74c3c" opacity="0.5"/>
                            <text x="80" y="20" text-anchor="middle" font-size="8" fill="white">T</text>
                            <circle cx="110" cy="15" r="8" fill="#3498db"/>
                            <text x="110" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <circle cx="140" cy="15" r="8" fill="#3498db"/>
                            <text x="140" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <text x="170" y="20" font-size="10" fill="#e74c3c">← Error</text>
                        </g>
                    </g>

                    <!-- Variant Model -->
                    <g transform="translate(450, 60)">
                        <rect width="300" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Variant Model (M^m_f)</text>
                        <text x="150" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">Mutation at allele fraction f</text>
                        <text x="150" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">True variants + sequencing errors</text>

                        <!-- DNA representation -->
                        <g transform="translate(50, 75)">
                            <circle cx="20" cy="15" r="8" fill="#3498db"/>
                            <text x="20" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <circle cx="50" cy="15" r="8" fill="#27ae60"/>
                            <text x="50" y="20" text-anchor="middle" font-size="8" fill="white">T</text>
                            <circle cx="80" cy="15" r="8" fill="#27ae60"/>
                            <text x="80" y="20" text-anchor="middle" font-size="8" fill="white">T</text>
                            <circle cx="110" cy="15" r="8" fill="#3498db"/>
                            <text x="110" y="20" text-anchor="middle" font-size="8" fill="white">A</text>
                            <circle cx="140" cy="15" r="8" fill="#27ae60"/>
                            <text x="140" y="20" text-anchor="middle" font-size="8" fill="white">T</text>
                            <text x="170" y="20" font-size="10" fill="#27ae60">← Mutations</text>
                        </g>
                    </g>

                    <!-- LOD Score Calculation -->
                    <g transform="translate(200, 220)">
                        <rect width="400" height="100" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="200" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">LOD Score Decision</text>
                        <text x="200" y="50" text-anchor="middle" font-size="12" fill="#2c3e50">LOD_T(m,f) = log₁₀(L[M^m_f] × P(m,f) / L[M₀] × (1-P(m,f)))</text>
                        <text x="200" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">If LOD_T ≥ θ_T (threshold = 6.3), declare variant candidate</text>
                    </g>
                </svg>
            </div>

            <h3>Bayesian Decision Theory Framework</h3>

            <div class="math-box">
                <h4>From Likelihood Ratio to Posterior Odds</h4>
                <p><strong>Bayes' Theorem Application:</strong></p>

                $$\frac{P(M^m_f | \text{data})}{P(M_0 | \text{data})} = \frac{L[M^m_f]}{L[M_0]} \times \frac{P(M^m_f)}{P(M_0)}$$

                <p><strong>Components:</strong></p>
                <ul>
                    <li><strong>Likelihood Ratio:</strong> $\frac{L[M^m_f]}{L[M_0]}$ - Evidence from sequencing data</li>
                    <li><strong>Prior Odds:</strong> $\frac{P(M^m_f)}{P(M_0)}$ - Prior belief about mutation probability</li>
                    <li><strong>Posterior Odds:</strong> Final odds after seeing data</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>LOD Score - Complete Derivation</h4>
                <p><strong>Definition:</strong> Log₁₀ of posterior odds ratio</p>

                $$LOD_T(m,f) = \log_{10}\left(\frac{P(M^m_f | \text{data})}{P(M_0 | \text{data})}\right)$$

                <p><strong>Expanding using Bayes' theorem:</strong></p>

                $$LOD_T(m,f) = \log_{10}\left(\frac{L[M^m_f]}{L[M_0]}\right) + \log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right)$$

                <p><strong>Decision Rule:</strong> Call variant if LOD_T ≥ θ_T</p>

                $$\log_{10}\left(\frac{L[M^m_f]}{L[M_0]}\right) + \log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right) \geq \theta_T$$

                <p><strong>Rearranging:</strong></p>

                $$\log_{10}\left(\frac{L[M^m_f]}{L[M_0]}\right) \geq \theta_T - \log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right)$$
            </div>

            <div class="math-box">
                <h4>Prior Probability Modeling</h4>
                <p><strong>Mutation Prior P(M^m_f):</strong></p>

                <p>Assumes independence: P(M^m_f) = P(m) × P(f)</p>

                <ul>
                    <li><strong>P(m):</strong> Prior for specific substitution = (1/3) × (mutation frequency)</li>
                    <li><strong>P(f):</strong> Prior for allele fraction = 1 (uniform distribution)</li>
                </ul>

                <p><strong>Typical Values:</strong></p>
                <ul>
                    <li>Mutation frequency ≈ 3×10⁻⁶ per base</li>
                    <li>P(m) = (1/3) × 3×10⁻⁶ = 1×10⁻⁶</li>
                    <li>P(M₀) = 1 - P(M^m_f) ≈ 1</li>
                </ul>

                <p><strong>Prior Odds:</strong></p>
                $$\frac{P(M^m_f)}{P(M_0)} \approx 1×10^{-6}$$

                <p><strong>Log Prior Odds:</strong></p>
                $$\log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right) \approx -6$$
            </div>

            <div class="math-box">
                <h4>Threshold Selection - Detailed Rationale</h4>
                <p><strong>Desired Posterior Odds:</strong> Want at least δ_T:1 odds favoring variant</p>

                $$\frac{P(M^m_f | \text{data})}{P(M_0 | \text{data})} \geq \delta_T$$

                <p><strong>Taking log₁₀:</strong></p>
                $$LOD_T(m,f) \geq \log_{10}(\delta_T)$$

                <p><strong>Substituting Bayes' theorem:</strong></p>
                $$\log_{10}\left(\frac{L[M^m_f]}{L[M_0]}\right) + \log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right) \geq \log_{10}(\delta_T)$$

                <p><strong>Final threshold:</strong></p>
                $$\theta_T = \log_{10}(\delta_T) - \log_{10}\left(\frac{P(M^m_f)}{P(M_0)}\right)$$

                <p><strong>With δ_T = 2 and prior odds ≈ 10⁻⁶:</strong></p>
                $$\theta_T = \log_{10}(2) - \log_{10}(10^{-6}) = 0.3 + 6 = 6.3$$

                <p><strong>Interpretation:</strong> Need 10^6.3 ≈ 2,000,000:1 likelihood ratio to overcome low prior probability</p>
            </div>

            <div class="math-box">
                <h4>Allele Fraction Estimation - Mathematical Details</h4>

                <p><strong>Maximum Likelihood Estimation (MLE):</strong></p>
                <p>Theoretically, we should find f that maximizes the likelihood:</p>

                $$\hat{f}_{MLE} = \arg\max_f L[M^m_f]$$

                <p><strong>Computational Approximation:</strong></p>
                <p>For efficiency, MuTect uses a simple estimator:</p>

                $$\hat{f} = \frac{\text{Number of reads supporting variant m}}{\text{Total number of reads}}$$

                <p><strong>Why this approximation works:</strong></p>
                <ul>
                    <li>When sequencing error rates are low (e_i << f), the MLE ≈ simple fraction</li>
                    <li>Computational cost is O(1) instead of requiring optimization</li>
                    <li>Empirically validated to give similar results to full MLE</li>
                </ul>

                <p><strong>Bias Considerations:</strong></p>
                <ul>
                    <li>Slight upward bias when f is very low (due to sequencing errors)</li>
                    <li>Bias decreases as sequencing quality improves</li>
                    <li>Acceptable trade-off for computational efficiency</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>Contamination Modeling - Advanced Protection</h4>

                <p><strong>Problem:</strong> Normal DNA contamination creates false positive mutations</p>
                <p><strong>Mechanism:</strong> Germline SNPs in contaminating DNA appear as somatic mutations</p>

                <p><strong>Enhanced Reference Model:</strong></p>
                <p>Replace M₀ with contamination-aware model M^m_{f_cont}:</p>

                $$P(b_i | M^m_{f_{cont}}, e_i, r, m, f_{cont}) = \begin{cases}
                f_{cont} \cdot \frac{e_i}{3} + (1-f_{cont}) \cdot (1-e_i) & \text{if } b_i = r \\
                f_{cont} \cdot (1-e_i) + (1-f_{cont}) \cdot \frac{e_i}{3} & \text{if } b_i = m \\
                \frac{e_i}{3} & \text{otherwise}
                \end{cases}$$

                <p><strong>Where f_cont is the estimated contamination fraction</strong></p>

                <p><strong>Modified Decision Rule:</strong></p>
                $$LOD_T(m,f) = \log_{10}\left(\frac{L[M^m_f]}{L[M^m_{f_{cont}}]}\right) \geq \theta_T$$

                <p><strong>Impact:</strong> Variants are called only when they're unlikely to be explained by contamination</p>

                <p><strong>Contamination Detection:</strong> Uses ContEst tool to estimate f_cont from data</p>
            </div>

            <div class="algorithm-box">
                <h4>Computational Optimization Strategies</h4>
                <div class="step">
                    <strong>Candidate Screening:</strong> Only test m ∈ {A,C,G,T} \ {r} (3 candidates per site)
                </div>
                <div class="step">
                    <strong>Early Termination:</strong> Stop if no candidate exceeds threshold
                </div>
                <div class="step">
                    <strong>Quality Filtering:</strong> Pre-filter low-quality reads to reduce noise
                </div>
                <div class="step">
                    <strong>Vectorized Computation:</strong> Process multiple sites simultaneously
                </div>
            </div>

            <div class="math-box">
                <h4>Model Assumptions and Limitations</h4>

                <p><strong>Key Assumptions:</strong></p>
                <ul>
                    <li><strong>Independence:</strong> Sequencing errors are independent across reads</li>
                    <li><strong>Uniform Error Model:</strong> All substitution errors equally likely</li>
                    <li><strong>Accurate Quality Scores:</strong> Phred scores reflect true error probabilities</li>
                    <li><strong>No Systematic Bias:</strong> Errors are random, not position-dependent</li>
                </ul>

                <p><strong>Real-World Violations:</strong></p>
                <ul>
                    <li>Strand bias in sequencing errors</li>
                    <li>Context-dependent error rates</li>
                    <li>Mapping quality issues</li>
                    <li>PCR artifacts and duplicates</li>
                </ul>

                <p><strong>MuTect's Solution:</strong> Systematic filtering pipeline addresses these violations</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(1)">← Previous</button>
                <button class="nav-button" onclick="showSection(3)">Next: Core Algorithm →</button>
            </div>
        </div>

        <!-- Section 3: Core Algorithm -->
        <div id="section3" class="section hidden">
            <h2>3. Core Algorithm</h2>

            <div class="highlight">
                <strong>MuTect Pipeline:</strong> Four-step process for robust somatic mutation detection with high sensitivity and specificity.
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        MuTect Algorithm Workflow
                    </text>

                    <!-- Input Data -->
                    <g transform="translate(50, 50)">
                        <rect width="120" height="60" fill="#3498db" rx="10"/>
                        <text x="60" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Input Data</text>
                        <text x="60" y="40" text-anchor="middle" font-size="10" fill="white">Tumor BAM</text>
                        <text x="60" y="52" text-anchor="middle" font-size="10" fill="white">Normal BAM</text>
                    </g>

                    <!-- Step 1: Quality Filtering -->
                    <g transform="translate(220, 50)">
                        <rect width="120" height="60" fill="#e74c3c" rx="10"/>
                        <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 1</text>
                        <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Quality Filtering</text>
                        <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Remove low-quality reads</text>
                    </g>

                    <!-- Step 2: Variant Detection -->
                    <g transform="translate(390, 50)">
                        <rect width="120" height="60" fill="#27ae60" rx="10"/>
                        <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 2</text>
                        <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Variant Detection</text>
                        <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Bayesian classifier</text>
                    </g>

                    <!-- Step 3: Artifact Filtering -->
                    <g transform="translate(560, 50)">
                        <rect width="120" height="60" fill="#f39c12" rx="10"/>
                        <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 3</text>
                        <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Artifact Filtering</text>
                        <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">6 systematic filters</text>
                    </g>

                    <!-- Step 4: Classification -->
                    <g transform="translate(305, 150)">
                        <rect width="120" height="60" fill="#9b59b6" rx="10"/>
                        <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Step 4</text>
                        <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Classification</text>
                        <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Somatic vs Germline</text>
                    </g>

                    <!-- Arrows -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <line x1="170" y1="80" x2="210" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="340" y1="80" x2="380" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="510" y1="80" x2="550" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="620" y1="110" x2="365" y2="140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <!-- Detailed Step 2 Breakdown -->
                    <g transform="translate(50, 250)">
                        <rect width="700" height="220" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Step 2: Variant Detection Details
                        </text>

                        <!-- LOD Score Calculation -->
                        <g transform="translate(20, 50)">
                            <rect width="200" height="80" fill="#3498db" opacity="0.8" rx="8"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">LOD Score</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="white">Compare M₀ vs M^m_f</text>
                            <text x="100" y="50" text-anchor="middle" font-size="10" fill="white">Threshold: θ_T = 6.3</text>
                            <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">For each m ∈ {A,C,G,T}</text>
                        </g>

                        <!-- Allele Fraction Estimation -->
                        <g transform="translate(250, 50)">
                            <rect width="200" height="80" fill="#27ae60" opacity="0.8" rx="8"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Allele Fraction</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="white">f̂ = mutant reads /</text>
                            <text x="100" y="50" text-anchor="middle" font-size="10" fill="white">total reads</text>
                            <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">No fixed 50% assumption</text>
                        </g>

                        <!-- Decision Making -->
                        <g transform="translate(480, 50)">
                            <rect width="200" height="80" fill="#e74c3c" opacity="0.8" rx="8"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Decision</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="white">If LOD_T ≥ 6.3:</text>
                            <text x="100" y="50" text-anchor="middle" font-size="10" fill="white">Candidate variant</text>
                            <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">Proceed to filtering</text>
                        </g>

                        <!-- Mathematical Formula -->
                        <g transform="translate(50, 150)">
                            <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                                Key Formula: LOD_T(m,f) = log₁₀(L[M^m_f]/L[M₀]) ≥ θ_T
                            </text>
                            <text x="300" y="40" text-anchor="middle" font-size="12" fill="#2c3e50">
                                Where θ_T accounts for prior mutation probability and desired specificity
                            </text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Sensitivity Calculation</h3>

            <div class="math-box">
                <h4>Theoretical Sensitivity Prediction</h4>
                <p>To calculate sensitivity for detecting a mutation with allele fraction f using n reads with quality score q:</p>

                <p><strong>Step 1:</strong> Find minimum number of mutant reads k needed for detection:</p>
                $$k = \arg\min_x LOD_T(x|n,e) \geq \theta_T$$

                <p><strong>Step 2:</strong> Calculate probability of observing k or more mutant reads:</p>
                $$\text{Sensitivity} = \sum_{i=k}^{n} \text{binom}(i|n, f(1-e) + (1-f)e)$$

                <p>Where e = 10^(-q/10) is the base error probability.</p>
            </div>

            <div class="algorithm-box">
                <h4>Algorithm Configuration Options</h4>
                <div class="step">
                    <strong>Standard (STD):</strong> No filters applied - maximum sensitivity, higher false positives
                </div>
                <div class="step">
                    <strong>High Confidence (HC):</strong> Six systematic filters applied - balanced sensitivity/specificity
                </div>
                <div class="step">
                    <strong>HC + Panel of Normals (HC+PON):</strong> Additional population-based filtering - highest specificity
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">← Previous</button>
                <button class="nav-button" onclick="showSection(4)">Next: Filtering Methods →</button>
            </div>
        </div>

        <!-- Section 4: Filtering Methods -->
        <div id="section4" class="section hidden">
            <h2>4. Filtering Methods</h2>

            <div class="highlight">
                <strong>Systematic False Positive Reduction:</strong> Six carefully designed filters eliminate artifacts while preserving true mutations, reducing false positive rate by an order of magnitude.
            </div>

            <h3>The Six Core Filters</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Filter Name</th>
                        <th>Purpose</th>
                        <th>Criteria</th>
                        <th>Impact</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Proximal Gap</strong></td>
                        <td>Remove indel-related artifacts</td>
                        <td>≥3 insertions OR ≥3 deletions in 11bp window</td>
                        <td><span class="performance-metric medium">Moderate</span></td>
                    </tr>
                    <tr>
                        <td><strong>Poor Mapping</strong></td>
                        <td>Remove misalignment artifacts</td>
                        <td>≥50% reads with MAPQ=0 OR no confident mapping (MAPQ≥20)</td>
                        <td><span class="performance-metric good">High at low depth</span></td>
                    </tr>
                    <tr>
                        <td><strong>Triallelic Site</strong></td>
                        <td>Avoid complex variant sites</td>
                        <td>Normal is A/B, considering alternate C</td>
                        <td><span class="performance-metric medium">Moderate</span></td>
                    </tr>
                    <tr>
                        <td><strong>Strand Bias</strong></td>
                        <td>Remove directional sequencing errors</td>
                        <td>Strand-specific LOD < 2.0 with ≥90% sensitivity</td>
                        <td><span class="performance-metric good">High</span></td>
                    </tr>
                    <tr>
                        <td><strong>Clustered Position</strong></td>
                        <td>Remove read-end artifacts</td>
                        <td>Median distance ≤10 from read end AND MAD ≤3</td>
                        <td><span class="performance-metric good">Highest exclusive</span></td>
                    </tr>
                    <tr>
                        <td><strong>Observed in Control</strong></td>
                        <td>Remove germline events</td>
                        <td>≥2 alt reads OR ≥3% frequency AND quality sum >20</td>
                        <td><span class="performance-metric good">High</span></td>
                    </tr>
                </tbody>
            </table>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Filter Impact on False Positive Rate
                    </text>

                    <!-- Y-axis (False Positive Rate) -->
                    <line x1="80" y1="50" x2="80" y2="350" stroke="#2c3e50" stroke-width="2"/>
                    <text x="25" y="200" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90, 25, 200)">
                        False Positive Rate (per Mb)
                    </text>

                    <!-- Y-axis labels -->
                    <text x="70" y="60" text-anchor="end" font-size="10" fill="#2c3e50">20</text>
                    <text x="70" y="120" text-anchor="end" font-size="10" fill="#2c3e50">15</text>
                    <text x="70" y="180" text-anchor="end" font-size="10" fill="#2c3e50">10</text>
                    <text x="70" y="240" text-anchor="end" font-size="10" fill="#2c3e50">5</text>
                    <text x="70" y="300" text-anchor="end" font-size="10" fill="#2c3e50">1</text>
                    <text x="70" y="340" text-anchor="end" font-size="10" fill="#2c3e50">0.5</text>

                    <!-- X-axis -->
                    <line x1="80" y1="350" x2="750" y2="350" stroke="#2c3e50" stroke-width="2"/>
                    <text x="415" y="380" text-anchor="middle" font-size="12" fill="#2c3e50">Configuration</text>

                    <!-- Bars -->
                    <!-- STD (No filters) -->
                    <rect x="150" y="80" width="80" height="270" fill="#e74c3c" opacity="0.8"/>
                    <text x="190" y="370" text-anchor="middle" font-size="11" fill="#2c3e50">STD</text>
                    <text x="190" y="70" text-anchor="middle" font-size="10" fill="#e74c3c">20.1/Mb</text>

                    <!-- HC (With 6 filters) -->
                    <rect x="300" y="290" width="80" height="60" fill="#f39c12" opacity="0.8"/>
                    <text x="340" y="370" text-anchor="middle" font-size="11" fill="#2c3e50">HC</text>
                    <text x="340" y="280" text-anchor="middle" font-size="10" fill="#f39c12">1.0/Mb</text>

                    <!-- HC+PON (With Panel of Normals) -->
                    <rect x="450" y="320" width="80" height="30" fill="#27ae60" opacity="0.8"/>
                    <text x="490" y="370" text-anchor="middle" font-size="11" fill="#2c3e50">HC+PON</text>
                    <text x="490" y="310" text-anchor="middle" font-size="10" fill="#27ae60">0.51/Mb</text>

                    <!-- Improvement arrows -->
                    <path d="M 240 175 Q 270 150 290 175" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="265" y="140" text-anchor="middle" font-size="10" fill="#2c3e50">20x reduction</text>

                    <path d="M 390 305 Q 420 290 440 305" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <text x="415" y="280" text-anchor="middle" font-size="10" fill="#2c3e50">2x reduction</text>
                </svg>
            </div>

            <h3>Panel of Normals (PON) Filter</h3>

            <div class="math-box">
                <h4>Population-Based Artifact Detection</h4>
                <p>The Panel of Normals filter uses a collection of normal samples to identify recurrent artifacts:</p>

                <p><strong>Construction:</strong></p>
                <ul>
                    <li>Run MuTect on normal samples as if they were tumors (STD mode)</li>
                    <li>Create VCF of sites detected in ≥2 normal samples</li>
                    <li>Filter out these sites from tumor analysis</li>
                </ul>

                <p><strong>Exception:</strong> Retain sites present in COSMIC database (known somatic mutations)</p>

                <p><strong>Power:</strong> Increases with panel size - typically use 100+ samples</p>
            </div>

            <h3>Variant Classification</h3>

            <div class="math-box">
                <h4>Somatic vs. Germline Classification</h4>
                <p>After variant detection, classify each variant using normal sample data:</p>

                $$LOD_N = \log_{10}\left(\frac{L[M_0] \cdot P(m,f)}{L[M^m_{0.5}] \cdot P(\text{germline})}\right) \geq \log_{10} \delta_N$$

                <p>Where:</p>
                <ul>
                    <li><strong>δ_N = 10:</strong> Higher threshold for germline classification</li>
                    <li><strong>f = 0.5:</strong> Assumes heterozygous germline variant</li>
                    <li><strong>P(germline):</strong> Prior probability based on dbSNP status</li>
                </ul>

                <p><strong>dbSNP-based priors:</strong></p>
                <ul>
                    <li>Known variant sites: P(germline) = 0.095 → θ_N = 5.5</li>
                    <li>Novel sites: P(germline) = 5×10⁻⁵ → θ_N = 2.2</li>
                </ul>
            </div>

            <div class="algorithm-box">
                <h4>Filter Effectiveness Analysis</h4>
                <div class="step">
                    <strong>Depth-Dependent:</strong> Poor Mapping filter most effective at low depths
                </div>
                <div class="step">
                    <strong>Depth-Invariant:</strong> Proximal Gap filter consistent across depths
                </div>
                <div class="step">
                    <strong>Exclusive Impact:</strong> Clustered Position filter rejects most sites exclusively
                </div>
                <div class="step">
                    <strong>Overlapping:</strong> Most false positives rejected by multiple filters
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(3)">← Previous</button>
                <button class="nav-button" onclick="showSection(5)">Next: Benchmarking →</button>
            </div>
        </div>

        <!-- Section 5: Benchmarking Approaches -->
        <div id="section5" class="section hidden">
            <h2>5. Benchmarking Approaches</h2>

            <div class="highlight">
                <strong>Novel Validation Methods:</strong> Two complementary approaches using real sequencing data to systematically evaluate sensitivity and specificity across parameter space.
            </div>

            <h3>Benchmarking Challenges</h3>

            <div class="algorithm-box">
                <h4>Traditional Limitations</h4>
                <div class="step">
                    <strong>Synthetic Simulations:</strong> Don't capture real sequencing error patterns
                </div>
                <div class="step">
                    <strong>Limited Validation Sets:</strong> Small numbers of confirmed mutations
                </div>
                <div class="step">
                    <strong>Parameter Constraints:</strong> Can't explore full allele fraction/depth space
                </div>
                <div class="step">
                    <strong>Specificity Blind Spots:</strong> Difficult to measure false positive rates
                </div>
            </div>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Two Complementary Benchmarking Approaches
                    </text>

                    <!-- Down-sampling Approach -->
                    <g transform="translate(50, 60)">
                        <rect width="300" height="160" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Down-sampling</text>

                        <!-- Process steps -->
                        <g transform="translate(20, 40)">
                            <circle cx="15" cy="15" r="12" fill="#3498db"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Start with validated mutations</text>
                        </g>

                        <g transform="translate(20, 65)">
                            <circle cx="15" cy="15" r="12" fill="#3498db"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">2</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Randomly remove reads to target depth</text>
                        </g>

                        <g transform="translate(20, 90)">
                            <circle cx="15" cy="15" r="12" fill="#3498db"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">3</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Measure detection sensitivity</text>
                        </g>

                        <!-- Advantages/Limitations -->
                        <text x="150" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">✓ Real somatic mutations</text>
                        <text x="150" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">✗ Limited parameter space</text>
                    </g>

                    <!-- Virtual Tumor Approach -->
                    <g transform="translate(450, 60)">
                        <rect width="300" height="160" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Virtual Tumors</text>

                        <!-- Process steps -->
                        <g transform="translate(20, 40)">
                            <circle cx="15" cy="15" r="12" fill="#27ae60"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Use two normal samples (A, B)</text>
                        </g>

                        <g transform="translate(20, 65)">
                            <circle cx="15" cy="15" r="12" fill="#27ae60"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">2</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Spike B reads into A at controlled AF</text>
                        </g>

                        <g transform="translate(20, 90)">
                            <circle cx="15" cy="15" r="12" fill="#27ae60"/>
                            <text x="15" y="20" text-anchor="middle" font-size="10" fill="white">3</text>
                            <text x="35" y="20" font-size="11" fill="#2c3e50">Measure sensitivity & specificity</text>
                        </g>

                        <!-- Advantages/Limitations -->
                        <text x="150" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">✓ Full parameter control</text>
                        <text x="150" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">✗ Simulated mutations</text>
                    </g>

                    <!-- Virtual Tumor Detail -->
                    <g transform="translate(100, 260)">
                        <rect width="600" height="160" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Virtual Tumor Construction Details
                        </text>

                        <!-- Sample A (NA12878) -->
                        <g transform="translate(50, 50)">
                            <rect width="120" height="80" fill="#3498db" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Sample A</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">(NA12878)</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="white">Reference at site</text>
                            <text x="60" y="65" text-anchor="middle" font-size="10" fill="white">90x coverage</text>
                        </g>

                        <!-- Sample B (NA12891) -->
                        <g transform="translate(200, 50)">
                            <rect width="120" height="80" fill="#27ae60" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Sample B</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">(NA12891)</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="white">Heterozygous</text>
                            <text x="60" y="65" text-anchor="middle" font-size="10" fill="white">60x coverage</text>
                        </g>

                        <!-- Mixing Process -->
                        <g transform="translate(350, 50)">
                            <rect width="120" height="80" fill="#f39c12" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Mixing</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Replace reads</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="white">at target AF</text>
                            <text x="60" y="65" text-anchor="middle" font-size="10" fill="white">Binomial sampling</text>
                        </g>

                        <!-- Virtual Tumor -->
                        <g transform="translate(500, 50)">
                            <rect width="120" height="80" fill="#9b59b6" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Virtual Tumor</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">Known mutations</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="white">Controlled AF</text>
                            <text x="60" y="65" text-anchor="middle" font-size="10" fill="white">Real seq errors</text>
                        </g>

                        <!-- Arrows -->
                        <path d="M 175 90 L 195 90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 325 90 L 345 90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 475 90 L 495 90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    </g>
                </svg>
            </div>

            <h3>Mathematical Framework for Virtual Tumors</h3>

            <div class="math-box">
                <h4>Sensitivity Measurement</h4>
                <p><strong>Setup:</strong> At sites where Sample A = reference, Sample B = heterozygous variant</p>

                <p><strong>Spiking Process:</strong> Replace k reads from A with reads from B, where k follows:</p>
                $$k \sim \text{Binomial}(n, f)$$

                <p>Where n = total reads at site, f = target allele fraction</p>

                <p><strong>Ground Truth:</strong> Know exact location and allele fraction of every simulated mutation</p>

                <p><strong>Sensitivity:</strong> Fraction of simulated mutations detected by algorithm</p>
            </div>

            <div class="math-box">
                <h4>Specificity Measurement</h4>
                <p><strong>Setup:</strong> Use different partitions of same sample (A) as tumor and normal</p>

                <p><strong>Ground Truth:</strong> No true somatic mutations should exist</p>

                <p><strong>False Positives:</strong> Any detected "somatic" mutations are artifacts</p>

                <p><strong>Specificity:</strong> 1 - (False Positive Rate)</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(4)">← Previous</button>
                <button class="nav-button" onclick="showSection(6)">Next: Performance Analysis →</button>
            </div>
        </div>

        <!-- Section 6: Performance Analysis -->
        <div id="section6" class="section hidden">
            <h2>6. Performance Analysis</h2>

            <div class="highlight">
                <strong>Exceptional Low-AF Performance:</strong> MuTect achieves 95.6% sensitivity at 30x depth with 0.2 allele fraction, and maintains 58.9% sensitivity even at 0.1 allele fraction.
            </div>

            <h3>Sensitivity Performance</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Sensitivity vs. Allele Fraction and Sequencing Depth
                    </text>

                    <!-- Y-axis (Sensitivity) -->
                    <line x1="80" y1="50" x2="80" y2="350" stroke="#2c3e50" stroke-width="2"/>
                    <text x="25" y="200" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90, 25, 200)">
                        Sensitivity (%)
                    </text>

                    <!-- Y-axis labels -->
                    <text x="70" y="60" text-anchor="end" font-size="10" fill="#2c3e50">100</text>
                    <text x="70" y="110" text-anchor="end" font-size="10" fill="#2c3e50">80</text>
                    <text x="70" y="160" text-anchor="end" font-size="10" fill="#2c3e50">60</text>
                    <text x="70" y="210" text-anchor="end" font-size="10" fill="#2c3e50">40</text>
                    <text x="70" y="260" text-anchor="end" font-size="10" fill="#2c3e50">20</text>
                    <text x="70" y="310" text-anchor="end" font-size="10" fill="#2c3e50">0</text>

                    <!-- X-axis -->
                    <line x1="80" y1="350" x2="750" y2="350" stroke="#2c3e50" stroke-width="2"/>
                    <text x="415" y="380" text-anchor="middle" font-size="12" fill="#2c3e50">Allele Fraction</text>

                    <!-- X-axis labels -->
                    <text x="150" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.05</text>
                    <text x="250" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.1</text>
                    <text x="350" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.2</text>
                    <text x="450" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.3</text>
                    <text x="550" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.4</text>
                    <text x="650" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.5</text>

                    <!-- Sensitivity curves for different depths -->
                    <!-- 30x depth -->
                    <path d="M 150 290 Q 200 240 250 160 Q 300 100 350 65 Q 400 55 450 52 Q 500 50 550 50 Q 600 50 650 50"
                          stroke="#e74c3c" stroke-width="3" fill="none"/>
                    <text x="680" y="55" font-size="12" fill="#e74c3c" font-weight="bold">30x</text>

                    <!-- 50x depth -->
                    <path d="M 150 250 Q 200 180 250 120 Q 300 80 350 60 Q 400 52 450 50 Q 500 50 550 50 Q 600 50 650 50"
                          stroke="#f39c12" stroke-width="3" fill="none"/>
                    <text x="680" y="75" font-size="12" fill="#f39c12" font-weight="bold">50x</text>

                    <!-- 100x depth -->
                    <path d="M 150 200 Q 200 140 250 90 Q 300 65 350 55 Q 400 50 450 50 Q 500 50 550 50 Q 600 50 650 50"
                          stroke="#27ae60" stroke-width="3" fill="none"/>
                    <text x="680" y="95" font-size="12" fill="#27ae60" font-weight="bold">100x</text>

                    <!-- 150x depth -->
                    <path d="M 150 170 Q 200 110 250 75 Q 300 58 350 52 Q 400 50 450 50 Q 500 50 550 50 Q 600 50 650 50"
                          stroke="#3498db" stroke-width="3" fill="none"/>
                    <text x="680" y="115" font-size="12" fill="#3498db" font-weight="bold">150x</text>

                    <!-- Key performance points -->
                    <circle cx="350" cy="65" r="5" fill="#e74c3c"/>
                    <text x="355" y="45" font-size="10" fill="#e74c3c">95.6%</text>

                    <circle cx="250" cy="160" r="5" fill="#e74c3c"/>
                    <text x="255" y="140" font-size="10" fill="#e74c3c">58.9%</text>

                    <circle cx="150" y="170" r="5" fill="#3498db"/>
                    <text x="155" y="150" font-size="10" fill="#3498db">66.4%</text>
                    <text x="155" y="190" font-size="9" fill="#3498db">@3% AF</text>
                </svg>
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>AF = 0.5</th>
                        <th>AF = 0.2</th>
                        <th>AF = 0.1</th>
                        <th>AF = 0.05</th>
                        <th>AF = 0.03</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>30x</strong></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">95.6%</span></td>
                        <td><span class="performance-metric medium">58.9%</span></td>
                        <td><span class="performance-metric">16.0%</span></td>
                        <td><span class="performance-metric">-</span></td>
                    </tr>
                    <tr>
                        <td><strong>50x</strong></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">85.2%</span></td>
                        <td><span class="performance-metric medium">51.9%</span></td>
                        <td><span class="performance-metric">-</span></td>
                    </tr>
                    <tr>
                        <td><strong>100x</strong></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">98.1%</span></td>
                        <td><span class="performance-metric good">85.7%</span></td>
                        <td><span class="performance-metric medium">58.2%</span></td>
                    </tr>
                    <tr>
                        <td><strong>150x</strong></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">99.9%</span></td>
                        <td><span class="performance-metric good">99.5%</span></td>
                        <td><span class="performance-metric good">95.1%</span></td>
                        <td><span class="performance-metric medium">66.4%</span></td>
                    </tr>
                </tbody>
            </table>

            <h3>Specificity Performance</h3>

            <div class="math-box">
                <h4>False Positive Rate Analysis</h4>
                <p><strong>Two Sources of False Positives:</strong></p>
                <ol>
                    <li><strong>Tumor Over-calling:</strong> Sequencing errors called as mutations</li>
                    <li><strong>Normal Under-calling:</strong> True germline variants missed in normal</li>
                </ol>

                <p><strong>Tumor Over-calling Rates (per Mb):</strong></p>
                <ul>
                    <li>STD configuration: 6.7/Mb (5x) → 20.1/Mb (30x)</li>
                    <li>HC configuration: 1.00/Mb (30x) - 10x improvement</li>
                    <li>HC+PON configuration: 0.51/Mb (30x) - 2x further improvement</li>
                </ul>

                <p><strong>Normal Under-calling:</strong> Rapidly decreases with depth</p>
                <ul>
                    <li>Novel sites: 2.4×10⁻³ (8x) → <0.2×10⁻³ (12x)</li>
                    <li>Known sites: Lower error rates due to dbSNP priors</li>
                </ul>
            </div>

            <h3>Clinical Validation Results</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Tumor Type</th>
                        <th>Mutation Rate (/Mb)</th>
                        <th>Validated</th>
                        <th>Invalidated</th>
                        <th>Validation Rate</th>
                        <th>FP Rate (/Mb)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Multiple Myeloma</td>
                        <td>2.9</td>
                        <td>87</td>
                        <td>5</td>
                        <td><span class="performance-metric good">94.6%</span></td>
                        <td>0.16</td>
                    </tr>
                    <tr>
                        <td>Head and Neck</td>
                        <td>3.3</td>
                        <td>181</td>
                        <td>8</td>
                        <td><span class="performance-metric good">95.8%</span></td>
                        <td>0.14</td>
                    </tr>
                    <tr>
                        <td>Breast Cancer</td>
                        <td>2.9</td>
                        <td>464</td>
                        <td>27</td>
                        <td><span class="performance-metric good">94.5%</span></td>
                        <td>0.16</td>
                    </tr>
                    <tr>
                        <td>Colorectal</td>
                        <td>5.9</td>
                        <td>292</td>
                        <td>16</td>
                        <td><span class="performance-metric good">94.8%</span></td>
                        <td>0.31</td>
                    </tr>
                    <tr>
                        <td>Lung Adenocarcinoma</td>
                        <td>12</td>
                        <td>9,458</td>
                        <td>374</td>
                        <td><span class="performance-metric good">96.2%</span></td>
                        <td>0.46</td>
                    </tr>
                </tbody>
            </table>

            <div class="algorithm-box">
                <h4>Key Performance Insights</h4>
                <div class="step">
                    <strong>Consistent Validation:</strong> ~95% validation rate across multiple cancer types
                </div>
                <div class="step">
                    <strong>Low False Positive Rate:</strong> Median 0.16/Mb in coding regions
                </div>
                <div class="step">
                    <strong>Subclonal Detection:</strong> Validated mutations as low as 7% allele fraction
                </div>
                <div class="step">
                    <strong>Depth Scalability:</strong> Performance improves predictably with sequencing depth
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(5)">← Previous</button>
                <button class="nav-button" onclick="showSection(7)">Next: Method Comparison →</button>
            </div>
        </div>

        <!-- Section 7: Method Comparison -->
        <div id="section7" class="section hidden">
            <h2>7. Comparison with Other Methods</h2>

            <div class="highlight">
                <strong>Superior Low-AF Performance:</strong> MuTect significantly outperforms competing methods at low allele fractions, with 53.2% sensitivity at 0.1 AF compared to 29.7% (Strelka), 16.8% (JointSNVMix), and 7.4% (SomaticSniper).
            </div>

            <h3>Comparative Analysis</h3>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Sensitivity Comparison at Different Allele Fractions (30x depth)
                    </text>

                    <!-- Y-axis -->
                    <line x1="80" y1="50" x2="80" y2="400" stroke="#2c3e50" stroke-width="2"/>
                    <text x="25" y="225" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90, 25, 225)">
                        Sensitivity (%)
                    </text>

                    <!-- Y-axis labels -->
                    <text x="70" y="60" text-anchor="end" font-size="10" fill="#2c3e50">100</text>
                    <text x="70" y="130" text-anchor="end" font-size="10" fill="#2c3e50">80</text>
                    <text x="70" y="200" text-anchor="end" font-size="10" fill="#2c3e50">60</text>
                    <text x="70" y="270" text-anchor="end" font-size="10" fill="#2c3e50">40</text>
                    <text x="70" y="340" text-anchor="end" font-size="10" fill="#2c3e50">20</text>
                    <text x="70" y="410" text-anchor="end" font-size="10" fill="#2c3e50">0</text>

                    <!-- X-axis -->
                    <line x1="80" y1="400" x2="750" y2="400" stroke="#2c3e50" stroke-width="2"/>
                    <text x="415" y="430" text-anchor="middle" font-size="12" fill="#2c3e50">Allele Fraction</text>

                    <!-- X-axis labels -->
                    <text x="200" y="415" text-anchor="middle" font-size="10" fill="#2c3e50">0.05</text>
                    <text x="350" y="415" text-anchor="middle" font-size="10" fill="#2c3e50">0.1</text>
                    <text x="500" y="415" text-anchor="middle" font-size="10" fill="#2c3e50">0.2</text>
                    <text x="650" y="415" text-anchor="middle" font-size="10" fill="#2c3e50">0.4</text>

                    <!-- Method curves -->
                    <!-- MuTect -->
                    <path d="M 200 340 Q 275 200 350 130 Q 425 80 500 65 Q 575 60 650 60"
                          stroke="#e74c3c" stroke-width="4" fill="none"/>
                    <text x="680" y="65" font-size="12" fill="#e74c3c" font-weight="bold">MuTect</text>

                    <!-- Strelka -->
                    <path d="M 200 380 Q 275 280 350 200 Q 425 120 500 80 Q 575 65 650 60"
                          stroke="#f39c12" stroke-width="3" fill="none"/>
                    <text x="680" y="85" font-size="12" fill="#f39c12" font-weight="bold">Strelka</text>

                    <!-- JointSNVMix -->
                    <path d="M 200 395 Q 275 340 350 270 Q 425 180 500 120 Q 575 80 650 65"
                          stroke="#3498db" stroke-width="3" fill="none"/>
                    <text x="680" y="105" font-size="12" fill="#3498db" font-weight="bold">JointSNVMix</text>

                    <!-- SomaticSniper -->
                    <path d="M 200 398 Q 275 370 350 320 Q 425 240 500 160 Q 575 100 650 80"
                          stroke="#9b59b6" stroke-width="3" fill="none"/>
                    <text x="680" y="125" font-size="12" fill="#9b59b6" font-weight="bold">SomaticSniper</text>

                    <!-- Key performance points -->
                    <circle cx="350" cy="130" r="5" fill="#e74c3c"/>
                    <text x="355" y="115" font-size="10" fill="#e74c3c" font-weight="bold">53.2%</text>

                    <circle cx="350" cy="200" r="4" fill="#f39c12"/>
                    <text x="355" y="185" font-size="10" fill="#f39c12">29.7%</text>

                    <circle cx="350" cy="270" r="4" fill="#3498db"/>
                    <text x="355" y="255" font-size="10" fill="#3498db">16.8%</text>

                    <circle cx="350" cy="320" r="4" fill="#9b59b6"/>
                    <text x="355" y="305" font-size="10" fill="#9b59b6">7.4%</text>
                </svg>
            </div>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>AF = 0.4 (STD)</th>
                        <th>AF = 0.4 (HC)</th>
                        <th>AF = 0.1 (HC)</th>
                        <th>AF = 0.05 (HC)</th>
                        <th>Key Advantage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>MuTect</strong></td>
                        <td><span class="performance-metric good">99.3%</span></td>
                        <td><span class="performance-metric good">98.8%</span></td>
                        <td><span class="performance-metric good">53.2%</span></td>
                        <td><span class="performance-metric medium">16.0%</span></td>
                        <td>Low-AF sensitivity</td>
                    </tr>
                    <tr>
                        <td><strong>Strelka</strong></td>
                        <td><span class="performance-metric good">99.5%</span></td>
                        <td><span class="performance-metric good">98.5%</span></td>
                        <td><span class="performance-metric medium">29.7%</span></td>
                        <td><span class="performance-metric">4.6%</span></td>
                        <td>Speed, ease of use</td>
                    </tr>
                    <tr>
                        <td><strong>JointSNVMix</strong></td>
                        <td><span class="performance-metric good">99.8%</span></td>
                        <td><span class="performance-metric good">96.6%</span></td>
                        <td><span class="performance-metric">16.8%</span></td>
                        <td><span class="performance-metric">≤2.0%</span></td>
                        <td>Joint modeling</td>
                    </tr>
                    <tr>
                        <td><strong>SomaticSniper</strong></td>
                        <td><span class="performance-metric good">99.3%</span></td>
                        <td><span class="performance-metric good">91.5%</span></td>
                        <td><span class="performance-metric">7.4%</span></td>
                        <td><span class="performance-metric">≤2.0%</span></td>
                        <td>Simplicity</td>
                    </tr>
                </tbody>
            </table>

            <h3>Methodological Differences</h3>

            <div class="algorithm-box">
                <h4>Why MuTect Outperforms at Low Allele Fractions</h4>
                <div class="step">
                    <strong>Allele Fraction Modeling:</strong> Estimates f rather than assuming fixed 50% (SomaticSniper, JointSNVMix)
                </div>
                <div class="step">
                    <strong>Optimized Filters:</strong> Carefully tuned to reject false positives without sacrificing sensitivity
                </div>
                <div class="step">
                    <strong>Threshold Selection:</strong> Working point chosen for optimal sensitivity-specificity tradeoff
                </div>
                <div class="step">
                    <strong>Contamination Awareness:</strong> Explicit modeling of sample contamination effects
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(6)">← Previous</button>
                <button class="nav-button" onclick="showSection(8)">Next: Clinical Applications →</button>
            </div>
        </div>

        <!-- Section 8: Clinical Applications -->
        <div id="section8" class="section hidden">
            <h2>8. Clinical Applications & Impact</h2>

            <div class="highlight">
                <strong>Transformative Clinical Impact:</strong> MuTect enables detection of subclonal mutations in standard sequencing data, opening new avenues for understanding tumor evolution and personalized cancer therapy.
            </div>

            <h3>Clinical Significance</h3>

            <div class="svg-container">
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Clinical Applications of Enhanced Mutation Detection
                    </text>

                    <!-- Tumor Evolution -->
                    <g transform="translate(50, 60)">
                        <rect width="200" height="100" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Tumor Evolution</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Track subclonal dynamics</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Understand progression</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Predict metastasis</text>
                    </g>

                    <!-- Treatment Response -->
                    <g transform="translate(300, 60)">
                        <rect width="200" height="100" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Treatment Response</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Monitor resistance</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Adapt therapy</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Predict relapse</text>
                    </g>

                    <!-- Personalized Medicine -->
                    <g transform="translate(550, 60)">
                        <rect width="200" height="100" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Personalized Medicine</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">Targeted therapies</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="#2c3e50">Risk stratification</text>
                        <text x="100" y="75" text-anchor="middle" font-size="11" fill="#2c3e50">Prognostic markers</text>
                    </g>

                    <!-- Subclonal Architecture -->
                    <g transform="translate(150, 200)">
                        <rect width="500" height="120" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="250" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">
                            Subclonal Architecture Analysis
                        </text>

                        <!-- Tumor representation -->
                        <g transform="translate(50, 40)">
                            <circle cx="50" cy="30" r="25" fill="#e74c3c" opacity="0.8"/>
                            <text x="50" y="35" text-anchor="middle" font-size="10" fill="white">Clone A</text>
                            <text x="50" y="65" text-anchor="middle" font-size="9" fill="#2c3e50">60% cells</text>
                        </g>

                        <g transform="translate(150, 40)">
                            <circle cx="50" cy="30" r="18" fill="#3498db" opacity="0.8"/>
                            <text x="50" y="35" text-anchor="middle" font-size="10" fill="white">Clone B</text>
                            <text x="50" y="65" text-anchor="middle" font-size="9" fill="#2c3e50">25% cells</text>
                        </g>

                        <g transform="translate(250, 40)">
                            <circle cx="50" cy="30" r="12" fill="#27ae60" opacity="0.8"/>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">Clone C</text>
                            <text x="50" y="65" text-anchor="middle" font-size="9" fill="#2c3e50">10% cells</text>
                        </g>

                        <g transform="translate(350, 40)">
                            <circle cx="50" cy="30" r="8" fill="#9b59b6" opacity="0.8"/>
                            <text x="50" y="35" text-anchor="middle" font-size="8" fill="white">Clone D</text>
                            <text x="50" y="65" text-anchor="middle" font-size="9" fill="#2c3e50">5% cells</text>
                        </g>

                        <text x="250" y="95" text-anchor="middle" font-size="11" fill="#2c3e50">
                            MuTect can detect mutations in clones as small as 5-10% of tumor
                        </text>
                    </g>
                </svg>
            </div>

            <h3>Real-World Impact Examples</h3>

            <div class="algorithm-box">
                <h4>Chronic Lymphocytic Leukemia (CLL) Study</h4>
                <div class="step">
                    <strong>Discovery:</strong> Subclonal mutations in driver genes are independent prognostic factors
                </div>
                <div class="step">
                    <strong>Detection Threshold:</strong> Mutations present in as low as 10% of cancer cells (5% AF)
                </div>
                <div class="step">
                    <strong>Clinical Value:</strong> Better prediction of time to therapy beyond standard parameters
                </div>
                <div class="step">
                    <strong>Standard Sequencing:</strong> Achieved using routine exome sequencing (no ultra-deep required)
                </div>
            </div>

            <h3>Technical Advantages for Clinical Use</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Advantage</th>
                        <th>Clinical Benefit</th>
                        <th>Technical Detail</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Standard Depth Compatibility</strong></td>
                        <td>Cost-effective implementation</td>
                        <td>Works with 30-150x coverage (standard for clinical sequencing)</td>
                    </tr>
                    <tr>
                        <td><strong>High Specificity</strong></td>
                        <td>Reduces validation burden</td>
                        <td>~95% validation rate, 0.16/Mb false positive rate</td>
                    </tr>
                    <tr>
                        <td><strong>Subclonal Sensitivity</strong></td>
                        <td>Detects resistance mutations early</td>
                        <td>53% sensitivity at 10% allele fraction</td>
                    </tr>
                    <tr>
                        <td><strong>Contamination Robustness</strong></td>
                        <td>Works with impure samples</td>
                        <td>Explicit contamination modeling prevents false positives</td>
                    </tr>
                </tbody>
            </table>

            <h3>Future Directions</h3>

            <div class="math-box">
                <h4>Sensitivity Prediction for Clinical Planning</h4>
                <p>MuTect's mathematical framework enables precise sensitivity calculations for clinical study design:</p>

                <p><strong>Power Calculation:</strong> Given tumor purity (p), local copy number (c), and clonality (α):</p>
                $$\text{Expected AF} = \frac{p \cdot \alpha}{p \cdot c + 2(1-p)}$$

                <p><strong>Required Depth:</strong> Calculate sequencing depth needed for desired sensitivity</p>

                <p><strong>Mutation Absence:</strong> Assert absence of mutations with specified confidence</p>
            </div>

            <div class="highlight">
                <strong>Transformative Potential:</strong> MuTect's ability to detect low-frequency mutations in standard sequencing data enables comprehensive analysis of tumor subclonal architecture, essential for understanding cancer evolution and developing precision therapies.
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(7)">← Previous</button>
                <button class="nav-button" onclick="showSection(1)">Return to Start</button>
            </div>
        </div>

        <script>
            function showSection(sectionNum) {
                // Hide all sections
                for (let i = 1; i <= 8; i++) {
                    const section = document.getElementById('section' + i);
                    if (section) {
                        section.classList.add('hidden');
                    }
                }

                // Show the requested section
                const targetSection = document.getElementById('section' + sectionNum);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }

            // Initialize MathJax after page load
            window.addEventListener('load', function() {
                if (window.MathJax) {
                    MathJax.typesetPromise();
                }
            });
        </script>
    </div>
</body>
</html>
