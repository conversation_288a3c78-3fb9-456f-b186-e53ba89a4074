<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BCFtools: Statistical Framework and Algorithms Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .algorithm-box {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #0d47a1;
            margin-bottom: 15px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BCFtools: Statistical Framework and Algorithms</h1>
            <p>A Comprehensive Tutorial on Uncertainty-Aware Genomic Analysis</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction and Statistical Framework</a></li>
                <li><a href="#foundations">2. Mathematical Foundations</a></li>
                <li><a href="#genotype-likelihood">3. Genotype Likelihood Computation</a></li>
                <li><a href="#multi-sample">4. Multi-Sample Inference Methods</a></li>
                <li><a href="#association-testing">5. Association Testing and HWE</a></li>
                <li><a href="#allele-count">6. Allele Count Estimation</a></li>
                <li><a href="#variant-calling">7. Variant Calling and AFS Estimation</a></li>
                <li><a href="#somatic-mutations">8. Somatic Mutation Discovery</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction and Statistical Framework -->
        <div id="introduction" class="section">
            <h2>1. Introduction and Statistical Framework</h2>

            <div class="highlight">
                <strong>Paradigm Shift:</strong> BCFtools introduces a revolutionary approach to genomic analysis by working directly with uncertain sequencing data, eliminating the need for explicit genotype calling while maintaining statistical rigor.
            </div>

            <h3>The Uncertainty Problem</h3>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Traditional vs. BCFtools Approach: Handling Sequencing Uncertainty
                    </text>

                    <!-- Traditional approach -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Traditional Approach
                        </text>

                        <!-- Step 1: Raw reads -->
                        <g transform="translate(50, 40)">
                            <rect width="200" height="40" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Raw Sequencing Reads</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">Low coverage, errors</text>
                        </g>

                        <!-- Arrow down -->
                        <g transform="translate(145, 85)">
                            <line x1="0" y1="0" x2="0" y2="20" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowdown)"/>
                        </g>

                        <!-- Step 2: Genotype calling -->
                        <g transform="translate(50, 110)">
                            <rect width="200" height="40" fill="#f39c12" opacity="0.2" stroke="#f39c12" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Hard Genotype Calls</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">0/0, 0/1, 1/1, ./.</text>
                        </g>

                        <!-- Arrow down -->
                        <g transform="translate(145, 155)">
                            <line x1="0" y1="0" x2="0" y2="20" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowdown)"/>
                        </g>

                        <!-- Step 3: Analysis -->
                        <g transform="translate(50, 180)">
                            <rect width="200" height="40" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Statistical Analysis</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">Information loss!</text>
                        </g>

                        <!-- Problems -->
                        <g transform="translate(20, 240)">
                            <text x="130" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Problems:</text>
                            <text x="130" y="35" text-anchor="middle" font-size="11" fill="#2c3e50">• Information loss at low coverage</text>
                            <text x="130" y="50" text-anchor="middle" font-size="11" fill="#2c3e50">• Arbitrary thresholds</text>
                            <text x="130" y="65" text-anchor="middle" font-size="11" fill="#2c3e50">• Poor performance on rare variants</text>
                        </g>
                    </g>

                    <!-- BCFtools approach -->
                    <g transform="translate(450, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            BCFtools Approach
                        </text>

                        <!-- Step 1: Raw reads -->
                        <g transform="translate(50, 40)">
                            <rect width="200" height="40" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Raw Sequencing Reads</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">Low coverage, errors</text>
                        </g>

                        <!-- Arrow down -->
                        <g transform="translate(145, 85)">
                            <line x1="0" y1="0" x2="0" y2="20" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowdown)"/>
                        </g>

                        <!-- Step 2: Likelihood computation -->
                        <g transform="translate(50, 110)">
                            <rect width="200" height="40" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Genotype Likelihoods</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">P(data|genotype)</text>
                        </g>

                        <!-- Arrow down -->
                        <g transform="translate(145, 155)">
                            <line x1="0" y1="0" x2="0" y2="20" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowdown)"/>
                        </g>

                        <!-- Step 3: Direct analysis -->
                        <g transform="translate(50, 180)">
                            <rect width="200" height="40" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="5"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Direct Statistical Analysis</text>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="#666">No information loss!</text>
                        </g>

                        <!-- Advantages -->
                        <g transform="translate(20, 240)">
                            <text x="130" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Advantages:</text>
                            <text x="130" y="35" text-anchor="middle" font-size="11" fill="#2c3e50">• Preserves all uncertainty</text>
                            <text x="130" y="50" text-anchor="middle" font-size="11" fill="#2c3e50">• Optimal for low coverage</text>
                            <text x="130" y="65" text-anchor="middle" font-size="11" fill="#2c3e50">• Better rare variant detection</text>
                        </g>
                    </g>

                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrowdown" markerWidth="10" markerHeight="7" refX="5" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <!-- Applications -->
                    <g transform="translate(100, 350)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            BCFtools Applications
                        </text>

                        <!-- SNP calling -->
                        <g transform="translate(0, 40)">
                            <rect width="120" height="60" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#3498db">SNP Calling</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="#2c3e50">Bayesian inference</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="#2c3e50">Multi-sample</text>
                        </g>

                        <!-- Association testing -->
                        <g transform="translate(140, 40)">
                            <rect width="120" height="60" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#e74c3c">Association</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="#2c3e50">Likelihood ratio tests</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="#2c3e50">No imputation</text>
                        </g>

                        <!-- Population genetics -->
                        <g transform="translate(280, 40)">
                            <rect width="120" height="60" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#f39c12">Population</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="#2c3e50">Allele frequency</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="#2c3e50">HWE testing</text>
                        </g>

                        <!-- Somatic mutations -->
                        <g transform="translate(420, 40)">
                            <rect width="120" height="60" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#27ae60">Somatic</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="#2c3e50">Mutation discovery</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="#2c3e50">Tumor-normal</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Core Innovation</h3>

            <div class="info-box">
                <h4>Statistical Framework Principles</h4>
                <ul class="step-list">
                    <li><strong>Uncertainty Preservation:</strong> Work directly with genotype likelihoods instead of hard calls</li>
                    <li><strong>Multi-Sample Integration:</strong> Leverage information across all samples simultaneously</li>
                    <li><strong>Bayesian Inference:</strong> Use prior knowledge and posterior distributions for robust inference</li>
                    <li><strong>Optimal Coverage:</strong> Achieve maximum power at 2-6× coverage per sample</li>
                </ul>
            </div>

            <h3>Key Advantages Over Traditional Methods</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Aspect</th>
                        <th>Traditional Approach</th>
                        <th>BCFtools Framework</th>
                        <th>Benefit</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Low Coverage</strong></td>
                        <td>Poor genotype calls</td>
                        <td>Preserves uncertainty</td>
                        <td>Better accuracy</td>
                    </tr>
                    <tr>
                        <td><strong>Rare Variants</strong></td>
                        <td>Often missed</td>
                        <td>Optimal detection</td>
                        <td>Higher sensitivity</td>
                    </tr>
                    <tr>
                        <td><strong>Population Studies</strong></td>
                        <td>Requires imputation</td>
                        <td>Direct inference</td>
                        <td>No LD dependency</td>
                    </tr>
                    <tr>
                        <td><strong>Computational Speed</strong></td>
                        <td>Slow imputation</td>
                        <td>Direct calculation</td>
                        <td>Faster analysis</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning-box">
                <h4>When BCFtools Excels</h4>
                <ul>
                    <li><strong>Low-coverage sequencing:</strong> 2-6× coverage per sample</li>
                    <li><strong>Population studies:</strong> Large sample sizes with moderate coverage</li>
                    <li><strong>Rare variant discovery:</strong> Events with low population frequency</li>
                    <li><strong>Somatic mutation detection:</strong> Ultra-rare events in cancer genomics</li>
                    <li><strong>Target sequencing:</strong> Fragmented regions with poor LD</li>
                </ul>
            </div>
        </div>

        <!-- Section 2: Mathematical Foundations -->
        <div id="foundations" class="section">
            <h2>2. Mathematical Foundations</h2>

            <div class="highlight">
                <strong>Core Assumptions:</strong> BCFtools builds on three fundamental assumptions that enable tractable statistical inference while maintaining biological realism.
            </div>

            <h3>Notation and Setup</h3>

            <div class="math-box">
                <h4>Basic Notation</h4>
                <p><strong>Samples and Ploidy:</strong></p>
                <ul>
                    <li>$n$ = number of samples</li>
                    <li>$m_i$ = ploidy of the $i$-th sample (typically 2 for diploid)</li>
                    <li>$M = \sum_{i=1}^n m_i$ = total number of chromosomes</li>
                </ul>

                <p><strong>Data and Genotypes:</strong></p>
                <ul>
                    <li>$d_i$ = sequencing data for sample $i$ (bases and qualities)</li>
                    <li>$g_i$ = genotype of sample $i$ (number of reference alleles, $0 \leq g_i \leq m_i$)</li>
                    <li>$\mathcal{L}_i(\theta) = P(d_i|\theta)$ = likelihood function for sample $i$</li>
                </ul>

                <p><strong>Population Parameters:</strong></p>
                <ul>
                    <li>$\psi$ = site reference allele frequency</li>
                    <li>$\phi_k$ = probability of observing $k$ reference alleles among $M$ chromosomes</li>
                    <li>$\xi_g$ = frequency of genotype $g$</li>
                </ul>
            </div>

            <h3>Three Fundamental Assumptions</h3>

            <div class="algorithm-box">
                <h4>Assumption 1: Site Independence</h4>
                <p><strong>Statement:</strong> Data at different genomic sites are independent</p>

                <p><strong>Mathematical Expression:</strong></p>
                $$P(\text{data across sites}) = \prod_{\text{sites}} P(\text{data at site})$$

                <p><strong>Biological Justification:</strong></p>
                <ul>
                    <li>Simplifies computational complexity</li>
                    <li>Reasonable for well-separated variants</li>
                    <li>Violations can be reduced by post-filtering</li>
                </ul>

                <p><strong>Limitations:</strong> May not hold near indels or in repetitive regions</p>
            </div>

            <div class="algorithm-box">
                <h4>Assumption 2: Error and Sample Independence</h4>
                <p><strong>Statement:</strong> Sequencing errors between reads and between samples are independent</p>

                <p><strong>Mathematical Expression:</strong></p>
                $$\mathcal{L}(\text{all data}) = \prod_{i=1}^n \mathcal{L}_i(d_i|\theta)$$

                <p><strong>Critical Importance:</strong></p>
                <ul>
                    <li>Enables factorization of likelihood functions</li>
                    <li>Essential for all subsequent derivations</li>
                    <li>Allows parallel computation across samples</li>
                </ul>

                <p><strong>Real-world Considerations:</strong> Context-dependent errors may violate this assumption</p>
            </div>

            <div class="algorithm-box">
                <h4>Assumption 3: Biallelic Variants</h4>
                <p><strong>Statement:</strong> All variants have exactly two possible alleles (reference and alternative)</p>

                <p><strong>Practical Impact:</strong></p>
                <ul>
                    <li>Simplifies genotype space: $g_i \in \{0, 1, 2\}$ for diploids</li>
                    <li>Reduces computational complexity significantly</li>
                    <li>Covers ~99.8% of human SNPs</li>
                </ul>

                <p><strong>Extension:</strong> Some methods (somatic calling) drop this assumption for better accuracy</p>
            </div>
        </div>

        <!-- Section 3: Genotype Likelihood Computation -->
        <div id="genotype-likelihood" class="section">
            <h2>3. Genotype Likelihood Computation</h2>

            <div class="highlight">
                <strong>Foundation of Framework:</strong> Genotype likelihoods capture the uncertainty in sequencing data, forming the basis for all downstream statistical inference.
            </div>

            <h3>Basic Likelihood Model</h3>

            <div class="math-box">
                <h4>Single Sample Likelihood</h4>
                <p><strong>Setup:</strong> For sample with $k$ reads, where first $l$ reads match reference</p>

                <p><strong>Error Model:</strong> Let $\epsilon_j$ be the error probability of the $j$-th read</p>

                <p><strong>Likelihood Formula:</strong></p>
                $$\mathcal{L}(g) = \binom{m}{g} \prod_{j=1}^l (1-\epsilon_j)^{[g>0]} \epsilon_j^{[g=0]} \prod_{j=l+1}^k \epsilon_j^{[g<m]} (1-\epsilon_j)^{[g=m]}$$

                <p><strong>Simplified for Diploids ($m=2$):</strong></p>
                $$\mathcal{L}(g) = \begin{cases}
                \prod_{j=1}^l \epsilon_j \prod_{j=l+1}^k (1-\epsilon_j) & \text{if } g=0 \\
                \prod_{j=1}^l (1-\epsilon_j) \prod_{j=l+1}^k (1-\epsilon_j) & \text{if } g=1 \\
                \prod_{j=1}^l (1-\epsilon_j) \prod_{j=l+1}^k \epsilon_j & \text{if } g=2
                \end{cases}$$
            </div>

            <h3>Quality Score Integration</h3>

            <div class="info-box">
                <h4>Phred Quality Scores</h4>
                <p><strong>Definition:</strong> Quality score $Q$ relates to error probability $\epsilon$</p>

                <p><strong>Conversion Formula:</strong></p>
                $$\epsilon = 10^{-Q/10}$$

                <p><strong>Common Quality Scores:</strong></p>
                <ul>
                    <li><strong>Q=10:</strong> $\epsilon = 0.1$ (90% accuracy)</li>
                    <li><strong>Q=20:</strong> $\epsilon = 0.01$ (99% accuracy)</li>
                    <li><strong>Q=30:</strong> $\epsilon = 0.001$ (99.9% accuracy)</li>
                    <li><strong>Q=40:</strong> $\epsilon = 0.0001$ (99.99% accuracy)</li>
                </ul>

                <p><strong>Implementation:</strong> Each base call includes both nucleotide and quality score</p>
            </div>

            <h3>Advanced Likelihood Models</h3>

            <div class="warning-box">
                <h4>SAMtools Improvements</h4>
                <p><strong>Enhanced Error Model:</strong> SAMtools implements improvements beyond basic model</p>

                <ul>
                    <li><strong>Error Dependencies:</strong> Models context-dependent errors</li>
                    <li><strong>Mapping Quality:</strong> Incorporates alignment uncertainty</li>
                    <li><strong>Base Alignment Quality:</strong> Adjusts for indel-induced errors</li>
                    <li><strong>Strand Bias:</strong> Detects systematic errors by strand</li>
                </ul>

                <p><strong>Result:</strong> More accurate likelihood estimates, especially for indels and repetitive regions</p>
            </div>
        </div>

        <!-- Section 4: Multi-Sample Inference Methods -->
        <div id="multi-sample" class="section">
            <h2>4. Multi-Sample Inference Methods</h2>

            <div class="highlight">
                <strong>Power of Aggregation:</strong> Multi-sample analysis dramatically improves statistical power by leveraging information across all samples simultaneously.
            </div>

            <h3>Site Allele Frequency Estimation</h3>

            <div class="math-box">
                <h4>Maximum Likelihood Estimation</h4>
                <p><strong>Likelihood Function:</strong> Under Hardy-Weinberg Equilibrium</p>

                $$\mathcal{L}(\psi) = \prod_{i=1}^n \sum_{g_i=0}^{m_i} \mathcal{L}_i(g_i) \cdot P(g_i|\psi)$$

                <p><strong>Where:</strong></p>
                <ul>
                    <li>$\mathcal{L}_i(g_i)$ = genotype likelihood for sample $i$</li>
                    <li>$P(g_i|\psi) = \binom{m_i}{g_i} \psi^{g_i} (1-\psi)^{m_i-g_i}$ = binomial probability</li>
                </ul>

                <p><strong>EM Algorithm:</strong> Iterative estimation when signal is weak</p>

                $$\psi^{(t+1)} = \frac{1}{M} \sum_{i=1}^n \sum_{g_i=0}^{m_i} g_i \cdot P(g_i|d_i, \psi^{(t)})$$

                <p><strong>Where:</strong> $M = \sum_{i=1}^n m_i$ is total chromosome count</p>
            </div>

            <h3>Genotype Frequency Estimation</h3>

            <div class="math-box">
                <h4>Beyond Hardy-Weinberg</h4>
                <p><strong>General Model:</strong> Estimate frequencies $\xi_g$ for each genotype $g$</p>

                <p><strong>Likelihood:</strong></p>
                $$\mathcal{L}(\{\xi_0, \ldots, \xi_m\}) = \prod_{i=1}^n \sum_{g=0}^m \mathcal{L}_i(g) \cdot \xi_g$$

                <p><strong>EM Update:</strong></p>
                $$\xi_g^{(t+1)} = \frac{1}{n} \sum_{i=1}^n P(g|d_i, \{\xi^{(t)}\})$$

                <p><strong>Constraint:</strong> $\sum_{g=0}^m \xi_g = 1$</p>
            </div>
        </div>

        <!-- Section 5: Association Testing and HWE -->
        <div id="association-testing" class="section">
            <h2>5. Association Testing and HWE</h2>

            <div class="highlight">
                <strong>Statistical Testing:</strong> Likelihood ratio tests enable robust association analysis and Hardy-Weinberg equilibrium testing without explicit genotyping.
            </div>

            <h3>Hardy-Weinberg Equilibrium Testing</h3>

            <div class="math-box">
                <h4>Likelihood Ratio Test</h4>
                <p><strong>Test Statistic:</strong></p>

                $$D_e = 2[\log \mathcal{L}(\hat{\xi}_0, \hat{\xi}_1, \hat{\xi}_2) - \log \mathcal{L}(\hat{\psi})]$$

                <p><strong>Where:</strong></p>
                <ul>
                    <li>$\hat{\psi}$ = MLE under HWE constraint</li>
                    <li>$\hat{\xi}_0, \hat{\xi}_1, \hat{\xi}_2$ = unconstrained genotype frequency MLEs</li>
                </ul>

                <p><strong>Distribution:</strong> $D_e \sim \chi^2_1$ under null hypothesis</p>

                <p><strong>Advantage:</strong> Properly accounts for genotype uncertainty at low coverage</p>
            </div>

            <h3>Association Testing</h3>

            <div class="math-box">
                <h4>One-Degree Test (Assumes HWE)</h4>
                <p><strong>Test Statistic:</strong></p>

                $$D_{a1} = \frac{(\hat{\psi}_1 - \hat{\psi}_2)^2}{\text{Var}(\hat{\psi}_1 - \hat{\psi}_2)}$$

                <p><strong>Where:</strong> $\hat{\psi}_1, \hat{\psi}_2$ are allele frequencies in cases and controls</p>

                <p><strong>Two-Degree Test (Robust to HWE Violation):</strong></p>

                $$D_{a2} = 2[\log \mathcal{L}(\hat{\xi}_{1,0}, \hat{\xi}_{1,1}, \hat{\xi}_{1,2}, \hat{\xi}_{2,0}, \hat{\xi}_{2,1}, \hat{\xi}_{2,2}) - \log \mathcal{L}(\hat{\xi}_0, \hat{\xi}_1, \hat{\xi}_2)]$$

                <p><strong>Practical Strategy:</strong> Use $D_{a1}$ p-value but filter candidates with low $D_{a2}$ to reduce HWE-related false positives</p>
            </div>
        </div>

        <!-- Section 6: Allele Count Estimation -->
        <div id="allele-count" class="section">
            <h2>6. Allele Count Estimation</h2>

            <div class="highlight">
                <strong>Discrete Inference:</strong> Direct estimation of allele counts provides a foundation for Bayesian variant calling and population genetic inference.
            </div>

            <h3>Allele Count Likelihood</h3>

            <div class="math-box">
                <h4>Dynamic Programming Solution</h4>
                <p><strong>Goal:</strong> Compute $P(X = k|\text{data})$ where $X$ is total reference allele count</p>

                <p><strong>Recursive Definition:</strong> Define $z_{j,l}$ as probability of observing $l$ reference alleles in first $j$ samples</p>

                $$z_{j,l} = \sum_{g=0}^{m_j} \mathcal{L}_j(g) \cdot z_{j-1,l-g}$$

                <p><strong>Base Case:</strong> $z_{0,0} = 1$, $z_{0,l} = 0$ for $l > 0$</p>

                <p><strong>Final Result:</strong> $P(X = k|\text{data}) = z_{n,k}$</p>
            </div>

            <h3>Numerical Stability</h3>

            <div class="algorithm-box">
                <h4>Preventing Underflow</h4>
                <p><strong>Problem:</strong> $z_{j,l}$ values can become extremely small</p>

                <p><strong>Solution 1:</strong> Normalize by total chromosomes</p>
                $$y_{j,l} = z_{j,l} / \binom{M_j}{l}$$

                <p><strong>Solution 2:</strong> Rescaling at each step</p>
                $$\tilde{y}_{j,l} = y_{j,l} / t_j$$

                <p><strong>Where:</strong> $t_j$ chosen such that $\max_l \tilde{y}_{j,l} = 1$</p>

                <p><strong>Implementation:</strong> Compute in bands rather than full triangle for efficiency</p>
            </div>
        </div>

        <!-- Section 7: Variant Calling and AFS Estimation -->
        <div id="variant-calling" class="section">
            <h2>7. Variant Calling and AFS Estimation</h2>

            <div class="highlight">
                <strong>Bayesian Framework:</strong> Variant calling uses prior knowledge about allele frequency spectra to make optimal decisions under uncertainty.
            </div>

            <h3>Bayesian Variant Calling</h3>

            <div class="math-box">
                <h4>Posterior Distribution</h4>
                <p><strong>Prior:</strong> Allele frequency spectrum $\Phi = \{\phi_k\}$</p>

                <p><strong>Posterior:</strong></p>
                $$P(X = k|\text{data}) = \frac{\mathcal{L}(k) \cdot \phi_k}{\sum_{j=0}^M \mathcal{L}(j) \cdot \phi_j}$$

                <p><strong>Variant Quality:</strong></p>
                $$Q_{\text{var}} = -10 \log_{10} P(X = M|\text{data})$$

                <p><strong>Decision Rule:</strong> Call variant if $Q_{\text{var}} > \text{threshold}$</p>
            </div>

            <h3>Allele Frequency Spectrum Estimation</h3>

            <div class="math-box">
                <h4>EM Algorithm for AFS</h4>
                <p><strong>Goal:</strong> Estimate $\Phi$ from multiple sites</p>

                <p><strong>EM Update:</strong></p>
                $$\phi_k^{(t+1)} = \frac{1}{L} \sum_{a=1}^L P(X_a = k|d_a, \Phi^{(t)})$$

                <p><strong>Where:</strong> $L$ = number of sites, $X_a$ = allele count at site $a$</p>

                <p><strong>Alternative (Site-AFS):</strong> Histogram of maximum likelihood allele counts</p>
            </div>
        </div>

        <!-- Section 8: Somatic Mutation Discovery -->
        <div id="somatic-mutations" class="section">
            <h2>8. Somatic Mutation Discovery</h2>

            <div class="highlight">
                <strong>Ultra-Rare Events:</strong> Somatic mutation detection requires specialized likelihood ratio methods to distinguish true mutations from sequencing artifacts.
            </div>

            <h3>Tumor-Normal Comparison</h3>

            <div class="math-box">
                <h4>Likelihood Ratio Score</h4>
                <p><strong>Test Statistic:</strong></p>

                $$D_p = \log \frac{\max_{g_1,g_2} \mathcal{L}_1(g_1) \mathcal{L}_2(g_2)}{\max_{g} \mathcal{L}_1(g) \mathcal{L}_2(g)}$$

                <p><strong>Interpretation:</strong></p>
                <ul>
                    <li>Numerator: Best fit allowing different genotypes</li>
                    <li>Denominator: Best fit assuming same genotype</li>
                    <li>Large $D_p$ indicates likely somatic mutation</li>
                </ul>

                <p><strong>Intuitive Form:</strong> When $\hat{g}_{\text{joint}} \in \{\hat{g}_1, \hat{g}_2\}$:</p>
                $$D_p = \log \mathcal{L}_1(\hat{g}_1) + \log \mathcal{L}_2(\hat{g}_2) - \log \mathcal{L}_1(\hat{g}_{\text{joint}}) - \log \mathcal{L}_2(\hat{g}_{\text{joint}})$$
            </div>

            <h3>Family Trio Analysis</h3>

            <div class="math-box">
                <h4>De Novo Mutation Detection</h4>
                <p><strong>Likelihood Ratio:</strong></p>

                $$D_{\text{trio}} = \log \frac{\max_{g_c,g_f,g_m} \mathcal{L}_c(g_c) \mathcal{L}_f(g_f) \mathcal{L}_m(g_m)}{\max_{(g_c,g_f,g_m) \in \mathcal{M}} \mathcal{L}_c(g_c) \mathcal{L}_f(g_f) \mathcal{L}_m(g_m)}$$

                <p><strong>Where:</strong> $\mathcal{M}$ = set of genotype configurations satisfying Mendelian inheritance</p>

                <p><strong>Extension:</strong> Drops biallelic assumption for better accuracy with complex variants</p>
            </div>

            <h3>Practical Considerations</h3>

            <div class="warning-box">
                <h4>Major Sources of Error</h4>
                <ul>
                    <li><strong>Mapping Errors:</strong> Leading cause of false positives in rare event detection</li>
                    <li><strong>Systematic Biases:</strong> Importance of using symmetric datasets</li>
                    <li><strong>Reference Bias:</strong> Aligners favor reference sequence</li>
                    <li><strong>Repetitive Regions:</strong> Increased error rates in complex genomic regions</li>
                </ul>

                <p><strong>Mitigation Strategies:</strong></p>
                <ul>
                    <li>Use multiple mapping algorithms and take intersection</li>
                    <li>Apply strict quality filters and depth limits</li>
                    <li>Remove clustered variants in windows</li>
                    <li>Validate with orthogonal sequencing technologies</li>
                </ul>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Initialize MathJax after page load
        window.addEventListener('load', function() {
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        });
    </script>
</body>
</html>
