<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding GATK: The Genome Analysis Toolkit</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .nav {
            background: #34495e;
            padding: 1rem 2rem;
            border-bottom: 3px solid #3498db;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .nav a:hover {
            background-color: #3498db;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            border-left: 4px solid #3498db;
            padding-left: 2rem;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .section h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .key-concept {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 8px 8px 0;
        }
        
        .formula-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 2rem;
            margin: 1.5rem 0;
            text-align: center;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            border-left: 4px solid #4299e1;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .performance-table th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 1rem;
            text-align: left;
        }
        
        .performance-table td {
            padding: 0.8rem 1rem;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .performance-table tr:hover {
            background: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .content {
                padding: 1rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Understanding GATK</h1>
            <p class="subtitle">The Genome Analysis Toolkit: A MapReduce Framework for Next-Generation DNA Sequencing</p>
        </header>
        
        <nav class="nav">
            <ul>
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#architecture">Architecture</a></li>
                <li><a href="#components">Components</a></li>
                <li><a href="#data-handling">Data Handling</a></li>
                <li><a href="#parallelization">Parallelization</a></li>
                <li><a href="#examples">Examples</a></li>
                <li><a href="#mathematics">Mathematics</a></li>
            </ul>
        </nav>
        
        <main class="content">
            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>Introduction and Overview</h2>
                
                <div class="key-concept">
                    <strong>What is GATK?</strong> The Genome Analysis Toolkit (GATK) is a structured Java programming framework designed to ease the development of efficient and robust analysis tools for next-generation DNA sequencing data using the MapReduce paradigm.
                </div>
                
                <p>Next-generation DNA sequencing (NGS) projects have revolutionized our understanding of genetic variation, but the massive datasets they generate present significant computational challenges. The 1000 Genomes Project pilot alone includes nearly five terabases of data, making it difficult for even computationally sophisticated researchers to write feature-rich, efficient analysis tools.</p>
                
                <h3>The Challenge</h3>
                <p>Before GATK, researchers faced several obstacles:</p>
                <ul>
                    <li><strong>Complex Data Management:</strong> Accessing and manipulating massive sequencing datasets was tedious and error-prone</li>
                    <li><strong>Development Gap:</strong> Large gap between sequencing output and analysis results</li>
                    <li><strong>Limited Frameworks:</strong> Existing tools focused on specific analysis problems or required specialized knowledge</li>
                    <li><strong>Scalability Issues:</strong> Difficulty in parallelizing and distributing computational tasks</li>
                </ul>
                
                <h3>The GATK Solution</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🔧 Structured Framework</h4>
                        <p>Separates analysis calculations from data management infrastructure, allowing developers to focus on algorithms rather than data handling.</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚡ MapReduce Architecture</h4>
                        <p>Uses the proven MapReduce paradigm for automatic parallelization and distributed computing capabilities.</p>
                    </div>
                    <div class="feature-card">
                        <h4>📊 Rich Data Access</h4>
                        <p>Provides comprehensive data access patterns that encompass the majority of analysis tool needs.</p>
                    </div>
                    <div class="feature-card">
                        <h4>🚀 Performance Optimized</h4>
                        <p>Continuously optimized for correctness, stability, CPU and memory efficiency.</p>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <h3>Key Benefits</h3>
                    <ul>
                        <li><strong>Rapid Development:</strong> Tools can be written in just dozens of lines of code</li>
                        <li><strong>Automatic Parallelization:</strong> Built-in support for shared memory and distributed computing</li>
                        <li><strong>Platform Independence:</strong> Works with data from any sequencing platform via SAM/BAM format</li>
                        <li><strong>Production Ready:</strong> Used in major projects like 1000 Genomes and The Cancer Genome Atlas</li>
                    </ul>
                </div>
                         </section>
              
              <!-- Components Section -->
              <section id="components" class="section">
                  <h2>Core Components: Traversals and Walkers</h2>
                  
                  <p>GATK's power comes from its well-designed separation of concerns. The framework handles data management while users focus on analysis algorithms. This is achieved through two key components:</p>
                  
                  <h3>Traversal Types</h3>
                  <p>Traversals are data presentation schemes that determine how genomic data is fed to analysis walkers. GATK provides a small but comprehensive set of traversal types that satisfy most analysis needs.</p>
                  
                  <div class="svg-container">
                      <h3>Read-based vs Locus-based Traversals</h3>
                      <svg width="800" height="350" viewBox="0 0 800 350">
                          <!-- Read-based traversal -->
                          <g>
                              <text x="200" y="30" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Read-based Traversal</text>
                              
                              <!-- Reference genome -->
                              <rect x="50" y="60" width="300" height="20" fill="#95a5a6" stroke="#7f8c8d"/>
                              <text x="200" y="75" text-anchor="middle" fill="white" font-size="12">Reference Genome</text>
                              
                              <!-- Reads -->
                              <rect x="70" y="100" width="80" height="15" fill="#3498db" opacity="0.8"/>
                              <text x="110" y="112" text-anchor="middle" fill="white" font-size="10">Read 1</text>
                              
                              <rect x="140" y="120" width="90" height="15" fill="#3498db" opacity="0.8"/>
                              <text x="185" y="132" text-anchor="middle" fill="white" font-size="10">Read 2</text>
                              
                              <rect x="110" y="140" width="85" height="15" fill="#3498db" opacity="0.8"/>
                              <text x="152" y="152" text-anchor="middle" fill="white" font-size="10">Read 3</text>
                              
                              <rect x="250" y="110" width="75" height="15" fill="#3498db" opacity="0.8"/>
                              <text x="287" y="122" text-anchor="middle" fill="white" font-size="10">Read 4</text>
                              
                              <!-- Arrows showing individual read processing -->
                              <defs>
                                  <marker id="blueArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                      <polygon points="0 0, 8 3, 0 6" fill="#3498db"/>
                                  </marker>
                              </defs>
                              
                              <path d="M 110 180 Q 110 200 80 220" stroke="#3498db" stroke-width="2" fill="none" marker-end="url(#blueArrow)"/>
                              <path d="M 185 180 Q 185 200 120 220" stroke="#3498db" stroke-width="2" fill="none" marker-end="url(#blueArrow)"/>
                              <path d="M 152 180 Q 152 200 160 220" stroke="#3498db" stroke-width="2" fill="none" marker-end="url(#blueArrow)"/>
                              <path d="M 287 180 Q 287 200 200 220" stroke="#3498db" stroke-width="2" fill="none" marker-end="url(#blueArrow)"/>
                              
                              <rect x="50" y="220" width="200" height="40" fill="#2ecc71" rx="5"/>
                              <text x="150" y="238" text-anchor="middle" fill="white" font-size="12">Walker processes</text>
                              <text x="150" y="252" text-anchor="middle" fill="white" font-size="12">each read individually</text>
                          </g>
                          
                          <!-- Locus-based traversal -->
                          <g>
                              <text x="600" y="30" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Locus-based Traversal</text>
                              
                              <!-- Reference genome with position markers -->
                              <rect x="450" y="60" width="300" height="20" fill="#95a5a6" stroke="#7f8c8d"/>
                              <text x="600" y="75" text-anchor="middle" fill="white" font-size="12">Reference Genome</text>
                              
                              <!-- Position markers -->
                              <line x1="500" y1="55" x2="500" y2="85" stroke="#e74c3c" stroke-width="2"/>
                              <line x1="550" y1="55" x2="550" y2="85" stroke="#e74c3c" stroke-width="2"/>
                              <line x1="600" y1="55" x2="600" y2="85" stroke="#e74c3c" stroke-width="2"/>
                              <line x1="650" y1="55" x2="650" y2="85" stroke="#e74c3c" stroke-width="2"/>
                              <line x1="700" y1="55" x2="700" y2="85" stroke="#e74c3c" stroke-width="2"/>
                              
                              <!-- Reads -->
                              <rect x="470" y="100" width="80" height="15" fill="#3498db" opacity="0.8"/>
                              <rect x="540" y="120" width="90" height="15" fill="#3498db" opacity="0.8"/>
                              <rect x="510" y="140" width="85" height="15" fill="#3498db" opacity="0.8"/>
                              <rect x="650" y="110" width="75" height="15" fill="#3498db" opacity="0.8"/>
                              
                              <!-- Pileup illustration at position 600 -->
                              <circle cx="600" cy="107" r="3" fill="#e74c3c"/>
                              <circle cx="600" cy="127" r="3" fill="#e74c3c"/>
                              <circle cx="600" cy="147" r="3" fill="#e74c3c"/>
                              
                              <line x1="600" y1="180" x2="600" y2="200" stroke="#e74c3c" stroke-width="3"/>
                              <polygon points="595 200, 605 200, 600 210" fill="#e74c3c"/>
                              
                              <rect x="550" y="220" width="100" height="40" fill="#e74c3c" rx="5"/>
                              <text x="600" y="238" text-anchor="middle" fill="white" font-size="12">Walker processes</text>
                              <text x="600" y="252" text-anchor="middle" fill="white" font-size="12">pileup at each locus</text>
                              
                              <text x="600" y="280" text-anchor="middle" fill="#7f8c8d" font-size="10">Position 12345</text>
                          </g>
                          
                          <!-- Legend -->
                          <g transform="translate(50, 300)">
                              <rect x="0" y="0" width="15" height="15" fill="#3498db"/>
                              <text x="20" y="12" fill="#2c3e50" font-size="12">Sequencing Reads</text>
                              
                              <circle cx="150" cy="7" r="3" fill="#e74c3c"/>
                              <text x="160" y="12" fill="#2c3e50" font-size="12">Read bases at locus</text>
                              
                              <rect x="280" y="0" width="15" height="15" fill="#95a5a6"/>
                              <text x="300" y="12" fill="#2c3e50" font-size="12">Reference genome</text>
                          </g>
                      </svg>
                  </div>
                  
                  <div class="feature-grid">
                      <div class="feature-card">
                          <h4>📖 Read-based Traversal</h4>
                          <p><strong>Use case:</strong> Quality score analysis, alignment scoring, read merging</p>
                          <p><strong>Data provided:</strong> Individual reads with overlapping reference sequence</p>
                          <p><strong>Processing:</strong> Each read processed once and only once</p>
                      </div>
                      <div class="feature-card">
                          <h4>🎯 Locus-based Traversal</h4>
                          <p><strong>Use case:</strong> SNP calling, coverage analysis, variant detection</p>
                          <p><strong>Data provided:</strong> All reads covering each genome position + reference base + annotations</p>
                          <p><strong>Processing:</strong> Most commonly used traversal type</p>
                      </div>
                  </div>
                  
                  <h3>Sharding: Managing Massive Data</h3>
                  <div class="highlight-box">
                      <p>One of GATK's most innovative features is its <strong>sharding system</strong>. Instead of loading entire chromosomes into memory, GATK intelligently divides genomic data into multikilobase-sized "shards" that contain:</p>
                      <ul>
                          <li>Reference bases for the genomic region</li>
                          <li>All overlapping sequencing reads</li>
                          <li>Associated annotation data (SNPs, intervals, etc.)</li>
                          <li>Quality scores and metadata</li>
                      </ul>
                      <p>This approach enables efficient memory usage and facilitates parallelization across different genomic regions.</p>
                  </div>
                  
                  <div class="svg-container">
                      <h3>Sharding Mechanism</h3>
                      <svg width="700" height="250" viewBox="0 0 700 250">
                          <!-- Chromosome -->
                          <rect x="50" y="100" width="600" height="30" fill="#34495e" rx="15"/>
                          <text x="350" y="120" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Chromosome (247 Mb)</text>
                          
                          <!-- Shard divisions -->
                          <line x1="150" y1="80" x2="150" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                          <line x1="250" y1="80" x2="250" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                          <line x1="350" y1="80" x2="350" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                          <line x1="450" y1="80" x2="450" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                          <line x1="550" y1="80" x2="550" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                          
                          <!-- Shard labels -->
                          <text x="100" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">Shard 1</text>
                          <text x="200" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">Shard 2</text>
                          <text x="300" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">Shard 3</text>
                          <text x="400" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">Shard 4</text>
                          <text x="500" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">Shard 5</text>
                          <text x="600" y="170" text-anchor="middle" fill="#2c3e50" font-size="12">...</text>
                          
                          <!-- Size indicators -->
                          <text x="100" y="185" text-anchor="middle" fill="#7f8c8d" font-size="10">~50kb</text>
                          <text x="200" y="185" text-anchor="middle" fill="#7f8c8d" font-size="10">~50kb</text>
                          <text x="300" y="185" text-anchor="middle" fill="#7f8c8d" font-size="10">~50kb</text>
                          <text x="400" y="185" text-anchor="middle" fill="#7f8c8d" font-size="10">~50kb</text>
                          <text x="500" y="185" text-anchor="middle" fill="#7f8c8d" font-size="10">~50kb</text>
                          
                          <!-- Parallel processing arrows -->
                          <g transform="translate(100, 40)">
                              <polygon points="0 20, 10 15, 10 18, 40 18, 40 22, 10 22, 10 25" fill="#2ecc71"/>
                              <text x="45" y="22" fill="#2ecc71" font-size="12">CPU 1</text>
                          </g>
                          <g transform="translate(200, 40)">
                              <polygon points="0 20, 10 15, 10 18, 40 18, 40 22, 10 22, 10 25" fill="#2ecc71"/>
                              <text x="45" y="22" fill="#2ecc71" font-size="12">CPU 2</text>
                          </g>
                          <g transform="translate(300, 40)">
                              <polygon points="0 20, 10 15, 10 18, 40 18, 40 22, 10 22, 10 25" fill="#2ecc71"/>
                              <text x="45" y="22" fill="#2ecc71" font-size="12">CPU 3</text>
                          </g>
                          <g transform="translate(400, 40)">
                              <polygon points="0 20, 10 15, 10 18, 40 18, 40 22, 10 22, 10 25" fill="#2ecc71"/>
                              <text x="45" y="22" fill="#2ecc71" font-size="12">CPU 4</text>
                          </g>
                          
                          <text x="350" y="20" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Parallel Processing of Shards</text>
                      </svg>
                  </div>
                             </section>
               
               <!-- Data Handling Section -->
               <section id="data-handling" class="section">
                   <h2>Data Handling and Input Formats</h2>
                   
                   <p>GATK is designed to work with the standard formats used in next-generation sequencing, making it compatible with data from any sequencing platform and most alignment tools.</p>
                   
                   <h3>Supported Input Formats</h3>
                   <div class="feature-grid">
                       <div class="feature-card">
                           <h4>📊 SAM/BAM Files</h4>
                           <p><strong>Primary format:</strong> Sequence Alignment/Map format</p>
                           <p><strong>BAM:</strong> Binary compressed version for performance</p>
                           <p><strong>Features:</strong> Indexed for fast random access</p>
                       </div>
                       <div class="feature-card">
                           <h4>🧬 Reference Genome</h4>
                           <p><strong>FASTA format:</strong> Standard reference sequences</p>
                           <p><strong>Indexed:</strong> For efficient sequence retrieval</p>
                           <p><strong>Compatibility:</strong> Works with any genome assembly</p>
                       </div>
                       <div class="feature-card">
                           <h4>🔍 Annotation Data</h4>
                           <p><strong>dbSNP:</strong> Known variant databases</p>
                           <p><strong>HapMap:</strong> Population variation data</p>
                           <p><strong>Custom:</strong> User-defined annotation tracks</p>
                       </div>
                       <div class="feature-card">
                           <h4>📋 Variant Formats</h4>
                           <p><strong>VCF:</strong> Variant Call Format</p>
                           <p><strong>GLF:</strong> Genotype likelihood format</p>
                           <p><strong>GELI:</strong> Custom text format</p>
                       </div>
                   </div>
                   
                   <div class="svg-container">
                       <h3>Data Flow and Format Conversion</h3>
                       <svg width="800" height="300" viewBox="0 0 800 300">
                           <!-- Input sources -->
                           <g>
                               <rect x="50" y="50" width="80" height="40" fill="#3498db" rx="5"/>
                               <text x="90" y="70" text-anchor="middle" fill="white" font-size="10">Illumina</text>
                               <text x="90" y="82" text-anchor="middle" fill="white" font-size="10">Sequencer</text>
                               
                               <rect x="50" y="120" width="80" height="40" fill="#3498db" rx="5"/>
                               <text x="90" y="140" text-anchor="middle" fill="white" font-size="10">SOLiD</text>
                               <text x="90" y="152" text-anchor="middle" fill="white" font-size="10">System</text>
                               
                               <rect x="50" y="190" width="80" height="40" fill="#3498db" rx="5"/>
                               <text x="90" y="210" text-anchor="middle" fill="white" font-size="10">454</text>
                               <text x="90" y="222" text-anchor="middle" fill="white" font-size="10">Sequencer</text>
                           </g>
                           
                           <!-- Alignment tools -->
                           <g>
                               <rect x="180" y="80" width="100" height="30" fill="#2ecc71" rx="5"/>
                               <text x="230" y="95" text-anchor="middle" fill="white" font-size="10">BWA Aligner</text>
                               
                               <rect x="180" y="130" width="100" height="30" fill="#2ecc71" rx="5"/>
                               <text x="230" y="145" text-anchor="middle" fill="white" font-size="10">MAQ Aligner</text>
                               
                               <rect x="180" y="180" width="100" height="30" fill="#2ecc71" rx="5"/>
                               <text x="230" y="195" text-anchor="middle" fill="white" font-size="10">SSAHA2</text>
                           </g>
                           
                           <!-- SAM/BAM -->
                           <rect x="320" y="100" width="120" height="80" fill="#e74c3c" rx="8"/>
                           <text x="380" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">SAM/BAM</text>
                           <text x="380" y="140" text-anchor="middle" fill="white" font-size="10">Standard Format</text>
                           <text x="380" y="155" text-anchor="middle" fill="white" font-size="10">Indexed & Sorted</text>
                           <text x="380" y="170" text-anchor="middle" fill="white" font-size="10">Read Coordinates</text>
                           
                           <!-- GATK Engine -->
                           <rect x="480" y="100" width="120" height="80" fill="#9b59b6" rx="8"/>
                           <text x="540" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">GATK</text>
                           <text x="540" y="140" text-anchor="middle" fill="white" font-size="10">Processing</text>
                           <text x="540" y="155" text-anchor="middle" fill="white" font-size="10">Engine</text>
                           <text x="540" y="170" text-anchor="middle" fill="white" font-size="10">Sharding</text>
                           
                           <!-- Output -->
                           <rect x="640" y="100" width="120" height="80" fill="#f39c12" rx="8"/>
                           <text x="700" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Analysis</text>
                           <text x="700" y="140" text-anchor="middle" fill="white" font-size="10">Results</text>
                           <text x="700" y="155" text-anchor="middle" fill="white" font-size="10">VCF, Stats</text>
                           <text x="700" y="170" text-anchor="middle" fill="white" font-size="10">Coverage</text>
                           
                           <!-- Arrows -->
                           <defs>
                               <marker id="dataArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                   <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                               </marker>
                           </defs>
                           
                           <!-- Input to alignment -->
                           <line x1="130" y1="70" x2="180" y2="95" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           <line x1="130" y1="140" x2="180" y2="145" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           <line x1="130" y1="210" x2="180" y2="195" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           
                           <!-- Alignment to SAM/BAM -->
                           <line x1="280" y1="95" x2="320" y2="120" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           <line x1="280" y1="145" x2="320" y2="140" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           <line x1="280" y1="195" x2="320" y2="160" stroke="#34495e" stroke-width="2" marker-end="url(#dataArrow)"/>
                           
                           <!-- SAM/BAM to GATK -->
                           <line x1="440" y1="140" x2="480" y2="140" stroke="#34495e" stroke-width="3" marker-end="url(#dataArrow)"/>
                           
                           <!-- GATK to Output -->
                           <line x1="600" y1="140" x2="640" y2="140" stroke="#34495e" stroke-width="3" marker-end="url(#dataArrow)"/>
                           
                           <!-- Title -->
                           <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Data Processing Pipeline</text>
                       </svg>
                   </div>
                   
                   <h3>Interval Processing</h3>
                   <div class="highlight-box">
                       <p>Many biological questions focus on specific genomic regions. GATK allows users to specify <strong>active intervals</strong> to target only regions of interest:</p>
                       <ul>
                           <li><strong>Exon analysis:</strong> Process only coding regions of genes</li>
                           <li><strong>HapMap sites:</strong> Focus on known polymorphic positions</li>
                           <li><strong>Custom regions:</strong> User-defined genomic intervals</li>
                           <li><strong>Multiple intervals:</strong> Process several non-contiguous regions</li>
                       </ul>
                       <p>Intervals can be specified in common formats like UCSC's BED format or GATK's custom interval format.</p>
                   </div>
                   
                   <h3>File Merging Capabilities</h3>
                   <p>GATK can seamlessly merge multiple BAM files on-the-fly, enabling analysis of:</p>
                   <div class="feature-grid">
                       <div class="feature-card">
                           <h4>🔗 Multiple Sequencing Runs</h4>
                           <p>Combine data from different sequencing experiments for the same sample</p>
                       </div>
                       <div class="feature-card">
                           <h4>👥 Population Analysis</h4>
                           <p>Merge data from multiple individuals for cohort studies</p>
                       </div>
                       <div class="feature-card">
                           <h4>📊 Preserved Metadata</h4>
                           <p>Read-group information and sequencing metadata are maintained</p>
                       </div>
                       <div class="feature-card">
                           <h4>💾 Optional Output</h4>
                           <p>Merged data can be written to disk for later use</p>
                       </div>
                   </div>
               </section>
               
               <!-- Parallelization Section -->
               <section id="parallelization" class="section">
                   <h2>Parallelization Strategies</h2>
                   
                   <p>GATK provides multiple approaches for parallelizing computational tasks, enabling efficient processing of massive genomic datasets across different computing environments.</p>
                   
                   <h3>Shared Memory Parallelization</h3>
                   <div class="highlight-box">
                       <p>GATK supports automatic shared-memory parallelization where multiple instances of the traversal engine and walker run on a single machine. Walkers that implement the <strong>TreeReducible interface</strong> can automatically benefit from this parallelization.</p>
                   </div>
                   
                   <div class="svg-container">
                       <h3>Shared Memory Parallel Processing</h3>
                       <svg width="800" height="400" viewBox="0 0 800 400">
                           <!-- Input data -->
                           <rect x="50" y="50" width="120" height="60" fill="#3498db" rx="8"/>
                           <text x="110" y="75" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Input Data</text>
                           <text x="110" y="90" text-anchor="middle" fill="white" font-size="10">BAM + Reference</text>
                           <text x="110" y="105" text-anchor="middle" fill="white" font-size="10">247M loci</text>
                           
                           <!-- Sharding -->
                           <rect x="220" y="30" width="80" height="40" fill="#2ecc71" rx="5"/>
                           <text x="260" y="50" text-anchor="middle" fill="white" font-size="10">Shard 1</text>
                           
                           <rect x="220" y="80" width="80" height="40" fill="#2ecc71" rx="5"/>
                           <text x="260" y="100" text-anchor="middle" fill="white" font-size="10">Shard 2</text>
                           
                           <rect x="220" y="130" width="80" height="40" fill="#2ecc71" rx="5"/>
                           <text x="260" y="150" text-anchor="middle" fill="white" font-size="10">Shard 3</text>
                           
                           <rect x="220" y="180" width="80" height="40" fill="#2ecc71" rx="5"/>
                           <text x="260" y="200" text-anchor="middle" fill="white" font-size="10">Shard N</text>
                           
                           <!-- Threads -->
                           <g>
                               <rect x="350" y="20" width="100" height="50" fill="#e74c3c" rx="8"/>
                               <text x="400" y="35" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Thread 1</text>
                               <text x="400" y="50" text-anchor="middle" fill="white" font-size="9">Walker Instance</text>
                               <text x="400" y="62" text-anchor="middle" fill="white" font-size="9">Map/Reduce</text>
                               
                               <rect x="350" y="80" width="100" height="50" fill="#e74c3c" rx="8"/>
                               <text x="400" y="95" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Thread 2</text>
                               <text x="400" y="110" text-anchor="middle" fill="white" font-size="9">Walker Instance</text>
                               <text x="400" y="122" text-anchor="middle" fill="white" font-size="9">Map/Reduce</text>
                               
                               <rect x="350" y="140" width="100" height="50" fill="#e74c3c" rx="8"/>
                               <text x="400" y="155" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Thread 3</text>
                               <text x="400" y="170" text-anchor="middle" fill="white" font-size="9">Walker Instance</text>
                               <text x="400" y="182" text-anchor="middle" fill="white" font-size="9">Map/Reduce</text>
                               
                               <rect x="350" y="200" width="100" height="50" fill="#e74c3c" rx="8"/>
                               <text x="400" y="215" text-anchor="middle" fill="white" font-size="10" font-weight="bold">Thread N</text>
                               <text x="400" y="230" text-anchor="middle" fill="white" font-size="9">Walker Instance</text>
                               <text x="400" y="242" text-anchor="middle" fill="white" font-size="9">Map/Reduce</text>
                           </g>
                           
                           <!-- Results -->
                           <rect x="500" y="30" width="80" height="40" fill="#9b59b6" rx="5"/>
                           <text x="540" y="50" text-anchor="middle" fill="white" font-size="10">Result 1</text>
                           
                           <rect x="500" y="80" width="80" height="40" fill="#9b59b6" rx="5"/>
                           <text x="540" y="100" text-anchor="middle" fill="white" font-size="10">Result 2</text>
                           
                           <rect x="500" y="130" width="80" height="40" fill="#9b59b6" rx="5"/>
                           <text x="540" y="150" text-anchor="middle" fill="white" font-size="10">Result 3</text>
                           
                           <rect x="500" y="180" width="80" height="40" fill="#9b59b6" rx="5"/>
                           <text x="540" y="200" text-anchor="middle" fill="white" font-size="10">Result N</text>
                           
                           <!-- Tree Reduction -->
                           <rect x="620" y="100" width="120" height="80" fill="#f39c12" rx="8"/>
                           <text x="680" y="120" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Tree Reduction</text>
                           <text x="680" y="135" text-anchor="middle" fill="white" font-size="10">Merge Results</text>
                           <text x="680" y="150" text-anchor="middle" fill="white" font-size="10">In Order</text>
                           <text x="680" y="165" text-anchor="middle" fill="white" font-size="10">Reference-based</text>
                           
                           <!-- Final Output -->
                           <rect x="780" y="100" width="100" height="80" fill="#34495e" rx="8"/>
                           <text x="830" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Final</text>
                           <text x="830" y="140" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Output</text>
                           <text x="830" y="155" text-anchor="middle" fill="white" font-size="10">VCF File</text>
                           <text x="830" y="170" text-anchor="middle" fill="white" font-size="10">Genotypes</text>
                           
                           <!-- Arrows -->
                           <defs>
                               <marker id="parallelArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                   <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                               </marker>
                           </defs>
                           
                           <!-- Input to shards -->
                           <line x1="170" y1="80" x2="220" y2="50" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="170" y1="80" x2="220" y2="100" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="170" y1="80" x2="220" y2="150" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="170" y1="80" x2="220" y2="200" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           
                           <!-- Shards to threads -->
                           <line x1="300" y1="50" x2="350" y2="45" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="300" y1="100" x2="350" y2="105" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="300" y1="150" x2="350" y2="165" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="300" y1="200" x2="350" y2="225" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           
                           <!-- Threads to results -->
                           <line x1="450" y1="45" x2="500" y2="50" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="450" y1="105" x2="500" y2="100" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="450" y1="165" x2="500" y2="150" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="450" y1="225" x2="500" y2="200" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           
                           <!-- Results to tree reduction -->
                           <line x1="580" y1="50" x2="620" y2="120" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="580" y1="100" x2="620" y2="130" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="580" y1="150" x2="620" y2="140" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           <line x1="580" y1="200" x2="620" y2="150" stroke="#34495e" stroke-width="2" marker-end="url(#parallelArrow)"/>
                           
                           <!-- Tree reduction to final output -->
                           <line x1="740" y1="140" x2="780" y2="140" stroke="#34495e" stroke-width="3" marker-end="url(#parallelArrow)"/>
                           
                           <!-- Title -->
                           <text x="400" y="15" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Shared Memory Parallelization</text>
                           
                           <!-- Performance metrics -->
                           <text x="400" y="320" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Performance Scaling</text>
                           <text x="400" y="340" text-anchor="middle" fill="#7f8c8d" font-size="12">Single CPU: 863 minutes → 12 CPUs: ~72 minutes (12x speedup)</text>
                       </svg>
                   </div>
                   
                   <h3>Distributed Computing</h3>
                   <p>For even larger datasets, GATK supports distributed parallelization across multiple machines:</p>
                   <div class="feature-grid">
                       <div class="feature-card">
                           <h4>🌐 Cluster Computing</h4>
                           <p>Split tasks by genomic locations (e.g., by chromosome) and distribute to multiple machines</p>
                       </div>
                       <div class="feature-card">
                           <h4>⚙️ Job Management</h4>
                           <p>Compatible with Sun Grid Engine, LSF, and other job scheduling systems</p>
                       </div>
                       <div class="feature-card">
                           <h4>📊 Scalability</h4>
                           <p>Can scale to 50+ processors with near-linear performance gains</p>
                       </div>
                       <div class="feature-card">
                           <h4>🔧 Flexibility</h4>
                           <p>Allocate CPU resources based on analysis priority and completion requirements</p>
                       </div>
                   </div>
                   
                   <div class="performance-table">
                       <table>
                           <thead>
                               <tr>
                                   <th>Processing Method</th>
                                   <th>Processor Count</th>
                                   <th>Time (minutes)</th>
                                   <th>Speedup Factor</th>
                               </tr>
                           </thead>
                           <tbody>
                               <tr>
                                   <td>Single CPU</td>
                                   <td>1</td>
                                   <td>863</td>
                                   <td>1.0x</td>
                               </tr>
                               <tr>
                                   <td>Shared Memory</td>
                                   <td>4</td>
                                   <td>~216</td>
                                   <td>4.0x</td>
                               </tr>
                               <tr>
                                   <td>Shared Memory</td>
                                   <td>8</td>
                                   <td>~108</td>
                                   <td>8.0x</td>
                               </tr>
                               <tr>
                                   <td>Shared Memory</td>
                                   <td>12</td>
                                   <td>~72</td>
                                   <td>12.0x</td>
                               </tr>
                               <tr>
                                   <td>Distributed</td>
                                   <td>25</td>
                                   <td>~35</td>
                                   <td>24.7x</td>
                               </tr>
                               <tr>
                                   <td>Distributed</td>
                                   <td>50</td>
                                   <td>~18</td>
                                   <td>47.9x</td>
                               </tr>
                           </tbody>
                       </table>
                   </div>
               </section>
               
               <!-- Examples Section -->
               <section id="examples" class="section">
                   <h2>Practical Examples: Real GATK Applications</h2>
                   
                   <p>Let's explore two concrete examples that demonstrate GATK's capabilities: a depth of coverage calculator and a Bayesian SNP genotyper.</p>
                   
                   <h3>Example 1: Depth of Coverage Walker</h3>
                   <div class="highlight-box">
                       <p><strong>Purpose:</strong> Calculate sequencing depth at each genomic position, essential for quality control and downstream analysis.</p>
                       <p><strong>Implementation:</strong> Only 83 lines of Java code extending the locus walker template.</p>
                   </div>
                   
                   <div class="code-block">
                       <pre>// Simplified Depth of Coverage Walker
public class DepthOfCoverageWalker extends LocusWalker {
    
    @Override
    public void map(RefMetaDataTracker tracker, ReferenceContext ref, 
                   AlignmentContext context) {
        
        // Get all reads covering this position
        ReadBackedPileup pileup = context.getBasePileup();
        
        // Count reads (optionally filter by quality)
        int depth = 0;
        for (PileupElement element : pileup) {
            if (element.getMappingQual() >= minMappingQuality) {
                depth++;
            }
        }
        
        // Emit coverage for this position
        emit(new CoverageResult(ref.getLocus(), depth));
    }
}</pre>
                   </div>
                   
                   <div class="svg-container">
                       <h3>MHC Region Coverage Analysis</h3>
                       <svg width="800" height="300" viewBox="0 0 800 300">
                           <!-- X-axis (genomic position) -->
                           <line x1="50" y1="250" x2="750" y2="250" stroke="#34495e" stroke-width="2"/>
                           <text x="400" y="270" text-anchor="middle" fill="#2c3e50" font-size="12">MHC Region (Chr6: 32.0-33.0 Mb)</text>
                           
                           <!-- Y-axis (coverage) -->
                           <line x1="50" y1="50" x2="50" y2="250" stroke="#34495e" stroke-width="2"/>
                           <text x="30" y="150" text-anchor="middle" fill="#2c3e50" font-size="12" transform="rotate(-90, 30, 150)">Coverage Depth</text>
                           
                           <!-- Coverage curve -->
                           <path d="M 50 200 Q 100 180 150 160 Q 200 140 250 120 Q 300 100 350 80 Q 400 60 450 40 Q 500 60 550 80 Q 600 100 650 120 Q 700 140 750 160" 
                                 stroke="#3498db" stroke-width="3" fill="none"/>
                           
                           <!-- HLA gene regions -->
                           <rect x="200" y="260" width="80" height="15" fill="#e74c3c" opacity="0.7"/>
                           <text x="240" y="275" text-anchor="middle" fill="#e74c3c" font-size="10" font-weight="bold">HLA-A</text>
                           
                           <rect x="400" y="260" width="80" height="15" fill="#e74c3c" opacity="0.7"/>
                           <text x="440" y="275" text-anchor="middle" fill="#e74c3c" font-size="10" font-weight="bold">HLA-B</text>
                           
                           <rect x="600" y="260" width="80" height="15" fill="#e74c3c" opacity="0.7"/>
                           <text x="640" y="275" text-anchor="middle" fill="#e74c3c" font-size="10" font-weight="bold">HLA-C</text>
                           
                           <!-- Coverage drops -->
                           <circle cx="240" cy="160" r="4" fill="#e74c3c"/>
                           <text x="240" y="145" text-anchor="middle" fill="#e74c3c" font-size="10">Drop</text>
                           
                           <circle cx="440" cy="80" r="4" fill="#e74c3c"/>
                           <text x="440" y="65" text-anchor="middle" fill="#e74c3c" font-size="10">Drop</text>
                           
                           <!-- Title -->
                           <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">MHC Region Coverage in JPT Samples (1000 Genomes)</text>
                           
                           <!-- Legend -->
                           <g transform="translate(50, 100)">
                               <line x1="0" y1="0" x2="30" y2="0" stroke="#3498db" stroke-width="3"/>
                               <text x="35" y="5" fill="#2c3e50" font-size="12">Coverage depth</text>
                               
                               <rect x="0" y="15" width="15" height="10" fill="#e74c3c" opacity="0.7"/>
                               <text x="20" y="23" fill="#2c3e50" font-size="12">HLA genes</text>
                           </g>
                       </svg>
                   </div>
                   
                   <h3>Example 2: Bayesian SNP Genotyper</h3>
                   <div class="key-concept">
                       <strong>Mathematical Foundation:</strong> Uses Bayesian inference to calculate the posterior probability of each possible genotype given the observed sequencing data and prior knowledge about population genetics.
                   </div>
                   
                   <div class="formula-box">
                       <h4>Bayesian Genotyping Formula</h4>
                       <p>The posterior probability of genotype G given data D:</p>
                       $$P(G|D) = \frac{P(D|G) \cdot P(G)}{P(D)}$$
                       
                       <p>Where:</p>
                       <ul style="text-align: left; display: inline-block;">
                           <li>$P(G|D)$ = Posterior probability of genotype</li>
                           <li>$P(D|G)$ = Likelihood of data given genotype</li>
                           <li>$P(G)$ = Prior probability of genotype</li>
                           <li>$P(D)$ = Marginal probability of data (constant)</li>
                       </ul>
                   </div>
                   
                   <div class="code-block">
                       <pre>// Simplified Bayesian Genotyper Walker
public class BayesianGenotyperWalker extends LocusWalker 
    implements TreeReducible&lt;GenotypeResult&gt; {
    
    @Override
    public void map(RefMetaDataTracker tracker, ReferenceContext ref, 
                   AlignmentContext context) {
        
        ReadBackedPileup pileup = context.getBasePileup();
        
        // Calculate likelihood for each of 10 possible genotypes
        double[] genotypeLikelihoods = new double[10];
        for (int i = 0; i < 10; i++) {
            genotypeLikelihoods[i] = calculateLikelihood(pileup, i);
        }
        
        // Apply prior probabilities
        double[] posteriors = applyPriors(genotypeLikelihoods);
        
        // Find most likely genotype
        int bestGenotype = findMaxIndex(posteriors);
        
        // Emit if above threshold
        if (posteriors[bestGenotype] > threshold) {
            emit(new GenotypeResult(ref.getLocus(), bestGenotype, posteriors));
        }
    }
    
    @Override
    public GenotypeResult reduce(GenotypeResult result1, GenotypeResult result2) {
        // Merge results from parallel processing
        return mergeResults(result1, result2);
    }
}</pre>
                   </div>
                   
                   <div class="svg-container">
                       <h3>Genotyping Performance Results</h3>
                       <svg width="800" height="350" viewBox="0 0 800 350">
                           <!-- Performance bars -->
                           <g>
                               <!-- Single CPU -->
                               <rect x="100" y="200" width="60" height="100" fill="#e74c3c"/>
                               <text x="130" y="195" text-anchor="middle" fill="#2c3e50" font-size="12">1 CPU</text>
                               <text x="130" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">863 min</text>
                               
                               <!-- 4 CPUs -->
                               <rect x="200" y="225" width="60" height="75" fill="#f39c12"/>
                               <text x="230" y="220" text-anchor="middle" fill="#2c3e50" font-size="12">4 CPUs</text>
                               <text x="230" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">216 min</text>
                               
                               <!-- 8 CPUs -->
                               <rect x="300" y="242" width="60" height="58" fill="#f1c40f"/>
                               <text x="330" y="237" text-anchor="middle" fill="#2c3e50" font-size="12">8 CPUs</text>
                               <text x="330" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">108 min</text>
                               
                               <!-- 12 CPUs -->
                               <rect x="400" y="250" width="60" height="50" fill="#2ecc71"/>
                               <text x="430" y="245" text-anchor="middle" fill="#2c3e50" font-size="12">12 CPUs</text>
                               <text x="430" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">72 min</text>
                               
                               <!-- 25 CPUs (distributed) -->
                               <rect x="500" y="260" width="60" height="40" fill="#3498db"/>
                               <text x="530" y="255" text-anchor="middle" fill="#2c3e50" font-size="12">25 CPUs</text>
                               <text x="530" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">35 min</text>
                               
                               <!-- 50 CPUs (distributed) -->
                               <rect x="600" y="265" width="60" height="35" fill="#9b59b6"/>
                               <text x="630" y="260" text-anchor="middle" fill="#2c3e50" font-size="12">50 CPUs</text>
                               <text x="630" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">18 min</text>
                           </g>
                           
                           <!-- Y-axis -->
                           <line x1="80" y1="100" x2="80" y2="300" stroke="#34495e" stroke-width="2"/>
                           <text x="60" y="200" text-anchor="middle" fill="#2c3e50" font-size="12" transform="rotate(-90, 60, 200)">Time (minutes)</text>
                           
                           <!-- X-axis -->
                           <line x1="80" y1="300" x2="680" y2="300" stroke="#34495e" stroke-width="2"/>
                           <text x="380" y="320" text-anchor="middle" fill="#2c3e50" font-size="12">Number of Processors</text>
                           
                           <!-- Title -->
                           <text x="380" y="50" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Genotyping Performance Scaling</text>
                           <text x="380" y="70" text-anchor="middle" fill="#7f8c8d" font-size="12">NA12878 Chromosome 1 (247M loci)</text>
                           
                           <!-- Accuracy metrics -->
                           <g transform="translate(50, 30)">
                               <text x="0" y="0" fill="#2c3e50" font-size="12" font-weight="bold">Accuracy Results:</text>
                               <text x="0" y="15" fill="#27ae60" font-size="11">• 315,202 variants called</text>
                               <text x="0" y="28" fill="#27ae60" font-size="11">• 81.70% in dbSNP</text>
                               <text x="0" y="41" fill="#27ae60" font-size="11">• 99.76% overall concordance</text>
                               <text x="0" y="54" fill="#27ae60" font-size="11">• 99.84% HapMap concordance</text>
                           </g>
                       </svg>
                   </div>
                   
                   <h3>Real-World Applications</h3>
                   <div class="feature-grid">
                       <div class="feature-card">
                           <h4>🏥 1000 Genomes Project</h4>
                           <p>Used for variant discovery and genotyping across diverse human populations</p>
                       </div>
                       <div class="feature-card">
                           <h4>🔬 Cancer Genome Atlas</h4>
                           <p>Applied to tumor-normal pair analysis for somatic variant detection</p>
                       </div>
                       <div class="feature-card">
                           <h4>🧬 Production Sequencing</h4>
                           <p>Processes hundreds of lanes weekly at Broad Institute facilities</p>
                       </div>
                       <div class="feature-card">
                           <h4>📊 Quality Control</h4>
                           <p>Routine coverage analysis and quality assessment for sequencing projects</p>
                       </div>
                   </div>
               </section>
               
               <!-- Mathematics Section -->
               <section id="mathematics" class="section">
                   <h2>Mathematical Foundations</h2>
                   
                   <p>GATK's Bayesian genotyping approach is grounded in solid statistical principles. Understanding the mathematics helps appreciate the framework's robustness and accuracy.</p>
                   
                   <h3>Bayesian Genotyping Mathematics</h3>
                   
                   <div class="formula-box">
                       <h4>Core Bayesian Formula</h4>
                       <p>The fundamental equation for calculating genotype probabilities:</p>
                       $$P(G|D) = \frac{P(D|G) \cdot P(G)}{P(D)}$$
                       
                       <p>Since $P(D)$ is constant across all genotypes, we can focus on the numerator:</p>
                       $$P(G|D) \propto P(D|G) \cdot P(G)$$
                   </div>
                   
                   <h3>Likelihood Calculation</h3>
                   <p>The likelihood $P(D|G)$ represents the probability of observing the sequencing data given a specific genotype:</p>
                   
                   <div class="formula-box">
                       <h4>Data Likelihood</h4>
                       <p>For a genotype $G = \{A_1, A_2\}$ with alleles $A_1$ and $A_2$:</p>
                       $$P(D|G) = \prod_{b \in \text{reads}} P(b|G)$$
                       
                       <p>Where each base $b$ contributes:</p>
                       $$P(b|G) = \frac{1}{2} \left[ P(b|A_1) + P(b|A_2) \right]$$
                       
                       <p>And the probability of observing base $b$ given allele $A$:</p>
                       $$P(b|A) = \begin{cases} 
                       1 - \epsilon & \text{if } b = A \\
                       \frac{\epsilon}{3} & \text{if } b \neq A
                       \end{cases}$$
                       
                       <p>Where $\epsilon$ is the sequencing error rate (derived from Phred quality scores).</p>
                   </div>
                   
                   <div class="svg-container">
                       <h3>Genotype Likelihood Calculation</h3>
                       <svg width="800" height="400" viewBox="0 0 800 400">
                           <!-- Genotype possibilities -->
                           <g>
                               <text x="400" y="30" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Diploid Genotype Likelihoods</text>
                               
                               <!-- Reference allele A -->
                               <rect x="50" y="60" width="80" height="40" fill="#3498db" rx="5"/>
                               <text x="90" y="80" text-anchor="middle" fill="white" font-size="12" font-weight="bold">A</text>
                               <text x="90" y="95" text-anchor="middle" fill="white" font-size="10">Reference</text>
                               
                               <!-- Alternative allele T -->
                               <rect x="150" y="60" width="80" height="40" fill="#e74c3c" rx="5"/>
                               <text x="190" y="80" text-anchor="middle" fill="white" font-size="12" font-weight="bold">T</text>
                               <text x="190" y="95" text-anchor="middle" fill="white" font-size="10">Alternative</text>
                               
                               <!-- Genotype combinations -->
                               <rect x="50" y="140" width="120" height="30" fill="#2ecc71" rx="5"/>
                               <text x="110" y="155" text-anchor="middle" fill="white" font-size="12" font-weight="bold">AA (Homozygous Ref)</text>
                               
                               <rect x="200" y="140" width="120" height="30" fill="#f39c12" rx="5"/>
                               <text x="260" y="155" text-anchor="middle" fill="white" font-size="12" font-weight="bold">AT (Heterozygous)</text>
                               
                               <rect x="350" y="140" width="120" height="30" fill="#9b59b6" rx="5"/>
                               <text x="410" y="155" text-anchor="middle" fill="white" font-size="12" font-weight="bold">TT (Homozygous Alt)</text>
                           </g>
                           
                           <!-- Read pileup -->
                           <g>
                               <text x="400" y="200" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Observed Read Pileup</text>
                               
                               <!-- Reads -->
                               <rect x="100" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="130" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                               
                               <rect x="180" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="210" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                               
                               <rect x="260" y="220" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                               <text x="290" y="232" text-anchor="middle" fill="white" font-size="10">T</text>
                               
                               <rect x="340" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="370" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                               
                               <rect x="420" y="220" width="60" height="15" fill="#e74c3c" opacity="0.8"/>
                               <text x="450" y="232" text-anchor="middle" fill="white" font-size="10">T</text>
                               
                               <rect x="500" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="530" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                               
                               <rect x="580" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="610" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                               
                               <rect x="660" y="220" width="60" height="15" fill="#3498db" opacity="0.8"/>
                               <text x="690" y="232" text-anchor="middle" fill="white" font-size="10">A</text>
                           </g>
                           
                           <!-- Likelihood calculations -->
                           <g>
                               <text x="400" y="270" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Likelihood Calculations</text>
                               
                               <!-- AA likelihood -->
                               <rect x="50" y="290" width="200" height="60" fill="#2ecc71" opacity="0.3" rx="5"/>
                               <text x="150" y="305" text-anchor="middle" fill="#2c3e50" font-size="11" font-weight="bold">P(D|AA)</text>
                               <text x="150" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">Most reads match A</text>
                               <text x="150" y="335" text-anchor="middle" fill="#2c3e50" font-size="10">High likelihood</text>
                               
                               <!-- AT likelihood -->
                               <rect x="300" y="290" width="200" height="60" fill="#f39c12" opacity="0.3" rx="5"/>
                               <text x="400" y="305" text-anchor="middle" fill="#2c3e50" font-size="11" font-weight="bold">P(D|AT)</text>
                               <text x="400" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">Mixed A/T reads</text>
                               <text x="400" y="335" text-anchor="middle" fill="#2c3e50" font-size="10">Medium likelihood</text>
                               
                               <!-- TT likelihood -->
                               <rect x="550" y="290" width="200" height="60" fill="#9b59b6" opacity="0.3" rx="5"/>
                               <text x="650" y="305" text-anchor="middle" fill="#2c3e50" font-size="11" font-weight="bold">P(D|TT)</text>
                               <text x="650" y="320" text-anchor="middle" fill="#2c3e50" font-size="10">Few T reads</text>
                               <text x="650" y="335" text-anchor="middle" fill="#2c3e50" font-size="10">Low likelihood</text>
                           </g>
                           
                           <!-- Arrows showing calculation flow -->
                           <defs>
                               <marker id="mathArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                   <polygon points="0 0, 8 3, 0 6" fill="#34495e"/>
                               </marker>
                           </defs>
                           
                           <line x1="400" y1="180" x2="400" y2="220" stroke="#34495e" stroke-width="2" marker-end="url(#mathArrow)"/>
                           <line x1="400" y1="250" x2="400" y2="290" stroke="#34495e" stroke-width="2" marker-end="url(#mathArrow)"/>
                       </svg>
                   </div>
                   
                   <h3>Prior Probabilities</h3>
                   <p>Prior probabilities $P(G)$ incorporate population genetics knowledge:</p>
                   
                   <div class="formula-box">
                       <h4>Prior Probability Model</h4>
                       <p>For a population with heterozygosity $H$:</p>
                       $$P(AA) = (1-H)^2$$
                       $$P(AT) = 2H(1-H)$$
                       $$P(TT) = H^2$$
                       
                       <p>Where $H$ is typically around 0.001 for human populations.</p>
                   </div>
                   
                   <h3>Quality Score Integration</h3>
                   <p>GATK integrates Phred quality scores to model sequencing errors:</p>
                   
                   <div class="formula-box">
                       <h4>Error Rate Calculation</h4>
                       <p>Phred quality score $Q$ relates to error probability:</p>
                       $$Q = -10 \log_{10}(\epsilon)$$
                       $$\epsilon = 10^{-Q/10}$$
                       
                       <p>This error rate is used in the likelihood calculations to weight each base observation.</p>
                   </div>
                   
                   <div class="key-concept">
                       <strong>Key Insight:</strong> The Bayesian approach naturally handles uncertainty in sequencing data by incorporating quality scores and population genetics priors, resulting in more robust genotype calls than simple majority voting.
                   </div>
               </section>
               
               <!-- Conclusion -->
               <section class="section">
                   <h2>Conclusion</h2>
                   
                   <div class="highlight-box">
                       <p>The Genome Analysis Toolkit (GATK) represents a paradigm shift in next-generation sequencing analysis. By providing a structured, MapReduce-based framework, GATK enables researchers to focus on biological questions rather than computational infrastructure.</p>
                   </div>
                   
                   <h3>Key Achievements</h3>
                   <div class="feature-grid">
                       <div class="feature-card">
                           <h4>🚀 Scalability</h4>
                           <p>Processes terabases of data with near-linear parallelization scaling</p>
                       </div>
                       <div class="feature-card">
                           <h4>🔧 Simplicity</h4>
                           <p>Complex tools written in just dozens of lines of code</p>
                       </div>
                       <div class="feature-card">
                           <h4>📊 Accuracy</h4>
                           <p>99.76% concordance in real-world validation studies</p>
                       </div>
                       <div class="feature-card">
                           <h4>🌍 Impact</h4>
                           <p>Used in major projects like 1000 Genomes and Cancer Genome Atlas</p>
                       </div>
                   </div>
                   
                   <h3>Future Directions</h3>
                   <p>GATK continues to evolve with plans to support:</p>
                   <ul>
                       <li><strong>Local reference-guided assembly</strong></li>
                       <li><strong>Copy-number variation detection</strong></li>
                       <li><strong>Structural variation algorithms</strong></li>
                       <li><strong>Advanced error models</strong></li>
                   </ul>
                   
                   <div class="key-concept">
                       <strong>Final Note:</strong> GATK demonstrates how well-designed software architecture can democratize access to complex genomic analysis, enabling researchers worldwide to contribute to our understanding of genetic variation and disease.
                   </div>
               </section>
           </main>
       </div>
   </body>
   </html>
             <section id="architecture" class="section">
                 <h2>GATK Architecture Fundamentals</h2>
                 
                 <p>GATK is built on the <strong>MapReduce paradigm</strong>, a functional programming approach that enables automatic parallelization and distribution of computational tasks. This design philosophy, popularized by companies like Google and Yahoo!, provides a contract between the framework and developers that makes scaling computations much simpler.</p>
                 
                 <div class="svg-container">
                     <h3>MapReduce Data Flow in GATK</h3>
                     <svg width="800" height="400" viewBox="0 0 800 400">
                         <!-- Background -->
                         <defs>
                             <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                 <stop offset="0%" style="stop-color:#f8f9fa"/>
                                 <stop offset="100%" style="stop-color:#e9ecef"/>
                             </linearGradient>
                             <linearGradient id="inputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                 <stop offset="0%" style="stop-color:#3498db"/>
                                 <stop offset="100%" style="stop-color:#2980b9"/>
                             </linearGradient>
                             <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                 <stop offset="0%" style="stop-color:#2ecc71"/>
                                 <stop offset="100%" style="stop-color:#27ae60"/>
                             </linearGradient>
                             <linearGradient id="outputGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                                 <stop offset="0%" style="stop-color:#e74c3c"/>
                                 <stop offset="100%" style="stop-color:#c0392b"/>
                             </linearGradient>
                         </defs>
                         
                         <!-- Input Data -->
                         <rect x="50" y="80" width="120" height="60" fill="url(#inputGrad)" rx="10"/>
                         <text x="110" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">BAM/SAM</text>
                         <text x="110" y="120" text-anchor="middle" fill="white" font-size="12">Input Data</text>
                         
                         <rect x="50" y="160" width="120" height="60" fill="url(#inputGrad)" rx="10"/>
                         <text x="110" y="185" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Reference</text>
                         <text x="110" y="200" text-anchor="middle" fill="white" font-size="12">Genome</text>
                         
                         <rect x="50" y="240" width="120" height="60" fill="url(#inputGrad)" rx="10"/>
                         <text x="110" y="265" text-anchor="middle" fill="white" font-size="12" font-weight="bold">dbSNP/</text>
                         <text x="110" y="280" text-anchor="middle" fill="white" font-size="12">Annotations</text>
                         
                         <!-- Sharding -->
                         <rect x="220" y="140" width="100" height="80" fill="url(#processGrad)" rx="10"/>
                         <text x="270" y="165" text-anchor="middle" fill="white" font-size="12" font-weight="bold">SHARDING</text>
                         <text x="270" y="180" text-anchor="middle" fill="white" font-size="10">Divide data into</text>
                         <text x="270" y="195" text-anchor="middle" fill="white" font-size="10">manageable</text>
                         <text x="270" y="210" text-anchor="middle" fill="white" font-size="10">chunks</text>
                         
                         <!-- Traversal Engine -->
                         <rect x="370" y="100" width="120" height="40" fill="url(#processGrad)" rx="8"/>
                         <text x="430" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">TRAVERSAL</text>
                         
                         <rect x="370" y="160" width="120" height="40" fill="url(#processGrad)" rx="8"/>
                         <text x="430" y="185" text-anchor="middle" fill="white" font-size="12" font-weight="bold">ENGINE</text>
                         
                         <rect x="370" y="220" width="120" height="40" fill="url(#processGrad)" rx="8"/>
                         <text x="430" y="245" text-anchor="middle" fill="white" font-size="12" font-weight="bold">MAP/REDUCE</text>
                         
                         <!-- Walker -->
                         <rect x="540" y="140" width="100" height="80" fill="url(#outputGrad)" rx="10"/>
                         <text x="590" y="165" text-anchor="middle" fill="white" font-size="12" font-weight="bold">WALKER</text>
                         <text x="590" y="180" text-anchor="middle" fill="white" font-size="10">Analysis</text>
                         <text x="590" y="195" text-anchor="middle" fill="white" font-size="10">Algorithm</text>
                         <text x="590" y="210" text-anchor="middle" fill="white" font-size="10">(User Code)</text>
                         
                         <!-- Output -->
                         <rect x="690" y="140" width="80" height="80" fill="url(#outputGrad)" rx="10"/>
                         <text x="730" y="170" text-anchor="middle" fill="white" font-size="12" font-weight="bold">OUTPUT</text>
                         <text x="730" y="190" text-anchor="middle" fill="white" font-size="10">VCF, Stats,</text>
                         <text x="730" y="205" text-anchor="middle" fill="white" font-size="10">Coverage, etc.</text>
                         
                         <!-- Arrows -->
                         <defs>
                             <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                 <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                             </marker>
                         </defs>
                         
                         <!-- Input to Sharding arrows -->
                         <line x1="170" y1="110" x2="220" y2="160" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="170" y1="190" x2="220" y2="180" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         <line x1="170" y1="270" x2="220" y2="200" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                         
                         <!-- Sharding to Traversal -->
                         <line x1="320" y1="180" x2="370" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                         
                         <!-- Traversal to Walker -->
                         <line x1="490" y1="180" x2="540" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                         
                         <!-- Walker to Output -->
                         <line x1="640" y1="180" x2="690" y2="180" stroke="#34495e" stroke-width="3" marker-end="url(#arrowhead)"/>
                         
                         <!-- Labels -->
                         <text x="400" y="40" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">GATK MapReduce Architecture</text>
                         <text x="245" y="330" text-anchor="middle" fill="#7f8c8d" font-size="11">Data Preparation</text>
                         <text x="430" y="330" text-anchor="middle" fill="#7f8c8d" font-size="11">Framework Engine</text>
                         <text x="615" y="330" text-anchor="middle" fill="#7f8c8d" font-size="11">User Analysis</text>
                     </svg>
                 </div>
                 
                 <div class="key-concept">
                     <strong>Key Insight:</strong> The MapReduce paradigm divides computations into two steps - Map (subdivide problems into independent pieces) and Reduce (join results back together). This natural fit for genomic analysis enables automatic parallelization.
                 </div>
                 
                 <h3>Core Components</h3>
                 <div class="feature-grid">
                     <div class="feature-card">
                         <h4>🗂️ Traversals</h4>
                         <p>Provide data division and preparation. They determine how data flows through the system and what information is presented to analysis modules.</p>
                     </div>
                     <div class="feature-card">
                         <h4>🚶 Walkers</h4>
                         <p>Analysis modules that contain the actual computational logic. They implement map and reduce methods to process the data bundles provided by traversals.</p>
                     </div>
                     <div class="feature-card">
                         <h4>🔧 Engine</h4>
                         <p>The core GATK infrastructure that manages data access, parallelization, and optimization. Handles all the complex data management tasks.</p>
                     </div>
                     <div class="feature-card">
                         <h4>⚙️ Sharding</h4>
                         <p>Intelligent division of genomic data into manageable multikilobase-sized pieces for efficient processing and memory management.</p>
                     </div>
                 </div>
             </section> 