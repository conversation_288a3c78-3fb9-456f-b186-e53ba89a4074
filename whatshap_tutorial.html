<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsHap: Mathematics and Algorithms Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .algorithm-box {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #0d47a1;
            margin-bottom: 15px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .read-fragment {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .snp-position {
            fill: #e74c3c;
            stroke: #c0392b;
            stroke-width: 1;
        }
        
        .haplotype-line {
            stroke: #27ae60;
            stroke-width: 3;
            fill: none;
        }
        
        .bipartition-line {
            stroke: #f39c12;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WhatsHap: Mathematics and Algorithms</h1>
            <p>A Comprehensive Tutorial on Read-Based Phasing with Pedigree Information</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction and Problem Formulation</a></li>
                <li><a href="#mec-problem">2. The MEC Problem and Matrix Representation</a></li>
                <li><a href="#pedmec">3. PedMEC: Extending to Pedigrees</a></li>
                <li><a href="#algorithm">4. Dynamic Programming Algorithm</a></li>
                <li><a href="#complexity">5. Computational Complexity and Runtime</a></li>
                <li><a href="#implementation">6. Implementation Details</a></li>
                <li><a href="#performance">7. Performance Analysis</a></li>
                <li><a href="#applications">8. Applications and Results</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction and Problem Formulation -->
        <div id="introduction" class="section">
            <h2>1. Introduction and Problem Formulation</h2>

            <div class="highlight">
                <strong>Core Innovation:</strong> WhatsHap combines read-based phasing with genetic haplotyping, leveraging both sequencing reads and pedigree information to achieve superior phasing accuracy, especially at low coverage.
            </div>

            <h3>The Haplotype Assembly Challenge</h3>

            <div class="math-box">
                <h4>Problem Definition</h4>
                <p><strong>Input:</strong> Sequencing reads covering multiple heterozygous variants</p>
                <p><strong>Goal:</strong> Determine which alleles are co-located on the same chromosome (haplotype)</p>

                <p><strong>Key Challenge:</strong> Sequencing errors create conflicts between reads that should belong to the same haplotype</p>

                <p><strong>Matrix Representation:</strong> SNP matrix $M \in \{0, 1, -\}^{R \times M}$ where:</p>
                <ul>
                    <li>R = number of reads</li>
                    <li>M = number of variants</li>
                    <li>0 = reference allele</li>
                    <li>1 = alternative allele</li>
                    <li>- = position not covered by read</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        WhatsHap: Integrating Read-Based and Genetic Phasing
                    </text>

                    <!-- Traditional single-individual approach -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Traditional Single-Individual Phasing
                        </text>

                        <!-- Individual genome -->
                        <g transform="translate(50, 40)">
                            <rect width="200" height="80" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" fill="#2c3e50">Individual</text>

                            <!-- Chromosome pair -->
                            <line x1="20" y1="40" x2="180" y2="40" stroke="#34495e" stroke-width="4"/>
                            <line x1="20" y1="55" x2="180" y2="55" stroke="#34495e" stroke-width="4"/>

                            <!-- SNP positions -->
                            <circle cx="40" cy="40" r="3" fill="#e74c3c"/>
                            <circle cx="70" cy="40" r="3" fill="#e74c3c"/>
                            <circle cx="100" cy="40" r="3" fill="#e74c3c"/>
                            <circle cx="130" cy="40" r="3" fill="#e74c3c"/>
                            <circle cx="160" cy="40" r="3" fill="#e74c3c"/>

                            <circle cx="40" cy="55" r="3" fill="#3498db"/>
                            <circle cx="70" cy="55" r="3" fill="#3498db"/>
                            <circle cx="100" cy="55" r="3" fill="#3498db"/>
                            <circle cx="130" cy="55" r="3" fill="#3498db"/>
                            <circle cx="160" cy="55" r="3" fill="#3498db"/>
                        </g>

                        <text x="150" y="140" text-anchor="middle" font-size="11" fill="#666">
                            Limited by read coverage and length
                        </text>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(350, 180)">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">WhatsHap</text>
                    </g>

                    <!-- WhatsHap trio approach -->
                    <g transform="translate(500, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            WhatsHap Trio Phasing
                        </text>

                        <!-- Family trio -->
                        <g transform="translate(20, 40)">
                            <!-- Mother -->
                            <rect width="80" height="60" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="5"/>
                            <text x="40" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">Mother</text>
                            <line x1="10" y1="25" x2="70" y2="25" stroke="#34495e" stroke-width="2"/>
                            <line x1="10" y1="35" x2="70" y2="35" stroke="#34495e" stroke-width="2"/>
                            <circle cx="20" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="35" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="50" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="20" cy="35" r="2" fill="#3498db"/>
                            <circle cx="35" cy="35" r="2" fill="#3498db"/>
                            <circle cx="50" cy="35" r="2" fill="#3498db"/>

                            <!-- Father -->
                            <rect x="100" y="0" width="80" height="60" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="5"/>
                            <text x="140" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">Father</text>
                            <line x1="110" y1="25" x2="170" y2="25" stroke="#34495e" stroke-width="2"/>
                            <line x1="110" y1="35" x2="170" y2="35" stroke="#34495e" stroke-width="2"/>
                            <circle cx="120" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="135" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="150" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="120" cy="35" r="2" fill="#3498db"/>
                            <circle cx="135" cy="35" r="2" fill="#3498db"/>
                            <circle cx="150" cy="35" r="2" fill="#3498db"/>

                            <!-- Child -->
                            <rect x="200" y="0" width="80" height="60" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="5"/>
                            <text x="240" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">Child</text>
                            <line x1="210" y1="25" x2="270" y2="25" stroke="#34495e" stroke-width="2"/>
                            <line x1="210" y1="35" x2="270" y2="35" stroke="#34495e" stroke-width="2"/>
                            <circle cx="220" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="235" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="250" cy="25" r="2" fill="#e74c3c"/>
                            <circle cx="220" cy="35" r="2" fill="#3498db"/>
                            <circle cx="235" cy="35" r="2" fill="#3498db"/>
                            <circle cx="250" cy="35" r="2" fill="#3498db"/>

                            <!-- Inheritance arrows -->
                            <path d="M 40 45 Q 140 70 220 45" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                            <path d="M 140 45 Q 180 70 250 45" stroke="#27ae60" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        </g>

                        <text x="150" y="140" text-anchor="middle" font-size="11" fill="#666">
                            Leverages Mendelian inheritance constraints
                        </text>
                    </g>

                    <!-- Key advantages -->
                    <g transform="translate(100, 280)">
                        <text x="300" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            WhatsHap Key Advantages
                        </text>

                        <!-- Advantage 1 -->
                        <g transform="translate(0, 40)">
                            <rect width="180" height="100" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Low Coverage</text>
                            <text x="90" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">2× coverage per individual</text>
                            <text x="90" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">achieves accuracy of</text>
                            <text x="90" y="75" text-anchor="middle" font-size="10" fill="#2c3e50">15× single individual</text>
                        </g>

                        <!-- Advantage 2 -->
                        <g transform="translate(210, 40)">
                            <rect width="180" height="100" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Block Connection</text>
                            <text x="90" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Phases variants between</text>
                            <text x="90" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">unconnected blocks using</text>
                            <text x="90" y="75" text-anchor="middle" font-size="10" fill="#2c3e50">genetic information</text>
                        </g>

                        <!-- Advantage 3 -->
                        <g transform="translate(420, 40)">
                            <rect width="180" height="100" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                            <text x="90" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Optimal Solution</text>
                            <text x="90" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">Fixed-parameter tractable</text>
                            <text x="90" y="60" text-anchor="middle" font-size="10" fill="#2c3e50">algorithm guarantees</text>
                            <text x="90" y="75" text-anchor="middle" font-size="10" fill="#2c3e50">optimal phasing</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Three Approaches to Haplotyping</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Approach</th>
                        <th>Data Required</th>
                        <th>Strengths</th>
                        <th>Limitations</th>
                        <th>WhatsHap Integration</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Population-Based</strong></td>
                        <td>Genotypes from large cohorts</td>
                        <td>Works for unrelated individuals</td>
                        <td>Poor for rare variants</td>
                        <td>Not directly used</td>
                    </tr>
                    <tr>
                        <td><strong>Genetic</strong></td>
                        <td>Family pedigree genotypes</td>
                        <td>Chromosome-length haplotypes</td>
                        <td>Cannot phase all-heterozygous sites</td>
                        <td>✓ Core component</td>
                    </tr>
                    <tr>
                        <td><strong>Read-Based</strong></td>
                        <td>Sequencing reads</td>
                        <td>High confidence, works for rare variants</td>
                        <td>Limited by read length and coverage</td>
                        <td>✓ Core component</td>
                    </tr>
                </tbody>
            </table>

            <h3>WhatsHap's Hybrid Strategy</h3>

            <div class="info-box">
                <h4>Unified Framework Benefits</h4>
                <ul class="step-list">
                    <li><strong>Complementary Information:</strong> Read-based phasing handles all-heterozygous sites that genetic phasing cannot resolve</li>
                    <li><strong>Enhanced Connectivity:</strong> Genetic relationships connect haplotype blocks that are not linked by reads</li>
                    <li><strong>Error Correction:</strong> Family constraints help identify and correct sequencing errors</li>
                    <li><strong>Coverage Efficiency:</strong> Achieves high accuracy with much lower sequencing coverage</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Key Assumptions</h4>
                <ul>
                    <li><strong>Bi-allelic variants:</strong> All variants have exactly two possible alleles</li>
                    <li><strong>Non-overlapping variants:</strong> No complex structural variations</li>
                    <li><strong>Known genotypes:</strong> Genotype calling is performed separately</li>
                    <li><strong>Mendelian inheritance:</strong> No de novo mutations (handled in extensions)</li>
                </ul>
            </div>
        </div>

        <!-- Section 2: The MEC Problem and Matrix Representation -->
        <div id="mec-problem" class="section">
            <h2>2. The MEC Problem and Matrix Representation</h2>

            <div class="highlight">
                <strong>Mathematical Foundation:</strong> The Minimum Error Correction (MEC) problem formalizes haplotype assembly as finding the minimum number of sequencing errors to correct to make the read matrix feasible.
            </div>

            <h3>Formal Problem Definition</h3>

            <div class="math-box">
                <h4>Distance and Feasibility</h4>

                <p><strong>Definition 1 (Distance):</strong> Given two vectors $r_1, r_2 \in \{0, 1, -\}^M$, the distance $d(r_1, r_2)$ is:</p>
                $$d(r_1, r_2) = \sum_{k=1}^{M} \mathbb{I}[r_1[k] \neq r_2[k] \text{ and } r_1[k], r_2[k] \in \{0,1\}]$$

                <p><strong>Definition 2 (Feasibility):</strong> A SNP matrix $M \in \{0, 1, -\}^{R \times M}$ is feasible if there exists a bipartition of rows into two sets such that all pairwise distances within each set are zero.</p>

                <p><strong>Equivalently:</strong> Matrix $M$ is feasible if there exist haplotypes $h_0, h_1 \in \{0,1\}^M$ such that every read $r$ has distance zero to either $h_0$ or $h_1$.</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        MEC Problem: From Conflicting Reads to Feasible Matrix
                    </text>

                    <!-- Original conflicting matrix -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Original Matrix (with conflicts)
                        </text>

                        <!-- Matrix representation -->
                        <g transform="translate(50, 40)">
                            <!-- Header row -->
                            <text x="30" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP1</text>
                            <text x="60" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP2</text>
                            <text x="90" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP3</text>
                            <text x="120" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP4</text>
                            <text x="150" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP5</text>

                            <!-- Read 1 -->
                            <text x="10" y="35" font-size="10" fill="#2c3e50">R1</text>
                            <rect x="20" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="30" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="50" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="80" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="90" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="110" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="120" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="140" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="150" y="35" text-anchor="middle" font-size="10" fill="white">-</text>

                            <!-- Read 2 -->
                            <text x="10" y="55" font-size="10" fill="#2c3e50">R2</text>
                            <rect x="20" y="45" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="30" y="55" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="50" y="45" width="20" height="15" fill="#e74c3c" stroke="#c0392b"/>
                            <text x="60" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="80" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="90" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="110" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="120" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="140" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="55" text-anchor="middle" font-size="10" fill="white">1</text>

                            <!-- Read 3 -->
                            <text x="10" y="75" font-size="10" fill="#2c3e50">R3</text>
                            <rect x="20" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="30" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="50" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="60" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="80" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="90" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="110" y="65" width="20" height="15" fill="#e74c3c" stroke="#c0392b"/>
                            <text x="120" y="75" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="140" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="75" text-anchor="middle" font-size="10" fill="white">1</text>

                            <!-- Read 4 -->
                            <text x="10" y="95" font-size="10" fill="#2c3e50">R4</text>
                            <rect x="20" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="30" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="50" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="60" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="80" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="90" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="110" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="120" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="140" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                        </g>

                        <!-- Conflict indicators -->
                        <g transform="translate(50, 150)">
                            <text x="75" y="15" text-anchor="middle" font-size="12" fill="#e74c3c">Conflict at SNP2!</text>
                            <text x="75" y="30" text-anchor="middle" font-size="10" fill="#666">R1: 1, R2: 0</text>
                        </g>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(350, 200)">
                        <defs>
                            <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead2)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">MEC</text>
                    </g>

                    <!-- Corrected feasible matrix -->
                    <g transform="translate(500, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            Corrected Matrix (feasible)
                        </text>

                        <!-- Matrix representation -->
                        <g transform="translate(50, 40)">
                            <!-- Header row -->
                            <text x="30" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP1</text>
                            <text x="60" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP2</text>
                            <text x="90" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP3</text>
                            <text x="120" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP4</text>
                            <text x="150" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">SNP5</text>

                            <!-- Read 1 (Haplotype 0) -->
                            <text x="10" y="35" font-size="10" fill="#3498db">R1</text>
                            <rect x="20" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="30" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="50" y="25" width="20" height="15" fill="#27ae60" stroke="#229954"/>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="80" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="90" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="110" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="120" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="140" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="150" y="35" text-anchor="middle" font-size="10" fill="white">-</text>

                            <!-- Read 2 (Haplotype 1) -->
                            <text x="10" y="55" font-size="10" fill="#e74c3c">R2</text>
                            <rect x="20" y="45" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="30" y="55" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="50" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="60" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="80" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="90" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="110" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="120" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="140" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="55" text-anchor="middle" font-size="10" fill="white">1</text>

                            <!-- Read 3 (Haplotype 0) -->
                            <text x="10" y="75" font-size="10" fill="#3498db">R3</text>
                            <rect x="20" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="30" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="50" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="60" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="80" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="90" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="110" y="65" width="20" height="15" fill="#27ae60" stroke="#229954"/>
                            <text x="120" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="140" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="75" text-anchor="middle" font-size="10" fill="white">1</text>

                            <!-- Read 4 (Haplotype 1) -->
                            <text x="10" y="95" font-size="10" fill="#e74c3c">R4</text>
                            <rect x="20" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="30" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="50" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="60" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="80" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="90" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="110" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="120" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="140" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="150" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                        </g>

                        <!-- Bipartition -->
                        <g transform="translate(50, 150)">
                            <text x="75" y="15" text-anchor="middle" font-size="12" fill="#27ae60">Bipartition Found!</text>
                            <text x="75" y="30" text-anchor="middle" font-size="10" fill="#3498db">Hap 0: R1, R3</text>
                            <text x="75" y="45" text-anchor="middle" font-size="10" fill="#e74c3c">Hap 1: R2, R4</text>
                        </g>
                    </g>

                    <!-- Cost indication -->
                    <g transform="translate(300, 320)">
                        <rect width="200" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#856404">
                            MEC Cost = 2
                        </text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="#856404">
                            2 corrections needed
                        </text>
                    </g>
                </svg>
            </div>

            <h3>MEC Problem Formulation</h3>

            <div class="math-box">
                <h4>Problem 1 (MEC)</h4>
                <p><strong>Input:</strong> SNP matrix $M \in \{0, 1, -\}^{R \times M}$</p>
                <p><strong>Goal:</strong> Flip minimum number of entries in $M$ to obtain a feasible matrix</p>

                <p><strong>Complexity:</strong> NP-hard (Cilibrasi et al., 2007)</p>

                <p><strong>Weighted Version (wMEC):</strong></p>
                <p><strong>Input:</strong> SNP matrix $M$ and weight matrix $W$ where $W_{i,j}$ is the cost of flipping $M_{i,j}$</p>
                <p><strong>Goal:</strong> Minimize total cost of flipped entries</p>

                $$\text{minimize} \sum_{(i,j) \in F} W_{i,j}$$

                <p>where $F$ is the set of flipped entries</p>
            </div>

            <h3>Phred Quality Scores</h3>

            <div class="info-box">
                <h4>Quality-Weighted Error Correction</h4>
                <p><strong>Phred Score:</strong> Each base call has quality score $Q$ corresponding to error probability $p = 10^{-Q/10}$</p>

                <p><strong>Cost Assignment:</strong> Weight matrix entries use phred scores as costs</p>
                <ul>
                    <li><strong>High quality (Q=30):</strong> $p = 0.001$, high cost to flip</li>
                    <li><strong>Low quality (Q=10):</strong> $p = 0.1$, low cost to flip</li>
                </ul>

                <p><strong>Statistical Interpretation:</strong> Minimizing weighted cost finds maximum likelihood phasing under error model</p>
            </div>
        </div>

        <!-- Section 3: PedMEC - Extending to Pedigrees -->
        <div id="pedmec" class="section">
            <h2>3. PedMEC: Extending to Pedigrees</h2>

            <div class="highlight">
                <strong>Key Innovation:</strong> PedMEC generalizes the MEC problem to multiple related individuals, incorporating Mendelian inheritance constraints and recombination costs.
            </div>

            <h3>Pedigree Notation and Constraints</h3>

            <div class="math-box">
                <h4>Mathematical Framework</h4>
                <p><strong>Individuals:</strong> Set $\mathcal{I} = \{1, 2, \ldots, N\}$</p>
                <p><strong>Relationships:</strong> Set of mother-father-child triples $\mathcal{T} \subseteq \mathcal{I}^3$</p>
                <p><strong>Example:</strong> $\mathcal{T} = \{(1, 2, 3)\}$ means individuals 1 and 2 are parents of individual 3</p>

                <p><strong>Per Individual:</strong></p>
                <ul>
                    <li>Genotype vector: $g^{(i)} \in \{0, 1, 2\}^M$ (0=hom ref, 1=het, 2=hom alt)</li>
                    <li>SNP matrix: $M^{(i)} \in \{0, 1, -\}^{R_i \times M}$</li>
                    <li>Weight matrix: $W^{(i)} \in \mathbb{R}_+^{R_i \times M}$</li>
                    <li>Sought haplotypes: $h_0^{(i)}, h_1^{(i)} \in \{0, 1\}^M$</li>
                </ul>
            </div>

            <h3>Transmission Vectors</h3>

            <div class="math-box">
                <h4>Modeling Inheritance</h4>
                <p><strong>Transmission Vector:</strong> For trio $(m, f, c) \in \mathcal{T}$:</p>
                $$t^{(m,f,c)} = (t_m, t_f) \in \{0,1\}^M \times \{0,1\}^M$$

                <p><strong>Interpretation:</strong></p>
                <ul>
                    <li>$t_m[k] = 0$: child inherits mother's first haplotype at position $k$</li>
                    <li>$t_m[k] = 1$: child inherits mother's second haplotype at position $k$</li>
                    <li>Similarly for $t_f[k]$ and father</li>
                </ul>

                <p><strong>Compatibility Constraint:</strong></p>
                $$h_0^{(c)}[k] = h_{t_m[k]}^{(m)}[k]$$
                $$h_1^{(c)}[k] = h_{t_f[k]}^{(f)}[k]$$
            </div>

            <h3>Recombination Modeling</h3>

            <div class="math-box">
                <h4>Definition 4 (Transmission Cost)</h4>
                <p><strong>Recombination Events:</strong> Changes in transmission vector indicate crossovers</p>

                <p><strong>Cost Function:</strong> For transmission vector $t$ and recombination cost vector $\rho$:</p>
                $$\text{cost}(t) = \sum_{k=2}^{M} \rho[k] \cdot (\mathbb{I}[t_m[k] \neq t_m[k-1]] + \mathbb{I}[t_f[k] \neq t_f[k-1]])$$

                <p><strong>Recombination Costs:</strong> $\rho[k]$ = phred-scaled probability of recombination between positions $k-1$ and $k$</p>
            </div>
        </div>

        <!-- Section 4: Dynamic Programming Algorithm -->
        <div id="algorithm" class="section">
            <h2>4. Dynamic Programming Algorithm</h2>

            <div class="highlight">
                <strong>Algorithmic Strategy:</strong> WhatsHap uses dynamic programming over bipartitions of active reads, extended to handle multiple individuals and transmission constraints.
            </div>

            <h3>Core DP Framework</h3>

            <div class="algorithm-box">
                <h4>Key Concepts</h4>
                <ul class="step-list">
                    <li><strong>Active Reads:</strong> $A(k) = $ set of reads covering position $k$ across all individuals</li>
                    <li><strong>Bipartition:</strong> $B$ partitions $A(k)$ into two sets (maternal/paternal)</li>
                    <li><strong>Transmission Tuple:</strong> $t$ specifies inheritance pattern for all trios</li>
                    <li><strong>DP Table:</strong> $C(k, B, t) = $ optimal cost for positions $1 \ldots k$</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>DP Recurrence</h4>
                <p><strong>State Space:</strong> For each column $k$:</p>
                <ul>
                    <li>$2^{|A(k)|}$ possible bipartitions</li>
                    <li>$2^{2|\mathcal{T}|}$ possible transmission tuples</li>
                    <li>Total: $2^{|A(k)| + 2|\mathcal{T}|}$ states per column</li>
                </ul>

                <p><strong>Recurrence Relation:</strong></p>
                $$C(k+1, B', t') = \min_{B,t} \{C(k, B, t) + L(k+1, B', t') + \rho \cdot d_H(t, t')\}$$

                <p>where:</p>
                <ul>
                    <li>$L(k+1, B', t')$ = local cost for column $k+1$</li>
                    <li>$d_H(t, t')$ = Hamming distance between transmission tuples</li>
                    <li>$B$ and $B'$ must be compatible on overlapping reads</li>
                </ul>
            </div>
        </div>

        <!-- Section 5: Computational Complexity -->
        <div id="complexity" class="section">
            <h2>5. Computational Complexity and Runtime</h2>

            <div class="highlight">
                <strong>Fixed-Parameter Tractability:</strong> Runtime is exponential in coverage but linear in number of variants, making it practical for long-read data.
            </div>

            <div class="math-box">
                <h4>Runtime Analysis</h4>
                <p><strong>Time Complexity:</strong></p>
                $$O(M \cdot 2^{c + 2|\mathcal{T}|} \cdot c \cdot 2^{2|\mathcal{T}|})$$

                <p>where:</p>
                <ul>
                    <li>$M$ = number of variants</li>
                    <li>$c$ = maximum coverage across all individuals</li>
                    <li>$|\mathcal{T}|$ = number of trios</li>
                </ul>

                <p><strong>Key Properties:</strong></p>
                <ul>
                    <li><strong>Linear in $M$:</strong> Scales well with genome size</li>
                    <li><strong>Independent of read length:</strong> Suitable for long reads</li>
                    <li><strong>Exponential in coverage:</strong> Requires coverage pruning</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Practical Considerations</h4>
                <ul>
                    <li><strong>Coverage Pruning:</strong> Limit to 5-15× coverage per individual</li>
                    <li><strong>Read Selection:</strong> Choose reads that connect many variants</li>
                    <li><strong>Memory Usage:</strong> DP table size manageable for typical datasets</li>
                    <li><strong>Parallelization:</strong> Can process chromosomes independently</li>
                </ul>
            </div>
        </div>

        <!-- Section 6: Implementation Details -->
        <div id="implementation" class="section">
            <h2>6. Implementation Details</h2>

            <div class="highlight">
                <strong>Efficient Implementation:</strong> WhatsHap uses several optimizations including Gray code enumeration and projection columns to achieve practical performance.
            </div>

            <h3>Key Optimizations</h3>

            <div class="algorithm-box">
                <h4>Gray Code Enumeration</h4>
                <p><strong>Problem:</strong> Need to enumerate all $2^{|A(k)|}$ bipartitions efficiently</p>
                <p><strong>Solution:</strong> Gray code ensures only one read moves between sets at each step</p>
                <p><strong>Benefit:</strong> Constant-time updates to local cost calculations</p>
            </div>

            <div class="algorithm-box">
                <h4>Projection Columns</h4>
                <p><strong>Purpose:</strong> Efficiently compute transitions between DP columns</p>
                <p><strong>Definition:</strong> Intermediate columns $P(k, B', t')$ for bipartitions of $A(k) \cap A(k+1)$</p>
                <p><strong>Benefit:</strong> Reduces transition computation from $O(4^c)$ to $O(2^c)$</p>
            </div>
        </div>

        <!-- Section 7: Performance Analysis -->
        <div id="performance" class="section">
            <h2>7. Performance Analysis</h2>

            <div class="highlight">
                <strong>Experimental Results:</strong> WhatsHap achieves remarkable performance improvements, especially at low coverage, demonstrating the power of integrating family information.
            </div>

            <h3>Coverage Requirements</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Coverage</th>
                        <th>Error Rate</th>
                        <th>Unphased SNPs</th>
                        <th>Key Advantage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>wMEC-15</strong></td>
                        <td>15× per individual</td>
                        <td>1.4%</td>
                        <td>1.3%</td>
                        <td>Single individual baseline</td>
                    </tr>
                    <tr>
                        <td><strong>PedMEC-G-5</strong></td>
                        <td>5× per individual</td>
                        <td>0.75%</td>
                        <td>0.85%</td>
                        <td>Better with 3× less data</td>
                    </tr>
                    <tr>
                        <td><strong>PedMEC-G-2</strong></td>
                        <td>2× per individual</td>
                        <td>1.4%</td>
                        <td>1.8%</td>
                        <td>Matches 15× single individual</td>
                    </tr>
                </tbody>
            </table>

            <h3>Block Connectivity</h3>

            <div class="info-box">
                <h4>Beyond Read Connections</h4>
                <p><strong>Key Insight:</strong> Genetic information connects haplotype blocks not linked by any reads</p>

                <p><strong>Mechanism:</strong> Homozygous parents + heterozygous child → phase determination</p>

                <p><strong>Results:</strong></p>
                <ul>
                    <li><strong>PedMEC-G:</strong> 89.7% correct inter-block phasing</li>
                    <li><strong>wMEC:</strong> 50.4% correct (random chance)</li>
                </ul>
            </div>
        </div>

        <!-- Section 8: Applications and Results -->
        <div id="applications" class="section">
            <h2>8. Applications and Results</h2>

            <div class="highlight">
                <strong>Real-World Impact:</strong> WhatsHap enables high-quality phasing with minimal sequencing, making family-based studies more cost-effective and accessible.
            </div>

            <h3>Validation Studies</h3>

            <div class="info-box">
                <h4>Genome in a Bottle (GIAB) Results</h4>
                <p><strong>Dataset:</strong> Ashkenazi trio (NA24143, NA24149, NA24385)</p>
                <p><strong>Technology:</strong> PacBio long-read sequencing</p>
                <p><strong>Validation:</strong> Comparison with SHAPEIT population phasing and 10X Genomics</p>

                <p><strong>Key Findings:</strong></p>
                <ul class="step-list">
                    <li><strong>Low Coverage Success:</strong> 2× coverage per individual achieves high accuracy</li>
                    <li><strong>Cost Reduction:</strong> 3× less sequencing for equivalent results</li>
                    <li><strong>Method Agreement:</strong> >99% agreement between independent methods</li>
                    <li><strong>Error Source Analysis:</strong> Most disagreements due to PacBio error rates</li>
                </ul>
            </div>

            <h3>Future Directions</h3>

            <div class="algorithm-box">
                <h4>Planned Extensions</h4>
                <ul class="step-list">
                    <li><strong>De Novo Variants:</strong> Handle mutations not present in parents</li>
                    <li><strong>Larger Pedigrees:</strong> Extend beyond trios to multi-generational families</li>
                    <li><strong>Structural Variants:</strong> Phase complex genomic rearrangements</li>
                    <li><strong>Population Integration:</strong> Combine with statistical phasing methods</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>Current Limitations</h4>
                <ul>
                    <li><strong>Coverage Dependence:</strong> Still requires pruning for very high coverage</li>
                    <li><strong>Error Rate Sensitivity:</strong> Performance degrades with poor quality reads</li>
                    <li><strong>Pedigree Requirements:</strong> Needs family samples, not always available</li>
                    <li><strong>Computational Resources:</strong> Memory usage scales with coverage</li>
                </ul>
            </div>

            <div class="math-box">
                <h4>Statistical Significance</h4>
                <p><strong>Maximum Likelihood Interpretation:</strong></p>
                <p>WhatsHap finds the phasing that maximizes:</p>
                $$P(\text{phasing} | \text{reads, pedigree}) \propto P(\text{reads} | \text{phasing}) \cdot P(\text{recombination pattern})$$

                <p><strong>Components:</strong></p>
                <ul>
                    <li>$P(\text{reads} | \text{phasing})$ modeled by phred quality scores</li>
                    <li>$P(\text{recombination pattern})$ modeled by genetic map distances</li>
                </ul>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Initialize MathJax after page load
        window.addEventListener('load', function() {
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        });
    </script>
</body>
</html>
