<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepVariant: Deep Learning for Genetic Variant Calling</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .outline {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .outline h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .outline li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .outline li:last-child {
            border-bottom: none;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .performance-metric {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-weight: bold;
        }
        
        .performance-metric.good {
            background: #27ae60;
        }
        
        .performance-metric.medium {
            background: #f39c12;
        }
        
        .nav-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .nav-button {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
        
        .cnn-layer {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .data-flow {
            stroke: #e74c3c;
            stroke-width: 3;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .image-pixel {
            stroke: #34495e;
            stroke-width: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DeepVariant: Deep Learning for Genetic Variant Calling</h1>
            <p>Revolutionary CNN-Based Approach to NGS Variant Detection</p>
        </div>
        
        <div class="outline">
            <h2>📋 Presentation Outline</h2>
            <ul>
                <li><strong>1. Introduction & Paradigm Shift</strong> - From statistical modeling to deep learning</li>
                <li><strong>2. Core Architecture</strong> - CNN-based variant calling pipeline</li>
                <li><strong>3. Image Encoding</strong> - Converting genomic data to RGB images</li>
                <li><strong>4. Mathematical Framework</strong> - Deep learning theory and genotype prediction</li>
                <li><strong>5. Training Methodology</strong> - Supervised learning with ground truth data</li>
                <li><strong>6. Performance Analysis</strong> - Benchmarking against traditional methods</li>
                <li><strong>7. Generalization Capabilities</strong> - Cross-platform and cross-species performance</li>
                <li><strong>8. Technical Implementation</strong> - Algorithms and computational details</li>
            </ul>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-button" onclick="showSection(1)">Start Presentation</button>
        </div>
        
        <!-- Section 1: Introduction -->
        <div id="section1" class="section hidden">
            <h2>1. Introduction & Paradigm Shift</h2>
            
            <div class="highlight">
                <strong>Revolutionary Approach:</strong> DeepVariant replaces complex statistical modeling with a single deep convolutional neural network, achieving superior accuracy through automated feature learning.
            </div>
            
            <h3>The Traditional Challenge</h3>
            
            <div class="algorithm-box">
                <h4>Problems with Statistical Approaches</h4>
                <div class="step">
                    <strong>Complex Error Modeling:</strong> NGS reads have error rates of 0.1-10% with technology-specific patterns
                </div>
                <div class="step">
                    <strong>Hand-crafted Features:</strong> Requires expert knowledge to design statistical models
                </div>
                <div class="step">
                    <strong>Technology Dependence:</strong> Models need manual retuning for different sequencing platforms
                </div>
                <div class="step">
                    <strong>Independence Assumptions:</strong> Traditional methods assume read errors are independent (often invalid)
                </div>
            </div>
            
            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">Next: Core Architecture →</button>
            </div>
        </div>

        <!-- Section 2: Core Architecture -->
        <div id="section2" class="section hidden">
            <h2>2. Core Architecture</h2>

            <div class="highlight">
                <strong>Three-Stage Pipeline:</strong> DeepVariant transforms variant calling into an image classification problem using candidate detection, image encoding, and CNN-based genotype prediction.
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        DeepVariant Architecture Overview
                    </text>

                    <!-- Stage 1: Candidate Detection -->
                    <g transform="translate(50, 60)">
                        <rect width="200" height="100" fill="#e74c3c" opacity="0.8" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Stage 1</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Candidate Detection</text>
                        <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">High sensitivity</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10" fill="white">Low specificity</text>
                        <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">Algorithmic preprocessing</text>
                    </g>

                    <!-- Stage 2: Image Encoding -->
                    <g transform="translate(300, 60)">
                        <rect width="200" height="100" fill="#f39c12" opacity="0.8" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Stage 2</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Image Encoding</text>
                        <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">RGB pileup images</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10" fill="white">299×299 pixels</text>
                        <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">Multi-channel encoding</text>
                    </g>

                    <!-- Stage 3: CNN Classification -->
                    <g transform="translate(550, 60)">
                        <rect width="200" height="100" fill="#27ae60" opacity="0.8" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Stage 3</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">CNN Classification</text>
                        <text x="100" y="65" text-anchor="middle" font-size="10" fill="white">Inception v3 architecture</text>
                        <text x="100" y="80" text-anchor="middle" font-size="10" fill="white">3-class output</text>
                        <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">Genotype probabilities</text>
                    </g>

                    <!-- Arrows -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <line x1="250" y1="110" x2="290" y2="110" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <line x1="500" y1="110" x2="540" y2="110" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

                    <!-- Detailed Pipeline -->
                    <g transform="translate(50, 200)">
                        <rect width="700" height="270" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Detailed Processing Pipeline
                        </text>

                        <!-- Input Data -->
                        <g transform="translate(20, 50)">
                            <rect width="100" height="60" fill="#3498db" opacity="0.8" rx="8"/>
                            <text x="50" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Input</text>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">Aligned BAM</text>
                            <text x="50" y="50" text-anchor="middle" font-size="9" fill="white">Reference genome</text>
                        </g>

                        <!-- Preprocessing -->
                        <g transform="translate(140, 50)">
                            <rect width="100" height="60" fill="#9b59b6" opacity="0.8" rx="8"/>
                            <text x="50" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Preprocessing</text>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">Realignment</text>
                            <text x="50" y="50" text-anchor="middle" font-size="9" fill="white">Quality filtering</text>
                        </g>

                        <!-- Candidate Sites -->
                        <g transform="translate(260, 50)">
                            <rect width="100" height="60" fill="#e74c3c" opacity="0.8" rx="8"/>
                            <text x="50" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Candidates</text>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">SNPs & Indels</text>
                            <text x="50" y="50" text-anchor="middle" font-size="9" fill="white">High sensitivity</text>
                        </g>

                        <!-- Image Creation -->
                        <g transform="translate(380, 50)">
                            <rect width="100" height="60" fill="#f39c12" opacity="0.8" rx="8"/>
                            <text x="50" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Image</text>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">RGB encoding</text>
                            <text x="50" y="50" text-anchor="middle" font-size="9" fill="white">Pileup view</text>
                        </g>

                        <!-- CNN Processing -->
                        <g transform="translate(500, 50)">
                            <rect width="100" height="60" fill="#27ae60" opacity="0.8" rx="8"/>
                            <text x="50" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">CNN</text>
                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="white">Inception v3</text>
                            <text x="50" y="50" text-anchor="middle" font-size="9" fill="white">Feature learning</text>
                        </g>

                        <!-- Output -->
                        <g transform="translate(620, 50)">
                            <rect width="60" height="60" fill="#34495e" opacity="0.8" rx="8"/>
                            <text x="30" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Output</text>
                            <text x="30" y="35" text-anchor="middle" font-size="9" fill="white">VCF</text>
                            <text x="30" y="50" text-anchor="middle" font-size="9" fill="white">Calls</text>
                        </g>

                        <!-- Arrows between stages -->
                        <line x1="125" y1="80" x2="135" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="245" y1="80" x2="255" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="365" y1="80" x2="375" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="485" y1="80" x2="495" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="605" y1="80" x2="615" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                        <!-- Genotype Classification -->
                        <g transform="translate(200, 140)">
                            <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                                Three-Class Genotype Classification
                            </text>

                            <!-- Homozygous Reference -->
                            <g transform="translate(0, 40)">
                                <rect width="90" height="50" fill="#3498db" opacity="0.8" rx="8"/>
                                <text x="45" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Hom-Ref</text>
                                <text x="45" y="35" text-anchor="middle" font-size="9" fill="white">0/0</text>
                                <text x="45" y="45" text-anchor="middle" font-size="9" fill="white">AA</text>
                            </g>

                            <!-- Heterozygous -->
                            <g transform="translate(105, 40)">
                                <rect width="90" height="50" fill="#f39c12" opacity="0.8" rx="8"/>
                                <text x="45" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Het</text>
                                <text x="45" y="35" text-anchor="middle" font-size="9" fill="white">0/1</text>
                                <text x="45" y="45" text-anchor="middle" font-size="9" fill="white">AB</text>
                            </g>

                            <!-- Homozygous Alternate -->
                            <g transform="translate(210, 40)">
                                <rect width="90" height="50" fill="#e74c3c" opacity="0.8" rx="8"/>
                                <text x="45" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Hom-Alt</text>
                                <text x="45" y="35" text-anchor="middle" font-size="9" fill="white">1/1</text>
                                <text x="45" y="45" text-anchor="middle" font-size="9" fill="white">BB</text>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Key Architectural Innovations</h3>

            <div class="algorithm-box">
                <h4>Paradigm Shift from Traditional Methods</h4>
                <div class="step">
                    <strong>Universal Approximator:</strong> CNN can model complex read dependencies without explicit statistical assumptions
                </div>
                <div class="step">
                    <strong>End-to-End Learning:</strong> Single model learns optimal features automatically from data
                </div>
                <div class="step">
                    <strong>Technology Agnostic:</strong> Same architecture works across different sequencing platforms
                </div>
                <div class="step">
                    <strong>Image-Based Representation:</strong> Converts genomic data to computer vision problem
                </div>
            </div>

            <h3>Inception v3 Architecture</h3>

            <div class="math-box">
                <h4>CNN Architecture Details</h4>
                <p><strong>Input Layer:</strong> 299×299×3 RGB images (rescaled from pileup images)</p>
                <p><strong>Architecture:</strong> Inception v3 with 9 partitions</p>
                <p><strong>Output Layer:</strong> 3-class Softmax for genotype classification</p>

                <p><strong>Softmax Function:</strong></p>
                $$P(\text{genotype}_i) = \frac{e^{z_i}}{\sum_{j=1}^{3} e^{z_j}}$$

                <p>Where z_i are the logits from the final fully connected layer</p>

                <p><strong>Genotype Classes:</strong></p>
                <ul>
                    <li>Class 0: Homozygous Reference (0/0)</li>
                    <li>Class 1: Heterozygous (0/1)</li>
                    <li>Class 2: Homozygous Alternate (1/1)</li>
                </ul>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(1)">← Previous</button>
                <button class="nav-button" onclick="showSection(3)">Next: Image Encoding →</button>
            </div>
        </div>

        <!-- Section 3: Image Encoding -->
        <div id="section3" class="section hidden">
            <h2>3. Image Encoding</h2>

            <div class="highlight">
                <strong>Genomic-to-Visual Transformation:</strong> DeepVariant encodes read alignments, base qualities, and genomic features into RGB images, enabling CNN-based analysis of complex sequencing patterns.
            </div>

            <h3>RGB Channel Encoding Strategy</h3>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        RGB Channel Encoding for Genomic Data
                    </text>

                    <!-- Red Channel -->
                    <g transform="translate(50, 60)">
                        <rect width="200" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Red Channel</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">Base Information</text>

                        <g transform="translate(20, 60)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Reference base</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">• Read bases (A,C,G,T)</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">• Insertions/Deletions</text>
                        </g>
                    </g>

                    <!-- Green Channel -->
                    <g transform="translate(300, 60)">
                        <rect width="200" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Green Channel</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">Quality Scores</text>

                        <g transform="translate(20, 60)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Base quality (Phred)</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">• Mapping quality</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">• Quality normalization</text>
                        </g>
                    </g>

                    <!-- Blue Channel -->
                    <g transform="translate(550, 60)">
                        <rect width="200" height="120" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">Blue Channel</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">Read Features</text>

                        <g transform="translate(20, 60)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">• Strand information</text>
                            <text x="0" y="30" font-size="10" fill="#2c3e50">• Read position</text>
                            <text x="0" y="45" font-size="10" fill="#2c3e50">• Pair orientation</text>
                        </g>
                    </g>

                    <!-- Pileup Image Example -->
                    <g transform="translate(100, 220)">
                        <rect width="600" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Pileup Image Construction
                        </text>

                        <!-- Reference sequence -->
                        <g transform="translate(50, 50)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#2c3e50">Reference:</text>
                            <rect x="80" y="0" width="20" height="20" fill="#95a5a6"/>
                            <text x="90" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                            <rect x="100" y="0" width="20" height="20" fill="#95a5a6"/>
                            <text x="110" y="15" text-anchor="middle" font-size="12" fill="white">T</text>
                            <rect x="120" y="0" width="20" height="20" fill="#95a5a6"/>
                            <text x="130" y="15" text-anchor="middle" font-size="12" fill="white">G</text>
                            <rect x="140" y="0" width="20" height="20" fill="#95a5a6"/>
                            <text x="150" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="160" y="0" width="20" height="20" fill="#95a5a6"/>
                            <text x="170" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                        </g>

                        <!-- Read 1 -->
                        <g transform="translate(50, 80)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">Read 1:</text>
                            <rect x="80" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="90" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                            <rect x="100" y="0" width="20" height="20" fill="#e74c3c" class="image-pixel"/>
                            <text x="110" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="120" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="130" y="15" text-anchor="middle" font-size="12" fill="white">G</text>
                            <rect x="140" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="150" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="160" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="170" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                        </g>

                        <!-- Read 2 -->
                        <g transform="translate(50, 110)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">Read 2:</text>
                            <rect x="80" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="90" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                            <rect x="100" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="110" y="15" text-anchor="middle" font-size="12" fill="white">T</text>
                            <rect x="120" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="130" y="15" text-anchor="middle" font-size="12" fill="white">G</text>
                            <rect x="140" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="150" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="160" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="170" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                        </g>

                        <!-- Read 3 -->
                        <g transform="translate(50, 140)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">Read 3:</text>
                            <rect x="80" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="90" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                            <rect x="100" y="0" width="20" height="20" fill="#e74c3c" class="image-pixel"/>
                            <text x="110" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="120" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="130" y="15" text-anchor="middle" font-size="12" fill="white">G</text>
                            <rect x="140" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="150" y="15" text-anchor="middle" font-size="12" fill="white">C</text>
                            <rect x="160" y="0" width="20" height="20" fill="#3498db" class="image-pixel"/>
                            <text x="170" y="15" text-anchor="middle" font-size="12" fill="white">A</text>
                        </g>

                        <!-- Variant annotation -->
                        <g transform="translate(250, 80)">
                            <text x="0" y="15" font-size="12" fill="#e74c3c" font-weight="bold">← T→C variant</text>
                            <text x="0" y="35" font-size="10" fill="#2c3e50">Heterozygous</text>
                            <text x="0" y="50" font-size="10" fill="#2c3e50">2/3 reads support</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Mathematical Encoding Framework</h3>

            <div class="math-box">
                <h4>Pixel Value Calculation</h4>
                <p><strong>Base Encoding (Red Channel):</strong></p>
                $$R_{i,j} = \begin{cases}
                0 & \text{if no read coverage} \\
                64 & \text{if reference base} \\
                128 & \text{if alternate base} \\
                192 & \text{if insertion} \\
                255 & \text{if deletion}
                \end{cases}$$

                <p><strong>Quality Encoding (Green Channel):</strong></p>
                $$G_{i,j} = \min(255, \text{base_quality} \times \text{mapping_quality} / 60)$$

                <p><strong>Feature Encoding (Blue Channel):</strong></p>
                $$B_{i,j} = \text{strand} \times 128 + \text{read_position} \times 64 + \text{pair_info} \times 32$$

                <p>Where i,j are pixel coordinates in the 299×299 image</p>
            </div>

            <div class="algorithm-box">
                <h4>Image Construction Algorithm</h4>
                <div class="step">
                    <strong>Window Selection:</strong> Extract reads overlapping candidate variant ± flanking region
                </div>
                <div class="step">
                    <strong>Read Sorting:</strong> Sort reads by alignment position and quality for consistent representation
                </div>
                <div class="step">
                    <strong>Pixel Mapping:</strong> Map each read position to image coordinates (rows = reads, cols = positions)
                </div>
                <div class="step">
                    <strong>Channel Encoding:</strong> Encode base, quality, and feature information into RGB channels
                </div>
                <div class="step">
                    <strong>Normalization:</strong> Resize to 299×299 and normalize pixel values to [0,255]
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">← Previous</button>
                <button class="nav-button" onclick="showSection(4)">Next: Mathematical Framework →</button>
            </div>
        </div>

        <!-- Section 4: Mathematical Framework -->
        <div id="section4" class="section hidden">
            <h2>4. Mathematical Framework</h2>

            <div class="highlight">
                <strong>Deep Learning Theory:</strong> DeepVariant leverages convolutional neural networks as universal approximators to learn the complex, unknown likelihood function relating sequencing reads to true genotypes.
            </div>

            <h3>Theoretical Foundation</h3>

            <div class="math-box">
                <h4>Universal Approximation Theorem</h4>
                <p><strong>Problem:</strong> Traditional methods assume read independence, but true likelihood function is unknown:</p>

                $$L(\text{genotype} | \text{reads}) = P(\text{reads} | \text{genotype})$$

                <p><strong>Traditional Assumption (Invalid):</strong></p>
                $$L_{\text{traditional}} = \prod_{i=1}^{n} P(\text{read}_i | \text{genotype})$$

                <p><strong>True Likelihood (Unknown):</strong></p>
                $$L_{\text{true}} = P(\text{read}_1, \text{read}_2, ..., \text{read}_n | \text{genotype})$$

                <p><strong>CNN Approximation:</strong></p>
                $$L_{\text{CNN}} \approx L_{\text{true}}$$

                <p>Where the CNN learns to approximate the true interdependent likelihood function</p>
            </div>

            <div class="math-box">
                <h4>Convolutional Neural Network Mathematics</h4>

                <p><strong>Convolution Operation:</strong></p>
                $$(\mathbf{I} * \mathbf{K})_{i,j} = \sum_{m}\sum_{n} \mathbf{I}_{i+m,j+n} \cdot \mathbf{K}_{m,n}$$

                <p><strong>Activation Function (ReLU):</strong></p>
                $$f(x) = \max(0, x)$$

                <p><strong>Pooling Operation:</strong></p>
                $$\text{MaxPool}(\mathbf{X})_{i,j} = \max_{(m,n) \in \text{window}} \mathbf{X}_{i+m,j+n}$$

                <p><strong>Batch Normalization:</strong></p>
                $$\hat{x} = \frac{x - \mu}{\sqrt{\sigma^2 + \epsilon}}$$
                $$y = \gamma \hat{x} + \beta$$
            </div>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        CNN Architecture for Genotype Classification
                    </text>

                    <!-- Input Image -->
                    <g transform="translate(50, 60)">
                        <rect width="80" height="80" fill="#3498db" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="40" y="45" text-anchor="middle" font-size="10" fill="white">Input</text>
                        <text x="40" y="60" text-anchor="middle" font-size="9" fill="white">299×299×3</text>
                        <text x="40" y="100" text-anchor="middle" font-size="9" fill="#2c3e50">RGB Image</text>
                    </g>

                    <!-- Conv Layers -->
                    <g transform="translate(180, 60)">
                        <rect width="60" height="70" fill="#e74c3c" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="30" y="40" text-anchor="middle" font-size="10" fill="white">Conv</text>
                        <text x="30" y="55" text-anchor="middle" font-size="9" fill="white">Layers</text>
                        <text x="30" y="90" text-anchor="middle" font-size="9" fill="#2c3e50">Feature Maps</text>
                    </g>

                    <!-- Inception Modules -->
                    <g transform="translate(290, 60)">
                        <rect width="80" height="60" fill="#f39c12" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="40" y="35" text-anchor="middle" font-size="10" fill="white">Inception</text>
                        <text x="40" y="50" text-anchor="middle" font-size="9" fill="white">Modules</text>
                        <text x="40" y="80" text-anchor="middle" font-size="9" fill="#2c3e50">Multi-scale</text>
                    </g>

                    <!-- Global Average Pooling -->
                    <g transform="translate(420, 60)">
                        <rect width="60" height="50" fill="#9b59b6" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="30" y="30" text-anchor="middle" font-size="10" fill="white">Global</text>
                        <text x="30" y="45" text-anchor="middle" font-size="9" fill="white">Pool</text>
                        <text x="30" y="70" text-anchor="middle" font-size="9" fill="#2c3e50">Avg Pool</text>
                    </g>

                    <!-- Fully Connected -->
                    <g transform="translate(530, 60)">
                        <rect width="60" height="40" fill="#27ae60" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="30" y="25" text-anchor="middle" font-size="10" fill="white">FC</text>
                        <text x="30" y="55" text-anchor="middle" font-size="9" fill="#2c3e50">Dense</text>
                    </g>

                    <!-- Softmax Output -->
                    <g transform="translate(640, 60)">
                        <rect width="60" height="30" fill="#34495e" opacity="0.8" rx="5" class="cnn-layer"/>
                        <text x="30" y="20" text-anchor="middle" font-size="10" fill="white">Softmax</text>
                        <text x="30" y="45" text-anchor="middle" font-size="9" fill="#2c3e50">3 Classes</text>
                    </g>

                    <!-- Arrows -->
                    <line x1="135" y1="100" x2="175" y2="95" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="245" y1="95" x2="285" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="375" y1="90" x2="415" y2="85" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="485" y1="85" x2="525" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="595" y1="80" x2="635" y2="75" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <!-- Output Probabilities -->
                    <g transform="translate(200, 200)">
                        <text x="200" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Genotype Probability Output
                        </text>

                        <!-- Probability bars -->
                        <g transform="translate(50, 40)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">P(0/0):</text>
                            <rect x="50" y="0" width="60" height="20" fill="#3498db" opacity="0.8"/>
                            <text x="115" y="15" font-size="11" fill="#2c3e50">0.15</text>
                        </g>

                        <g transform="translate(50, 70)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">P(0/1):</text>
                            <rect x="50" y="0" width="200" height="20" fill="#f39c12" opacity="0.8"/>
                            <text x="255" y="15" font-size="11" fill="#2c3e50">0.75</text>
                        </g>

                        <g transform="translate(50, 100)">
                            <text x="0" y="15" font-size="12" fill="#2c3e50">P(1/1):</text>
                            <rect x="50" y="0" width="30" height="20" fill="#e74c3c" opacity="0.8"/>
                            <text x="85" y="15" font-size="11" fill="#2c3e50">0.10</text>
                        </g>

                        <text x="150" y="140" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">
                            Predicted: Heterozygous (0/1)
                        </text>
                    </g>
                </svg>
            </div>

            <h3>Loss Function and Training</h3>

            <div class="math-box">
                <h4>Cross-Entropy Loss</h4>
                <p><strong>Multi-class Classification Loss:</strong></p>

                $$\mathcal{L} = -\sum_{i=1}^{N} \sum_{c=1}^{3} y_{i,c} \log(\hat{y}_{i,c})$$

                <p>Where:</p>
                <ul>
                    <li>N = number of training examples</li>
                    <li>y_{i,c} = true label (one-hot encoded)</li>
                    <li>ŷ_{i,c} = predicted probability for class c</li>
                </ul>

                <p><strong>Gradient Descent Update:</strong></p>
                $$\theta_{t+1} = \theta_t - \eta \nabla_\theta \mathcal{L}$$

                <p><strong>Stochastic Gradient Descent with Momentum:</strong></p>
                $$v_t = \mu v_{t-1} + \eta \nabla_\theta \mathcal{L}$$
                $$\theta_{t+1} = \theta_t - v_t$$

                <p>Where μ = momentum coefficient (0.8-0.9), η = learning rate (0.001-0.0015)</p>
            </div>

            <div class="algorithm-box">
                <h4>Training Hyperparameters</h4>
                <div class="step">
                    <strong>Batch Size:</strong> 32 images per batch with 8 replicated models
                </div>
                <div class="step">
                    <strong>Learning Rate:</strong> Grid search over [0.00095, 0.001, 0.0015]
                </div>
                <div class="step">
                    <strong>Momentum:</strong> Grid search over [0.8, 0.85, 0.9]
                </div>
                <div class="step">
                    <strong>Weight Decay:</strong> 0.00004 for regularization
                </div>
                <div class="step">
                    <strong>RMS Decay:</strong> 0.9 for adaptive learning rate
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(3)">← Previous</button>
                <button class="nav-button" onclick="showSection(5)">Next: Training Methodology →</button>
            </div>
        </div>

        <!-- Section 5: Training Methodology -->
        <div id="section5" class="section hidden">
            <h2>5. Training Methodology</h2>

            <div class="highlight">
                <strong>Supervised Learning Approach:</strong> DeepVariant trains on labeled image-genotype pairs from samples with known ground truth, using transfer learning from ImageNet for initialization.
            </div>

            <h3>Training Data Preparation</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Training Data Generation Pipeline
                    </text>

                    <!-- Ground Truth Data -->
                    <g transform="translate(50, 60)">
                        <rect width="150" height="80" fill="#3498db" opacity="0.8" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Ground Truth</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">Genome in a Bottle</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">Platinum Genomes</text>
                        <text x="75" y="75" text-anchor="middle" font-size="10" fill="white">Known genotypes</text>
                    </g>

                    <!-- Sequencing Data -->
                    <g transform="translate(250, 60)">
                        <rect width="150" height="80" fill="#e74c3c" opacity="0.8" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Sequencing Data</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">Aligned BAM files</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">Multiple platforms</text>
                        <text x="75" y="75" text-anchor="middle" font-size="10" fill="white">Various depths</text>
                    </g>

                    <!-- Image Generation -->
                    <g transform="translate(450, 60)">
                        <rect width="150" height="80" fill="#f39c12" opacity="0.8" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Image Generation</text>
                        <text x="75" y="45" text-anchor="middle" font-size="10" fill="white">RGB encoding</text>
                        <text x="75" y="60" text-anchor="middle" font-size="10" fill="white">299×299 pixels</text>
                        <text x="75" y="75" text-anchor="middle" font-size="10" fill="white">Pileup views</text>
                    </g>

                    <!-- Training Set -->
                    <g transform="translate(650, 60)">
                        <rect width="100" height="80" fill="#27ae60" opacity="0.8" rx="10"/>
                        <text x="50" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Training</text>
                        <text x="50" y="40" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Set</text>
                        <text x="50" y="60" text-anchor="middle" font-size="10" fill="white">Labeled</text>
                        <text x="50" y="75" text-anchor="middle" font-size="10" fill="white">Examples</text>
                    </g>

                    <!-- Arrows -->
                    <line x1="205" y1="100" x2="245" y2="100" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="405" y1="100" x2="445" y2="100" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="605" y1="100" x2="645" y2="100" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <!-- Training Process -->
                    <g transform="translate(100, 180)">
                        <rect width="600" height="180" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Training Process Details
                        </text>

                        <!-- Data Augmentation -->
                        <g transform="translate(50, 50)">
                            <rect width="120" height="60" fill="#9b59b6" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Data Aug</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="white">Random sampling</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Coverage variation</text>
                        </g>

                        <!-- Transfer Learning -->
                        <g transform="translate(200, 50)">
                            <rect width="120" height="60" fill="#e67e22" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Transfer</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="white">ImageNet init</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Pre-trained CNN</text>
                        </g>

                        <!-- Fine-tuning -->
                        <g transform="translate(350, 50)">
                            <rect width="120" height="60" fill="#1abc9c" opacity="0.8" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Fine-tuning</text>
                            <text x="60" y="35" text-anchor="middle" font-size="9" fill="white">Genomic data</text>
                            <text x="60" y="50" text-anchor="middle" font-size="9" fill="white">Domain adaptation</text>
                        </g>

                        <!-- Validation -->
                        <g transform="translate(500, 50)">
                            <rect width="80" height="60" fill="#c0392b" opacity="0.8" rx="8"/>
                            <text x="40" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Valid</text>
                            <text x="40" y="35" text-anchor="middle" font-size="9" fill="white">Hold-out</text>
                            <text x="40" y="50" text-anchor="middle" font-size="9" fill="white">Accuracy</text>
                        </g>

                        <!-- Training metrics -->
                        <g transform="translate(150, 130)">
                            <text x="150" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">
                                Training Configuration
                            </text>
                            <text x="0" y="35" font-size="10" fill="#2c3e50">• 80 hours training time</text>
                            <text x="150" y="35" font-size="10" fill="#2c3e50">• 250,000 update steps</text>
                            <text x="300" y="35" font-size="10" fill="#2c3e50">• Multiple model selection</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Transfer Learning Strategy</h3>

            <div class="math-box">
                <h4>ImageNet Pre-training Benefits</h4>
                <p><strong>Feature Hierarchy Transfer:</strong></p>
                <ul>
                    <li><strong>Low-level features:</strong> Edge detection, texture patterns</li>
                    <li><strong>Mid-level features:</strong> Shape recognition, spatial patterns</li>
                    <li><strong>High-level features:</strong> Complex pattern combinations</li>
                </ul>

                <p><strong>Mathematical Formulation:</strong></p>
                $$\theta_{\text{genomic}} = \arg\min_\theta \mathcal{L}_{\text{genomic}}(\theta | \theta_{\text{ImageNet}})$$

                <p>Where θ_ImageNet provides initialization for genomic fine-tuning</p>

                <p><strong>Learning Rate Schedule:</strong></p>
                $$\eta_t = \eta_0 \times \text{decay}^{\lfloor t / \text{step_size} \rfloor}$$
            </div>

            <div class="algorithm-box">
                <h4>Training Data Characteristics</h4>
                <div class="step">
                    <strong>Genome in a Bottle:</strong> High-confidence variant calls for NA12878, NA24385
                </div>
                <div class="step">
                    <strong>Platinum Genomes:</strong> Family-based validation for additional samples
                </div>
                <div class="step">
                    <strong>Class Balance:</strong> Roughly equal representation of hom-ref, het, hom-alt
                </div>
                <div class="step">
                    <strong>Technology Diversity:</strong> Multiple sequencing platforms and protocols
                </div>
            </div>

            <h3>Model Selection and Validation</h3>

            <div class="math-box">
                <h4>Performance Metrics</h4>

                <p><strong>Accuracy:</strong></p>
                $$\text{Accuracy} = \frac{\text{Correct Predictions}}{\text{Total Predictions}}$$

                <p><strong>F1 Score:</strong></p>
                $$F1 = \frac{2 \times \text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}$$

                <p><strong>Genotype Concordance:</strong></p>
                $$\text{Concordance} = \frac{\text{Matching Genotypes}}{\text{Total Evaluated Sites}}$$

                <p><strong>Calibration Assessment:</strong></p>
                $$\text{Calibration Error} = \sum_{i=1}^{B} \frac{n_i}{N} |p_i - a_i|$$

                <p>Where p_i = predicted probability, a_i = actual accuracy in bin i</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(4)">← Previous</button>
                <button class="nav-button" onclick="showSection(6)">Next: Performance Analysis →</button>
            </div>
        </div>

        <!-- Section 6: Performance Analysis -->
        <div id="section6" class="section hidden">
            <h2>6. Performance Analysis</h2>

            <div class="highlight">
                <strong>Superior Performance:</strong> DeepVariant achieves >50% fewer errors than next-best method, with SNP F1=99.95% and indel F1=98.98% on FDA Truth Challenge.
            </div>

            <h3>Benchmark Results</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>SNP F1 (%)</th>
                        <th>Indel F1 (%)</th>
                        <th>Total Errors</th>
                        <th>Error Reduction</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>DeepVariant</strong></td>
                        <td><span class="performance-metric good">99.95</span></td>
                        <td><span class="performance-metric good">98.98</span></td>
                        <td><span class="performance-metric good">4,652</span></td>
                        <td><span class="performance-metric good">Baseline</span></td>
                    </tr>
                    <tr>
                        <td>GATK HaplotypeCaller</td>
                        <td><span class="performance-metric medium">99.38</span></td>
                        <td><span class="performance-metric medium">97.84</span></td>
                        <td>9,531</td>
                        <td>51.2% worse</td>
                    </tr>
                    <tr>
                        <td>FreeBayes</td>
                        <td><span class="performance-metric medium">99.16</span></td>
                        <td><span class="performance-metric medium">95.64</span></td>
                        <td>12,847</td>
                        <td>63.8% worse</td>
                    </tr>
                    <tr>
                        <td>SAMtools</td>
                        <td><span class="performance-metric">98.74</span></td>
                        <td><span class="performance-metric">92.18</span></td>
                        <td>18,392</td>
                        <td>74.7% worse</td>
                    </tr>
                </tbody>
            </table>

            <h3>Calibration Analysis</h3>

            <div class="math-box">
                <h4>Probability Calibration</h4>
                <p><strong>Well-Calibrated Predictions:</strong> DeepVariant's predicted probabilities closely match observed error rates</p>

                <p><strong>Calibration Curve:</strong></p>
                $$\text{Observed Accuracy} \approx \text{Predicted Probability}$$

                <p><strong>Reliability Diagram:</strong> Plot of predicted vs. observed accuracy across probability bins</p>

                <p><strong>Expected Calibration Error (ECE):</strong></p>
                $$ECE = \sum_{m=1}^{M} \frac{|B_m|}{n} |\text{acc}(B_m) - \text{conf}(B_m)|$$

                <p>Where B_m are probability bins, acc = accuracy, conf = confidence</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(5)">← Previous</button>
                <button class="nav-button" onclick="showSection(7)">Next: Generalization →</button>
            </div>
        </div>

        <!-- Section 7: Generalization Capabilities -->
        <div id="section7" class="section hidden">
            <h2>7. Generalization Capabilities</h2>

            <div class="highlight">
                <strong>Remarkable Generalization:</strong> Models trained on human data achieve high accuracy across genome builds, sequencing technologies, and even different mammalian species.
            </div>

            <h3>Cross-Platform Performance</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Technology</th>
                        <th>Training Data</th>
                        <th>Candidate PPV (%)</th>
                        <th>Final PPV (%)</th>
                        <th>Sensitivity (%)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Illumina WGS</td>
                        <td>Human (Illumina)</td>
                        <td><span class="performance-metric good">85.2</span></td>
                        <td><span class="performance-metric good">99.5</span></td>
                        <td><span class="performance-metric good">98.1</span></td>
                    </tr>
                    <tr>
                        <td>SOLiD WGS</td>
                        <td>Human (Illumina)</td>
                        <td><span class="performance-metric">13.9</span></td>
                        <td><span class="performance-metric good">99.0</span></td>
                        <td><span class="performance-metric medium">76.6</span></td>
                    </tr>
                    <tr>
                        <td>PacBio WGS</td>
                        <td>Human (Illumina)</td>
                        <td><span class="performance-metric">22.1</span></td>
                        <td><span class="performance-metric good">97.3</span></td>
                        <td><span class="performance-metric good">88.5</span></td>
                    </tr>
                    <tr>
                        <td>Ion Ampliseq</td>
                        <td>Human (Illumina)</td>
                        <td><span class="performance-metric">8.1</span></td>
                        <td><span class="performance-metric good">99.7</span></td>
                        <td><span class="performance-metric good">92.6</span></td>
                    </tr>
                </tbody>
            </table>

            <h3>Cross-Species Performance</h3>

            <div class="algorithm-box">
                <h4>Human-to-Mouse Transfer</h4>
                <div class="step">
                    <strong>Training:</strong> Human data (50× 2×148bp, HiSeq 2500, TruSeq prep)
                </div>
                <div class="step">
                    <strong>Testing:</strong> Mouse data (27× 2×100bp, Genome Analyzer II, custom prep)
                </div>
                <div class="step">
                    <strong>Result:</strong> F1 = 98.29% (better than mouse-trained model: 97.84%)
                </div>
                <div class="step">
                    <strong>Implication:</strong> Human ground truth can improve non-human variant calling
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(6)">← Previous</button>
                <button class="nav-button" onclick="showSection(8)">Next: Implementation →</button>
            </div>
        </div>

        <!-- Section 8: Technical Implementation -->
        <div id="section8" class="section hidden">
            <h2>8. Technical Implementation</h2>

            <div class="highlight">
                <strong>Production-Ready System:</strong> DeepVariant provides complete implementation with preprocessing, training, and inference pipelines, available as open-source software.
            </div>

            <h3>Algorithm Details</h3>

            <div class="math-box">
                <h4>Candidate Variant Detection</h4>
                <p><strong>Allele Counting Algorithm:</strong></p>

                <p>For each genomic position i:</p>
                $$\text{alleles}_i = \{a : \text{count}(a, i) \geq \text{threshold}\}$$

                <p><strong>Quality Filtering:</strong></p>
                $$\text{usable_read} = \text{MAPQ} \geq 10 \land \text{base_quality} \geq 10$$

                <p><strong>Emission Criteria:</strong></p>
                $$\text{emit_variant} = |\text{alleles}_i \setminus \{\text{reference}\}| > 0$$
            </div>

            <div class="algorithm-box">
                <h4>Implementation Pipeline</h4>
                <div class="step">
                    <strong>Preprocessing:</strong> Haplotype-aware realignment using De Bruijn graphs
                </div>
                <div class="step">
                    <strong>Candidate Detection:</strong> High-sensitivity algorithmic variant calling
                </div>
                <div class="step">
                    <strong>Image Generation:</strong> RGB encoding of pileup data
                </div>
                <div class="step">
                    <strong>CNN Inference:</strong> Genotype probability prediction
                </div>
                <div class="step">
                    <strong>Post-processing:</strong> VCF output with quality scores
                </div>
            </div>

            <h3>Software Architecture</h3>

            <div class="math-box">
                <h4>Key Components</h4>
                <p><strong>make_examples.py:</strong> Candidate detection and image generation</p>
                <p><strong>call_variants.py:</strong> CNN inference on pileup images</p>
                <p><strong>postprocess_variants.py:</strong> VCF formatting and quality assignment</p>

                <p><strong>Quality Score Calculation:</strong></p>
                $$Q = -10 \log_{10}(1 - P_{\text{correct}})$$

                <p>Where P_correct is the CNN's predicted probability for the called genotype</p>
            </div>

            <div class="highlight">
                <strong>Open Source Impact:</strong> DeepVariant democratizes state-of-the-art variant calling, enabling researchers worldwide to benefit from deep learning advances in genomics without requiring machine learning expertise.
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(7)">← Previous</button>
                <button class="nav-button" onclick="showSection(1)">Return to Start</button>
            </div>
        </div>

        <script>
            function showSection(sectionNum) {
                // Hide all sections
                for (let i = 1; i <= 8; i++) {
                    const section = document.getElementById('section' + i);
                    if (section) {
                        section.classList.add('hidden');
                    }
                }
                
                // Show the requested section
                const targetSection = document.getElementById('section' + sectionNum);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
            
            // Initialize MathJax after page load
            window.addEventListener('load', function() {
                if (window.MathJax) {
                    MathJax.typesetPromise();
                }
            });
        </script>
    </div>
</body>
</html>
