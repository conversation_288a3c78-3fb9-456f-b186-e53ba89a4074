<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATK Tutorial - Genome Analysis Toolkit</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            margin-top: 0;
            font-size: 2.5rem;
        }
        h2 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 2rem;
        }
        h3 {
            color: #3498db;
            margin-top: 1.5rem;
        }
        .section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .formula {
            background: #f1f8ff;
            padding: 1rem;
            border-left: 4px solid #3498db;
            margin: 1.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .visualization svg {
            max-width: 100%;
            height: auto;
        }
        .authors {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 1rem;
        }
        .abstract {
            background: #e8f4fc;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1.5rem 0;
            border-left: 4px solid #3498db;
        }
        .highlight {
            background-color: #fffacd;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }
        .note {
            background: #fff8e1;
            padding: 1rem;
            border-left: 4px solid #ffc107;
            margin: 1.5rem 0;
            border-radius: 0 4px 4px 0;
        }
        code {
            background: #f1f2f6;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
        }
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .nav-links {
            display: flex;
            justify-content: space-between;
            margin: 1rem 0;
        }
        .nav-links a {
            text-decoration: none;
            color: #3498db;
            font-weight: bold;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <h1>GATK Tutorial: Genome Analysis Toolkit</h1>
        <p class="authors">Aaron McKenna et al. - Broad Institute of Harvard and MIT</p>
        <p>A comprehensive guide to understanding the MapReduce framework for analyzing next-generation DNA sequencing data</p>
    </header>

    <div class="nav-links">
        <a href="#introduction">Introduction</a>
        <a href="#architecture">Architecture</a>
        <a href="#methods">Methods</a>
        <a href="#parallelization">Parallelization</a>
        <a href="#results">Results</a>
    </div>

    <div class="section" id="introduction">
        <h2>Introduction to GATK</h2>
        
        <div class="abstract">
            <h3>Abstract</h3>
            <p>Next-generation DNA sequencing (NGS) projects, such as the 1000 Genomes Project, are revolutionizing our understanding of genetic variation among individuals. However, the massive data sets generated by NGS make writing feature-rich, efficient, and robust analysis tools difficult. The Genome Analysis Toolkit (GATK) is a structured programming framework designed to ease the development of efficient and robust analysis tools for next-generation DNA sequencers using the functional programming philosophy of MapReduce.</p>
        </div>

        <p>The GATK provides a small but rich set of data access patterns that encompass the majority of analysis tool needs. Separating specific analysis calculations from common data management infrastructure enables optimization of the GATK framework for correctness, stability, and CPU and memory efficiency, and enables distributed and shared memory parallelization.</p>

        <div class="note">
            <p><strong>Key Insight:</strong> The GATK programming framework enables developers and analysts to quickly and easily write efficient and robust NGS tools, many of which have already been incorporated into large-scale sequencing projects like the 1000 Genomes Project and The Cancer Genome Atlas.</p>
        </div>
    </div>

    <div class="section" id="architecture">
        <h2>GATK Architecture</h2>
        
        <h3>MapReduce Framework</h3>
        <p>The GATK was designed using the functional programming paradigm of <span class="highlight">MapReduce</span>. This approach makes a contract with the developer, in which analysis tools are constructed so that the underlying framework can easily parallelize and distribute processing.</p>
        
        <p>MapReduce divides computations into two separate steps:</p>
        <ol>
            <li><strong>Map:</strong> The larger problem is subdivided into many discrete independent pieces, which are fed to the map function</li>
            <li><strong>Reduce:</strong> The map results are joined back into a final product</li>
        </ol>

        <div class="visualization">
            <h3>MapReduce Concept in GATK</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Map Phase -->
                <rect x="50" y="50" width="200" height="100" fill="#3498db" rx="10" />
                <text x="150" y="90" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Map Phase</text>
                <text x="150" y="120" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Divide problem into</text>
                <text x="150" y="135" font-family="Arial" font-size="12" fill="white" text-anchor="middle">independent pieces</text>
                
                <!-- Arrows to Reduce -->
                <line x1="250" y1="75" x2="350" y2="75" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="250" y1="125" x2="350" y2="175" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="250" y1="100" x2="350" y2="125" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Reduce Phase -->
                <rect x="350" y="50" width="200" height="100" fill="#e74c3c" rx="10" />
                <text x="450" y="90" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Reduce Phase</text>
                <text x="450" y="120" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Combine results</text>
                <text x="450" y="135" font-family="Arial" font-size="12" fill="white" text-anchor="middle">into final output</text>
                
                <!-- Data Flow Labels -->
                <text x="300" y="65" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Data</text>
                <text x="300" y="115" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Chunks</text>
                
                <!-- Input Data -->
                <rect x="30" y="200" width="100" height="40" fill="#95a5a6" rx="5" />
                <text x="80" y="225" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Input Data</text>
                
                <!-- Arrow to Map -->
                <line x1="80" y1="200" x2="150" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Output Result -->
                <rect x="400" y="200" width="100" height="40" fill="#95a5a6" rx="5" />
                <text x="450" y="225" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Final Result</text>
                
                <!-- Arrow from Reduce -->
                <line x1="450" y1="150" x2="450" y2="200" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Arrowhead Definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                
                <!-- GATK Specific Labels -->
                <text x="150" y="30" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">GATK MapReduce Framework</text>
                <text x="150" y="180" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">Traversals</text>
                <text x="450" y="180" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">Walkers</text>
            </svg>
        </div>

        <h3>Core Components</h3>
        <p>The GATK is structured into:</p>
        <ul>
            <li><strong>Traversals:</strong> Provide the division and preparation of data</li>
            <li><strong>Walkers (Analysis Modules):</strong> Provide the map and reduce methods that consume the data</li>
        </ul>

        <p>In this contract, the traversal provides a succession of associated bundles of data to the analysis walker, and the analysis walker consumes these bundles of data, optionally emitting an output for each bundle to be reduced.</p>
    </div>

    <div class="section" id="methods">
        <h2>Key Methods in GATK</h2>
        
        <h3>Traversal Types</h3>
        <p>As stated above, the GATK provides a collection of common data presentation schemes, called traversals, to walker developers. For example, traversals "by each sequencer read" and "by every read covering each single base position in a genome" are the standard methods for accessing data.</p>
        
        <ul>
            <li><strong>Locus-based traversal:</strong> Most commonly used traversal type. Presents the analysis walkers with all the associated genomic data, including all the reads that span the genomic location, all reference ordered data, and the reference base at the specific locus.</li>
            <li><strong>Read-based traversal:</strong> Presents the analysis walker with each read individually, passing each read once and only once to the walker's map function.</li>
        </ul>

        <div class="visualization">
            <h3>Read-based and Locus-based Traversals</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Reference Genome -->
                <rect x="100" y="50" width="600" height="30" fill="#27ae60" rx="5" />
                <text x="400" y="70" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Reference Genome</text>
                
                <!-- Locus-based traversal visualization -->
                <text x="100" y="120" font-family="Arial" font-size="16" fill="#2c3e50" font-weight="bold">Locus-based Traversal</text>
                
                <!-- Locus position -->
                <circle cx="300" cy="65" r="8" fill="#e74c3c" />
                <line x1="300" y1="73" x2="300" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5" />
                
                <!-- Reads covering locus -->
                <rect x="250" y="100" width="100" height="15" fill="#3498db" rx="3" />
                <rect x="270" y="120" width="100" height="15" fill="#3498db" rx="3" />
                <rect x="230" y="140" width="100" height="15" fill="#3498db" rx="3" />
                <rect x="280" y="160" width="100" height="15" fill="#3498db" rx="3" />
                
                <!-- Analysis at locus -->
                <rect x="250" y="190" width="100" height="40" fill="#f39c12" rx="5" />
                <text x="300" y="215" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Analysis</text>
                
                <!-- Read-based traversal visualization -->
                <text x="100" y="270" font-family="Arial" font-size="16" fill="#2c3e50" font-weight="bold">Read-based Traversal</text>
                
                <!-- Individual reads -->
                <rect x="150" y="290" width="100" height="15" fill="#9b59b6" rx="3" />
                <rect x="270" y="290" width="100" height="15" fill="#9b59b6" rx="3" />
                <rect x="390" y="290" width="100" height="15" fill="#9b59b6" rx="3" />
                <rect x="510" y="290" width="100" height="15" fill="#9b59b6" rx="3" />
                
                <!-- Analysis per read -->
                <rect x="150" y="320" width="100" height="30" fill="#f39c12" rx="5" />
                <rect x="270" y="320" width="100" height="30" fill="#f39c12" rx="5" />
                <rect x="390" y="320" width="100" height="30" fill="#f39c12" rx="5" />
                <rect x="510" y="320" width="100" height="30" fill="#f39c12" rx="5" />
                
                <text x="200" y="340" font-family="Arial" font-size="10" fill="white" text-anchor="middle">Analysis</text>
                <text x="320" y="340" font-family="Arial" font-size="10" fill="white" text-anchor="middle">Analysis</text>
                <text x="440" y="340" font-family="Arial" font-size="10" fill="white" text-anchor="middle">Analysis</text>
                <text x="560" y="340" font-family="Arial" font-size="10" fill="white" text-anchor="middle">Analysis</text>
                
                <!-- Labels -->
                <text x="300" y="30" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle" font-weight="bold">GATK Traversal Types</text>
                <text x="70" y="65" font-family="Arial" font-size="12" fill="#7f8c8d">Locus</text>
                <text x="70" y="110" font-family="Arial" font-size="12" fill="#7f8c8d">Reads</text>
                <text x="70" y="205" font-family="Arial" font-size="12" fill="#7f8c8d">Analysis</text>
                <text x="70" y="295" font-family="Arial" font-size="12" fill="#7f8c8d">Reads</text>
                <text x="70" y="335" font-family="Arial" font-size="12" fill="#7f8c8d">Analysis</text>
            </svg>
        </div>

        <h3>Depth of Coverage Walker</h3>
        <p>Determining the depth of coverage (DoC) in the whole genome, whole exome, or in a targeted hybrid capture sequencing run is a computationally simple, but critical analysis tool. Depth-of-coverage calculations play an important role in accurate CNV discovery, SNP calling, and other downstream analysis methods.</p>

        <p>The DoC code contains 83 lines of code, extending the locus walker template. At each site the walker receives a list of the reads covering the reference base and emits the size of the pileup. The end user can optionally exclude reads of low mapping quality, reads indicated to be deletions at the current locus, and other read filtering criteria.</p>

        <div class="visualization">
            <h3>MHC Depth of Coverage in JPT Samples</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Axes -->
                <line x1="80" y1="300" x2="750" y2="300" stroke="#2c3e50" stroke-width="2" />
                <line x1="80" y1="300" x2="80" y2="50" stroke="#2c3e50" stroke-width="2" />
                
                <!-- Axis labels -->
                <text x="400" y="340" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle">Genomic Position (Mbp)</text>
                <text x="20" y="175" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle" transform="rotate(-90, 20, 175)">Coverage</text>
                
                <!-- Scale labels -->
                <text x="80" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">32.0</text>
                <text x="200" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">32.2</text>
                <text x="320" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">32.4</text>
                <text x="440" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">32.6</text>
                <text x="560" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">32.8</text>
                <text x="680" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">33.0</text>
                
                <text x="60" y="300" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">0</text>
                <text x="60" y="250" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">20</text>
                <text x="60" y="200" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">40</text>
                <text x="60" y="150" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">60</text>
                <text x="60" y="100" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">80</text>
                <text x="60" y="50" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">100</text>
                
                <!-- Coverage curve (simulated data) -->
                <path d="M80,300 
                         L100,280 
                         L120,270 
                         L140,260 
                         L160,250 
                         L180,240 
                         L200,230 
                         L220,220 
                         L240,210 
                         L260,200 
                         L280,190 
                         L300,180 
                         L320,170 
                         L340,160 
                         L360,170 
                         L380,180 
                         L400,190 
                         L420,200 
                         L440,210 
                         L460,220 
                         L480,230 
                         L500,240 
                         L520,250 
                         L540,260 
                         L560,270 
                         L580,280 
                         L600,290 
                         L620,280 
                         L640,270 
                         L660,260 
                         L680,250 
                         L700,240 
                         L720,230 
                         L740,220 
                         L750,210" 
                      fill="none" stroke="#3498db" stroke-width="3" />
                
                <!-- HLA gene regions (highlighted areas) -->
                <rect x="250" y="50" width="40" height="250" fill="#e74c3c" opacity="0.3" />
                <rect x="450" y="50" width="50" height="250" fill="#e74c3c" opacity="0.3" />
                
                <!-- Labels -->
                <text x="400" y="30" font-family="Arial" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">MHC Depth of Coverage in JPT Samples</text>
                <text x="270" y="40" font-family="Arial" font-size="12" fill="#e74c3c" text-anchor="middle">HLA Region</text>
                <text x="475" y="40" font-family="Arial" font-size="12" fill="#e74c3c" text-anchor="middle">HLA Region</text>
                
                <!-- Legend -->
                <rect x="600" y="60" width="15" height="15" fill="#3498db" />
                <text x="620" y="72" font-family="Arial" font-size="12" fill="#2c3e50">Coverage</text>
                
                <rect x="600" y="85" width="15" height="15" fill="#e74c3c" opacity="0.3" />
                <text x="620" y="97" font-family="Arial" font-size="12" fill="#2c3e50">HLA Genes</text>
            </svg>
        </div>
    </div>

    <div class="section" id="bayesian">
        <h2>Simple Bayesian Genotyper</h2>
        
        <p>Bayesian estimation of the most likely genotype from next-generation DNA resequencing reads has already proven valuable. Our final example GATK tool is a simple Bayesian genotyper. The genotyper, though naïve, provides a framework for implementing more advanced genotyping and variant discovery approaches.</p>

        <h3>Bayesian Formulation</h3>
        <p>In brief, our example genotyper computes the posterior probability of each genotype, given the pileup of sequencer reads that cover the current locus, and expected heterozygosity of the sample. This computation is used to derive the prior probability each of the possible 10 diploid genotypes, using the Bayesian formulation:</p>

        <div class="formula">
            <p>$$P(G|D) = \frac{P(D|G) \cdot P(G)}{P(D)}$$</p>
            <p>where:</p>
            <ul>
                <li>D represents our data (the read base pileup at this reference base)</li>
                <li>G represents the given genotype</li>
                <li>P(G) is the prior probability of seeing this genotype</li>
                <li>P(D) is constant over all genotypes, and can be ignored</li>
            </ul>
        </div>

        <p>The likelihood P(D|G) is computed as:</p>
        
        <div class="formula">
            <p>$$P(D|G) = \prod_{b} P(b|G)$$</p>
            <p>where b represents each base covering the target locus. The probability of each base given the genotype is defined as:</p>
            <p>$$P(b|G = \{A_1, A_2\}) = \frac{1}{2} P(b|A_1) + \frac{1}{2} P(b|A_2)$$</p>
        </div>

        <p>The probability of seeing a base given an allele is:</p>
        
        <div class="formula">
            <p>$$P(b|A) = \begin{cases} 
                1 - \epsilon & \text{if } b = A \\
                \frac{\epsilon}{3} & \text{if } b \neq A
                \end{cases}$$</p>
            <p>and the epsilon term ε is the reversed phred scaled quality score at the base.</p>
        </div>

        <p>Finally, the assigned genotype at each site is the genotype with the greatest posterior probability, which is emitted to disk if its log-odds score exceeds a set threshold.</p>

        <div class="visualization">
            <h3>Simple Genotyper Code Sample</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Code structure -->
                <rect x="50" y="50" width="700" height="300" fill="#2c3e50" rx="10" />
                
                <!-- Code lines -->
                <text x="70" y="80" font-family="Courier New" font-size="14" fill="#ecf0f1">public class SimpleBayesianGenotyper extends LocusWalker {</text>
                <text x="70" y="100" font-family="Courier New" font-size="14" fill="#ecf0f1">    </text>
                <text x="70" y="120" font-family="Courier New" font-size="14" fill="#ecf0f1">    public Map<String, Object> map(RefMetaDataTracker tracker, </text>
                <text x="70" y="140" font-family="Courier New" font-size="14" fill="#ecf0f1">                                ReferenceContext ref, </text>
                <text x="70" y="160" font-family="Courier New" font-size="14" fill="#ecf0f1">                                AlignmentContext context) {</text>
                <text x="70" y="180" font-family="Courier New" font-size="14" fill="#ecf0f1">        </text>
                <text x="90" y="200" font-family="Courier New" font-size="14" fill="#ecf0f1"> // Get pileup of reads at this locus</text>
                <text x="90" y="220" font-family="Courier New" font-size="14" fill="#ecf0f1"> ReadBackedPileup pileup = context.getBasePileup();</text>
                <text x="70" y="240" font-family="Courier New" font-size="14" fill="#ecf0f1">        </text>
                <text x="90" y="260" font-family="Courier New" font-size="14" fill="#ecf0f1"> // Compute genotype likelihoods using Bayesian approach</text>
                <text x="90" y="280" font-family="Courier New" font-size="14" fill="#ecf0f1"> GenotypeLikelihoods likelihoods = computeLikelihoods(pileup);</text>
                <text x="70" y="300" font-family="Courier New" font-size="14" fill="#ecf0f1">        </text>
                <text x="90" y="320" font-family="Courier New" font-size="14" fill="#ecf0f1"> // Return likelihoods for all 10 possible genotypes</text>
                <text x="90" y="340" font-family="Courier New" font-size="14" fill="#ecf0f1"> return likelihoods.getGenotypeMap();</text>
                <text x="70" y="360" font-family="Courier New" font-size="14" fill="#ecf0f1">    }</text>
                <text x="70" y="380" font-family="Courier New" font-size="14" fill="#ecf0f1">}</text>
                
                <!-- Title -->
                <text x="400" y="30" font-family="Arial" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Simple Bayesian Genotyper Implementation</text>
                
                <!-- Highlight boxes -->
                <rect x="60" y="190" width="320" height="20" fill="#f39c12" opacity="0.3" />
                <rect x="60" y="250" width="380" height="20" fill="#f39c12" opacity="0.3" />
                <rect x="60" y="310" width="380" height="20" fill="#f39c12" opacity="0.3" />
            </svg>
        </div>
    </div>

    <div class="section" id="parallelization">
        <h2>Parallelization in GATK</h2>
        
        <p>The GATK provides multiple approaches for the parallelization of tasks:</p>
        
        <h3>Interval Processing</h3>
        <p>Users can split tasks by genomic locations (i.e., dividing up a job by chromosome) and farm out each interval to a GATK instance on a distributed computing system, like Sun Grid Engine or LSF.</p>
        
        <h3>Shared Memory Parallelization</h3>
        <p>The GATK also supports an automatic shared-memory parallelization, where the GATK manages multiple instances of the traversal engine and the given walker on a single machine. Walkers that wish to use this shared memory parallelization implement the TreeReducible interface, which enables the GATK to merge together two reduce results.</p>

        <div class="visualization">
            <h3>Shared Memory Parallel Tree-Reduction in GATK</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Title -->
                <text x="400" y="30" font-family="Arial" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">Shared Memory Parallel Tree-Reduction in GATK</text>
                
                <!-- Threads -->
                <rect x="100" y="70" width="150" height="80" fill="#3498db" rx="10" />
                <text x="175" y="100" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Thread 1</text>
                <text x="175" y="120" font-family="Arial" font-size="12" fill="white" text-anchor="middle">MapReduce</text>
                <text x="175" y="135" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Calls</text>
                
                <rect x="325" y="70" width="150" height="80" fill="#3498db" rx="10" />
                <text x="400" y="100" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Thread 2</text>
                <text x="400" y="120" font-family="Arial" font-size="12" fill="white" text-anchor="middle">MapReduce</text>
                <text x="400" y="135" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Calls</text>
                
                <rect x="550" y="70" width="150" height="80" fill="#3498db" rx="10" />
                <text x="625" y="100" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Thread N</text>
                <text x="625" y="120" font-family="Arial" font-size="12" fill="white" text-anchor="middle">MapReduce</text>
                <text x="625" y="135" font-family="Arial" font-size="12" fill="white" text-anchor="middle">Calls</text>
                
                <!-- Dots for additional threads -->
                <circle cx="450" cy="110" r="3" fill="#2c3e50" />
                <circle cx="470" cy="110" r="3" fill="#2c3e50" />
                <circle cx="490" cy="110" r="3" fill="#2c3e50" />
                
                <!-- Tree Reduction -->
                <rect x="212" y="180" width="150" height="60" fill="#e74c3c" rx="10" />
                <text x="287" y="205" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Tree</text>
                <text x="287" y="220" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Reduce</text>
                
                <rect x="438" y="180" width="150" height="60" fill="#e74c3c" rx="10" />
                <text x="513" y="205" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Tree</text>
                <text x="513" y="220" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Reduce</text>
                
                <!-- Second Level Reduction -->
                <rect x="325" y="270" width="150" height="60" fill="#f39c12" rx="10" />
                <text x="400" y="295" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Final</text>
                <text x="400" y="310" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Reduction</text>
                
                <!-- Arrows from threads to first level reduction -->
                <line x1="175" y1="150" x2="287" y2="180" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                <line x1="400" y1="150" x2="287" y2="180" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                <line x1="400" y1="150" x2="513" y2="180" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                <line x1="625" y1="150" x2="513" y2="180" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                
                <!-- Arrows from first level to second level -->
                <line x1="287" y1="240" x2="400" y2="270" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                <line x1="513" y1="240" x2="400" y2="270" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead2)" />
                
                <!-- Arrowhead Definition -->
                <defs>
                    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                
                <!-- Labels -->
                <text x="70" y="110" font-family="Arial" font-size="12" fill="#7f8c8d">Multiple</text>
                <text x="70" y="125" font-family="Arial" font-size="12" fill="#7f8c8d">Processing</text>
                <text x="70" y="140" font-family="Arial" font-size="12" fill="#7f8c8d">Threads</text>
                
                <text x="150" y="210" font-family="Arial" font-size="12" fill="#7f8c8d">Parallel</text>
                <text x="150" y="225" font-family="Arial" font-size="12" fill="#7f8c8d">Reduction</text>
                <text x="600" y="210" font-family="Arial" font-size="12" fill="#7f8c8d">Sequential</text>
                <text x="600" y="225" font-family="Arial" font-size="12" fill="#7f8c8d">Output</text>
            </svg>
        </div>

        <h3>Performance Results</h3>
        <p>The algorithm was implemented in the GATK as a locus based walker, in 57 lines of Java code. Along with implementing the locus walker strategy, it also implements the Tree-Reducible interface, which allows the GATK to parallelize the MapReduce calls across processors.</p>
        
        <p>We applied the genotyping algorithm to Pilot 2 deep coverage data for the CEU daughter, sample NA12878, on chromosome 1 of the 1000 Genomes Project data using Illumina sequencing technology. On a single processor, this calculation requires 863 min to process the 247,249,719 million loci of chromosome 1.</p>
        
        <p>Moreover, the GATK's built-in support for shared memory parallelization allows us to quickly add CPU resources to reduce the run-time of target analyses. The elapsed time to genotype NA12878's chromosome 1 drops nearly exponentially through the addition of only 11 additional processing nodes, with no change to the analysis code.</p>

        <div class="visualization">
            <h3>Parallelization of Genotyping in GATK</h3>
            <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background -->
                <rect width="100%" height="100%" fill="#f8f9fa" />
                
                <!-- Axes -->
                <line x1="80" y1="300" x2="750" y2="300" stroke="#2c3e50" stroke-width="2" />
                <line x1="80" y1="300" x2="80" y2="50" stroke="#2c3e50" stroke-width="2" />
                
                <!-- Axis labels -->
                <text x="400" y="340" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle">Number of Processors</text>
                <text x="20" y="175" font-family="Arial" font-size="14" fill="#2c3e50" text-anchor="middle" transform="rotate(-90, 20, 175)">Elapsed Time (minutes)</text>
                
                <!-- Scale labels -->
                <text x="80" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">1</text>
                <text x="180" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">5</text>
                <text x="280" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">10</text>
                <text x="380" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">15</text>
                <text x="480" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">20</text>
                <text x="580" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">25</text>
                <text x="680" y="320" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">30</text>
                
                <text x="60" y="300" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">0</text>
                <text x="60" y="250" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">200</text>
                <text x="60" y="200" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">400</text>
                <text x="60" y="150" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">600</text>
                <text x="60" y="100" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">800</text>
                <text x="60" y="50" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="end">1000</text>
                
                <!-- Exponential decay curve (simulated data) -->
                <path d="M80,300 
                         C150,250 200,200 280,150
                         C350,120 450,100 550,90
                         C600,85 700,80 750,80" 
                      fill="none" stroke="#3498db" stroke-width="3" />
                
                <!-- Data points -->
                <circle cx="80" cy="300" r="5" fill="#e74c3c" />
                <circle cx="180" cy="220" r="5" fill="#e74c3c" />
                <circle cx="280" cy="150" r="5" fill="#e74c3c" />
                <circle cx="380" cy="120" r="5" fill="#e74c3c" />
                <circle cx="480" cy="100" r="5" fill="#e74c3c" />
                <circle cx="580" cy="90" r="5" fill="#e74c3c" />
                <circle cx="680" cy="85" r="5" fill="#e74c3c" />
                
                <!-- Labels -->
                <text x="400" y="30" font-family="Arial" font-size="16" fill="#2c3e50" text-anchor="middle" font-weight="bold">GATK Genotyping Parallelization Performance</text>
                <text x="400" y="360" font-family="Arial" font-size="12" fill="#7f8c8d" text-anchor="middle">Near-exponential decrease in elapsed time with increased processors</text>
                
                <!-- Legend -->
                <rect x="600" y="60" width="15" height="15" fill="#3498db" />
                <text x="620" y="72" font-family="Arial" font-size="12" fill="#2c3e50">Performance Curve</text>
                
                <circle cx="607" cy="97" r="5" fill="#e74c3c" />
                <text x="620" y="102" font-family="Arial" font-size="12" fill="#2c3e50">Data Points</text>
            </svg>
        </div>
    </div>

    <div class="section" id="results">
        <h2>Results and Applications</h2>
        
        <p>Even this naïve genotyper performs reasonably well at identifying variants—315,202 variants were called on chromosome 1, with 81.70% in dbSNP and a concordance figure of 99.76%. This compares well against previous single individual genotyping efforts.</p>
        
        <p>For HapMap sites the overall concordance figure is 99.84%, with 99.81% of homozygous variants correctly called. Given that the error rate in HapMap is 0.5%, these values are remarkably good.</p>
        
        <h3>Real-world Applications</h3>
        <p>Despite less than 1 yr of development, the GATK already underlies several critical tools in both the 1000 Genomes Project and The Cancer Genome Atlas, including:</p>
        <ul>
            <li>Quality-score recalibration</li>
            <li>Multiple-sequence realignment</li>
            <li>HLA typing</li>
            <li>Multiple-sample SNP genotyping</li>
            <li>Indel discovery and genotyping</li>
        </ul>
        
        <p>The GATK's robustness and efficiency has enabled these tools to be easily and rapidly deployed in recent projects to routinely process terabases of Illumina, SOLiD, and 454 data, as well processing hundreds of lanes each week in the production resequencing facilities at the Broad Institute.</p>
    </div>

    <div class="section" id="future">
        <h2>Future Directions</h2>
        <p>In the near future, we intend to expand the GATK to support additional data access patterns to enable the implementation of:</p>
        <ul>
            <li>Local reference-guided assembly</li>
            <li>Copy-number variation detection</li>
            <li>Inversions</li>
            <li>General structural variation algorithms</li>
        </ul>
    </div>

    <div class="footer">
        <p>Based on "The Genome Analysis Toolkit: A MapReduce framework for analyzing next-generation DNA sequencing data" by Aaron McKenna et al.</p>
        <p>Program in Medical and Population Genetics, The Broad Institute of Harvard and MIT</p>
    </div>
</body>
</html>