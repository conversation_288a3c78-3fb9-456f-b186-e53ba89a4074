<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsHap: Fast and Accurate Read-Based Phasing Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .toc li:last-child {
            border-bottom: none;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }
        
        .toc a:hover {
            color: #007bff;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .algorithm-box {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #0d47a1;
            margin-bottom: 15px;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .read-fragment {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .snp-position {
            fill: #e74c3c;
            stroke: #c0392b;
            stroke-width: 1;
        }
        
        .haplotype-line {
            stroke: #27ae60;
            stroke-width: 3;
            fill: none;
        }
        
        .bipartition-line {
            stroke: #f39c12;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WhatsHap: Fast and Accurate Read-Based Phasing</h1>
            <p>A Comprehensive Tutorial on Production-Ready Haplotype Assembly</p>
        </div>
        
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction and Background</a></li>
                <li><a href="#wmec-problem">2. The wMEC Problem Formulation</a></li>
                <li><a href="#core-algorithm">3. Core Algorithm and Dynamic Programming</a></li>
                <li><a href="#allele-detection">4. Allele Detection Strategies</a></li>
                <li><a href="#read-selection">5. Read Selection and Coverage Management</a></li>
                <li><a href="#pedigree-integration">6. Pedigree Integration (PedMEC)</a></li>
                <li><a href="#implementation">7. Implementation and Software Engineering</a></li>
                <li><a href="#performance">8. Performance Analysis and Results</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction and Background -->
        <div id="introduction" class="section">
            <h2>1. Introduction and Background</h2>

            <div class="highlight">
                <strong>Production-Ready Innovation:</strong> WhatsHap is the first production-ready tool for highly accurate read-based phasing, designed from the beginning to leverage third-generation sequencing technologies while maintaining ease of use and standards compliance.
            </div>

            <h3>The Phasing Problem</h3>

            <div class="math-box">
                <h4>From Genotypes to Haplotypes</h4>
                <p><strong>Genotype:</strong> Set of alleles present at a variant locus (e.g., G/A for heterozygous)</p>
                <p><strong>Phase Ambiguity:</strong> G/A and A/G are equivalent in genotype representation</p>
                <p><strong>Haplotype:</strong> Full sequence of nucleotides in an individual chromosome</p>

                <p><strong>Phasing Goal:</strong> Determine the correct relationship (cis or trans) between alleles at multiple variant loci</p>

                <p><strong>Example:</strong></p>
                <ul>
                    <li><strong>Input:</strong> Two heterozygous variants G/A and C/T</li>
                    <li><strong>Output:</strong> Haplotypes G–T and A–C (or G–C and A–T)</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Four Approaches to Haplotype Phasing
                    </text>

                    <!-- Molecular Phasing -->
                    <g transform="translate(50, 60)">
                        <rect width="150" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="75" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Molecular</text>
                        <text x="75" y="40" text-anchor="middle" font-size="11" fill="#2c3e50">Direct measurement</text>
                        <text x="75" y="55" text-anchor="middle" font-size="11" fill="#2c3e50">Physical separation</text>

                        <!-- Chromosome separation illustration -->
                        <g transform="translate(25, 70)">
                            <line x1="0" y1="0" x2="100" y2="0" stroke="#34495e" stroke-width="3"/>
                            <line x1="0" y1="15" x2="100" y2="15" stroke="#34495e" stroke-width="3"/>
                            <circle cx="20" cy="0" r="3" fill="#e74c3c"/>
                            <circle cx="50" cy="0" r="3" fill="#e74c3c"/>
                            <circle cx="80" cy="0" r="3" fill="#e74c3c"/>
                            <circle cx="20" cy="15" r="3" fill="#3498db"/>
                            <circle cx="50" cy="15" r="3" fill="#3498db"/>
                            <circle cx="80" cy="15" r="3" fill="#3498db"/>
                        </g>

                        <text x="75" y="110" text-anchor="middle" font-size="10" fill="#666">High confidence, limited scale</text>
                    </g>

                    <!-- Genetic Phasing -->
                    <g transform="translate(250, 60)">
                        <rect width="150" height="120" fill="#f39c12" opacity="0.1" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="75" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#f39c12">Genetic</text>
                        <text x="75" y="40" text-anchor="middle" font-size="11" fill="#2c3e50">Family relationships</text>
                        <text x="75" y="55" text-anchor="middle" font-size="11" fill="#2c3e50">Mendelian inheritance</text>

                        <!-- Family trio illustration -->
                        <g transform="translate(25, 70)">
                            <!-- Parents -->
                            <circle cx="25" cy="0" r="8" fill="#3498db" opacity="0.7"/>
                            <text x="25" y="5" text-anchor="middle" font-size="8" fill="white">M</text>
                            <circle cx="75" cy="0" r="8" fill="#e74c3c" opacity="0.7"/>
                            <text x="75" y="5" text-anchor="middle" font-size="8" fill="white">F</text>

                            <!-- Child -->
                            <circle cx="50" cy="25" r="8" fill="#27ae60" opacity="0.7"/>
                            <text x="50" y="30" text-anchor="middle" font-size="8" fill="white">C</text>

                            <!-- Inheritance lines -->
                            <line x1="25" y1="8" x2="45" y2="20" stroke="#666" stroke-width="1"/>
                            <line x1="75" y1="8" x2="55" y2="20" stroke="#666" stroke-width="1"/>
                        </g>

                        <text x="75" y="110" text-anchor="middle" font-size="10" fill="#666">Chromosome-length, needs families</text>
                    </g>

                    <!-- Population-Based Phasing -->
                    <g transform="translate(450, 60)">
                        <rect width="150" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="75" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Population</text>
                        <text x="75" y="40" text-anchor="middle" font-size="11" fill="#2c3e50">Statistical inference</text>
                        <text x="75" y="55" text-anchor="middle" font-size="11" fill="#2c3e50">Shared ancestry</text>

                        <!-- Population illustration -->
                        <g transform="translate(25, 70)">
                            <circle cx="15" cy="5" r="4" fill="#3498db" opacity="0.7"/>
                            <circle cx="35" cy="5" r="4" fill="#3498db" opacity="0.7"/>
                            <circle cx="55" cy="5" r="4" fill="#3498db" opacity="0.7"/>
                            <circle cx="75" cy="5" r="4" fill="#3498db" opacity="0.7"/>
                            <circle cx="85" cy="5" r="4" fill="#3498db" opacity="0.7"/>

                            <circle cx="25" cy="20" r="4" fill="#e74c3c" opacity="0.7"/>
                            <circle cx="45" cy="20" r="4" fill="#e74c3c" opacity="0.7"/>
                            <circle cx="65" cy="20" r="4" fill="#e74c3c" opacity="0.7"/>

                            <text x="50" y="35" text-anchor="middle" font-size="9" fill="#666">Large cohorts</text>
                        </g>

                        <text x="75" y="110" text-anchor="middle" font-size="10" fill="#666">Common variants only</text>
                    </g>

                    <!-- Read-Based Phasing -->
                    <g transform="translate(650, 60)">
                        <rect width="150" height="120" fill="#9b59b6" opacity="0.1" stroke="#9b59b6" stroke-width="2" rx="10"/>
                        <text x="75" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#9b59b6">Read-Based</text>
                        <text x="75" y="40" text-anchor="middle" font-size="11" fill="#2c3e50">Sequencing reads</text>
                        <text x="75" y="55" text-anchor="middle" font-size="11" fill="#2c3e50">Haplotype assembly</text>

                        <!-- Read coverage illustration -->
                        <g transform="translate(25, 70)">
                            <!-- Chromosome -->
                            <line x1="0" y1="15" x2="100" y2="15" stroke="#34495e" stroke-width="3"/>
                            <circle cx="20" cy="15" r="2" fill="#e74c3c"/>
                            <circle cx="40" cy="15" r="2" fill="#e74c3c"/>
                            <circle cx="60" cy="15" r="2" fill="#e74c3c"/>
                            <circle cx="80" cy="15" r="2" fill="#e74c3c"/>

                            <!-- Reads -->
                            <rect x="10" y="5" width="35" height="4" fill="#3498db" opacity="0.7"/>
                            <rect x="30" y="0" width="40" height="4" fill="#3498db" opacity="0.7"/>
                            <rect x="50" y="25" width="35" height="4" fill="#3498db" opacity="0.7"/>
                        </g>

                        <text x="75" y="110" text-anchor="middle" font-size="10" fill="#666">Read length limited</text>
                    </g>

                    <!-- WhatsHap integration -->
                    <g transform="translate(200, 220)">
                        <rect width="400" height="180" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="3" rx="15"/>
                        <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">
                            WhatsHap: Integrated Approach
                        </text>

                        <!-- Read-based core -->
                        <g transform="translate(50, 50)">
                            <rect width="120" height="60" fill="#9b59b6" opacity="0.2" stroke="#9b59b6" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#9b59b6">Read-Based Core</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">wMEC algorithm</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Long read optimized</text>
                        </g>

                        <!-- Plus sign -->
                        <text x="200" y="85" text-anchor="middle" font-size="24" font-weight="bold" fill="#2c3e50">+</text>

                        <!-- Pedigree integration -->
                        <g transform="translate(230, 50)">
                            <rect width="120" height="60" fill="#f39c12" opacity="0.2" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Pedigree Info</text>
                            <text x="60" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">PedMEC extension</text>
                            <text x="60" y="50" text-anchor="middle" font-size="10" fill="#2c3e50">Family constraints</text>
                        </g>

                        <!-- Key advantages -->
                        <g transform="translate(50, 130)">
                            <text x="150" y="15" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Advantages</text>
                            <text x="75" y="35" text-anchor="middle" font-size="11" fill="#27ae60">✓ Production ready</text>
                            <text x="225" y="35" text-anchor="middle" font-size="11" fill="#27ae60">✓ Standard formats</text>
                            <text x="75" y="50" text-anchor="middle" font-size="11" fill="#27ae60">✓ Long read optimized</text>
                            <text x="225" y="50" text-anchor="middle" font-size="11" fill="#27ae60">✓ Indel support</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Clinical and Research Applications</h3>

            <div class="info-box">
                <h4>Why Phasing Matters</h4>
                <ul class="step-list">
                    <li><strong>Population Genetics:</strong> Understanding population structure, migration patterns, and environmental pressures</li>
                    <li><strong>Clinical Decision Making:</strong> Crucial for medical diagnosis and treatment planning</li>
                    <li><strong>Compound Heterozygosity:</strong> Determining if two variants are in cis (same chromosome) or trans (different chromosomes)</li>
                    <li><strong>Allele-Specific Expression:</strong> Understanding how different alleles contribute to gene expression</li>
                </ul>
            </div>

            <h3>WhatsHap's Unique Position</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>WhatsHap</th>
                        <th>GATK ReadBackedPhasing</th>
                        <th>HapCUT</th>
                        <th>phASER</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Standard VCF I/O</strong></td>
                        <td>✓ Full support</td>
                        <td>✓ Limited</td>
                        <td>✗ Custom format</td>
                        <td>✓ Yes</td>
                    </tr>
                    <tr>
                        <td><strong>Long Read Optimized</strong></td>
                        <td>✓ Designed for</td>
                        <td>✗ Short reads</td>
                        <td>✓ Supported</td>
                        <td>✗ RNA-seq focus</td>
                    </tr>
                    <tr>
                        <td><strong>Indel Phasing</strong></td>
                        <td>✓ Full support</td>
                        <td>✗ SNVs only</td>
                        <td>✓ Yes</td>
                        <td>✓ Yes</td>
                    </tr>
                    <tr>
                        <td><strong>Pedigree Integration</strong></td>
                        <td>✓ Unique feature</td>
                        <td>✗ No</td>
                        <td>✗ No</td>
                        <td>✗ No</td>
                    </tr>
                    <tr>
                        <td><strong>Maintenance Status</strong></td>
                        <td>✓ Active</td>
                        <td>✗ Discontinued</td>
                        <td>✓ Open source</td>
                        <td>✓ Active</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning-box">
                <h4>Third-Generation Sequencing Challenges</h4>
                <p>Long-read technologies (PacBio, Oxford Nanopore) present unique challenges:</p>
                <ul>
                    <li><strong>High Error Rates:</strong> 10%+ error rates require robust error correction</li>
                    <li><strong>Indel Errors:</strong> Alignment artifacts at variant sites</li>
                    <li><strong>Reference Bias:</strong> Aligners favor reference sequence over alternative alleles</li>
                    <li><strong>Computational Complexity:</strong> Longer reads increase algorithm complexity</li>
                </ul>
                <p><strong>WhatsHap's Solution:</strong> Fixed-parameter tractable algorithm with intelligent re-alignment strategy</p>
            </div>
        </div>

        <!-- Section 2: The wMEC Problem Formulation -->
        <div id="wmec-problem" class="section">
            <h2>2. The wMEC Problem Formulation</h2>

            <div class="highlight">
                <strong>Mathematical Foundation:</strong> WhatsHap solves the weighted Minimum Error Correction (wMEC) problem, which formalizes haplotype assembly as finding the minimum-cost partition of reads into two conflict-free haplotypes.
            </div>

            <h3>Allele Matrix Representation</h3>

            <div class="math-box">
                <h4>Matrix Structure</h4>
                <p><strong>Allele Matrix:</strong> $M \in \{0, 1, -\}^{R \times V}$ where:</p>
                <ul>
                    <li>$R$ = number of reads</li>
                    <li>$V$ = number of heterozygous variants</li>
                    <li>$M_{i,j} = 0$ if read $i$ supports reference allele at variant $j$</li>
                    <li>$M_{i,j} = 1$ if read $i$ supports alternative allele at variant $j$</li>
                    <li>$M_{i,j} = -$ if read $i$ does not cover variant $j$</li>
                </ul>

                <p><strong>Paired-End Reads:</strong> Represented as two contiguous stretches of 0/1 values separated by "-" entries at the unsequenced internal segment</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Weighted MEC Problem: From Conflicting Reads to Optimal Partition
                    </text>

                    <!-- Input: Allele Matrix -->
                    <g transform="translate(50, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">
                            Input: Allele Matrix with Conflicts
                        </text>

                        <!-- Matrix headers -->
                        <g transform="translate(50, 40)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">Read</text>
                            <text x="40" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V1</text>
                            <text x="70" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V2</text>
                            <text x="100" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V3</text>
                            <text x="130" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V4</text>
                            <text x="160" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V5</text>
                            <text x="190" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">Weight</text>

                            <!-- Read 1 (paired-end) -->
                            <text x="0" y="35" font-size="10" fill="#2c3e50">R1</text>
                            <rect x="30" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="40" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="60" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="90" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="120" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="130" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="150" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <text x="190" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">30</text>

                            <!-- Read 2 -->
                            <text x="0" y="55" font-size="10" fill="#2c3e50">R2</text>
                            <rect x="30" y="45" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="40" y="55" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="60" y="45" width="20" height="15" fill="#e74c3c" stroke="#c0392b"/>
                            <text x="70" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="90" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="100" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="120" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="130" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="150" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="190" y="55" text-anchor="middle" font-size="10" fill="#2c3e50">25</text>

                            <!-- Read 3 -->
                            <text x="0" y="75" font-size="10" fill="#2c3e50">R3</text>
                            <rect x="30" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="40" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="60" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="70" y="75" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="90" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="100" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="120" y="65" width="20" height="15" fill="#e74c3c" stroke="#c0392b"/>
                            <text x="130" y="75" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="150" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <text x="190" y="75" text-anchor="middle" font-size="10" fill="#2c3e50">20</text>

                            <!-- Read 4 -->
                            <text x="0" y="95" font-size="10" fill="#2c3e50">R4</text>
                            <rect x="30" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="40" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="60" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="70" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="90" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="120" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="130" y="95" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="150" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="190" y="95" text-anchor="middle" font-size="10" fill="#2c3e50">35</text>
                        </g>

                        <!-- Conflict indicators -->
                        <g transform="translate(50, 150)">
                            <text x="100" y="15" text-anchor="middle" font-size="12" fill="#e74c3c">Conflicts Detected!</text>
                            <text x="100" y="30" text-anchor="middle" font-size="10" fill="#666">R1 vs R2 at V2: 1 vs 0</text>
                            <text x="100" y="45" text-anchor="middle" font-size="10" fill="#666">R2 vs R3 at V4: 0 vs 1</text>
                        </g>
                    </g>

                    <!-- Arrow -->
                    <g transform="translate(350, 250)">
                        <defs>
                            <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <text x="40" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">wMEC</text>
                    </g>

                    <!-- Output: Optimal Partition -->
                    <g transform="translate(500, 60)">
                        <text x="150" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">
                            Output: Optimal Bipartition
                        </text>

                        <!-- Corrected matrix -->
                        <g transform="translate(50, 40)">
                            <text x="0" y="15" font-size="10" fill="#2c3e50">Read</text>
                            <text x="40" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V1</text>
                            <text x="70" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V2</text>
                            <text x="100" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V3</text>
                            <text x="130" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V4</text>
                            <text x="160" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">V5</text>
                            <text x="190" y="15" text-anchor="middle" font-size="10" fill="#2c3e50">Hap</text>

                            <!-- Read 1 (Haplotype 0) -->
                            <text x="0" y="35" font-size="10" fill="#3498db">R1</text>
                            <rect x="30" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="40" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="60" y="25" width="20" height="15" fill="#27ae60" stroke="#229954"/>
                            <text x="70" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="90" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="100" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="120" y="25" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="130" y="35" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="150" y="25" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="35" text-anchor="middle" font-size="10" fill="white">0</text>
                            <text x="190" y="35" text-anchor="middle" font-size="10" fill="#3498db">H0</text>

                            <!-- Read 2 (Haplotype 1) -->
                            <text x="0" y="55" font-size="10" fill="#e74c3c">R2</text>
                            <rect x="30" y="45" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="40" y="55" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="60" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="70" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="90" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="100" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="120" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="130" y="55" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="150" y="45" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="55" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="190" y="55" text-anchor="middle" font-size="10" fill="#e74c3c">H1</text>

                            <!-- Read 3 (Haplotype 0) -->
                            <text x="0" y="75" font-size="10" fill="#3498db">R3</text>
                            <rect x="30" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="40" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="60" y="65" width="20" height="15" fill="#27ae60" stroke="#229954"/>
                            <text x="70" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="90" y="65" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="100" y="75" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="120" y="65" width="20" height="15" fill="#27ae60" stroke="#229954"/>
                            <text x="130" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="150" y="65" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="75" text-anchor="middle" font-size="10" fill="white">0</text>
                            <text x="190" y="75" text-anchor="middle" font-size="10" fill="#3498db">H0</text>

                            <!-- Read 4 (Haplotype 1) -->
                            <text x="0" y="95" font-size="10" fill="#e74c3c">R4</text>
                            <rect x="30" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="40" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="60" y="85" width="20" height="15" fill="#95a5a6" stroke="#7f8c8d"/>
                            <text x="70" y="95" text-anchor="middle" font-size="10" fill="white">-</text>
                            <rect x="90" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="100" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <rect x="120" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="130" y="95" text-anchor="middle" font-size="10" fill="white">0</text>
                            <rect x="150" y="85" width="20" height="15" fill="#3498db" stroke="#2980b9"/>
                            <text x="160" y="95" text-anchor="middle" font-size="10" fill="white">1</text>
                            <text x="190" y="95" text-anchor="middle" font-size="10" fill="#e74c3c">H1</text>
                        </g>

                        <!-- Solution summary -->
                        <g transform="translate(50, 150)">
                            <text x="100" y="15" text-anchor="middle" font-size="12" fill="#27ae60">Optimal Solution Found!</text>
                            <text x="100" y="30" text-anchor="middle" font-size="10" fill="#666">Corrections: R1-V2, R3-V4</text>
                            <text x="100" y="45" text-anchor="middle" font-size="10" fill="#666">Total Cost: 25 + 20 = 45</text>
                        </g>
                    </g>

                    <!-- Resulting haplotypes -->
                    <g transform="translate(200, 350)">
                        <rect width="400" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="200" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Reconstructed Haplotypes
                        </text>

                        <!-- Haplotype 0 -->
                        <g transform="translate(50, 50)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#3498db">Haplotype 0:</text>
                            <text x="100" y="15" font-size="12" fill="#2c3e50">0 - 0 - ? - 0 - 0</text>
                            <text x="250" y="15" font-size="10" fill="#666">(Reads: R1, R3)</text>
                        </g>

                        <!-- Haplotype 1 -->
                        <g transform="translate(50, 80)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#e74c3c">Haplotype 1:</text>
                            <text x="100" y="15" font-size="12" fill="#2c3e50">? - 0 - 1 - 0 - 1</text>
                            <text x="250" y="15" font-size="10" fill="#666">(Reads: R2, R4)</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Problem Definition</h3>

            <div class="math-box">
                <h4>Weighted MEC (wMEC) Problem</h4>
                <p><strong>Input:</strong></p>
                <ul>
                    <li>Allele matrix $M \in \{0, 1, -\}^{R \times V}$</li>
                    <li>Weight matrix $W \in \mathbb{R}_+^{R \times V}$ (phred-scaled quality scores)</li>
                </ul>

                <p><strong>Goal:</strong> Find minimum-cost corrections to make matrix feasible</p>

                <p><strong>Feasible Matrix:</strong> Rows can be partitioned into two conflict-free sets</p>

                <p><strong>Objective Function:</strong></p>
                $$\text{minimize} \sum_{(i,j) \in \text{corrections}} W_{i,j}$$

                <p><strong>Constraint:</strong> Corrected matrix admits a bipartition where all reads in each partition are consistent</p>
            </div>

            <h3>Quality Score Integration</h3>

            <div class="info-box">
                <h4>Phred-Scaled Weights</h4>
                <p><strong>Quality Score Interpretation:</strong></p>
                <ul>
                    <li>Phred score $Q$ corresponds to error probability $p = 10^{-Q/10}$</li>
                    <li>Higher quality scores → higher cost to flip</li>
                    <li>Lower quality scores → lower cost to flip</li>
                </ul>

                <p><strong>Examples:</strong></p>
                <ul>
                    <li><strong>Q=30:</strong> $p = 0.001$ (99.9% confidence) → high flip cost</li>
                    <li><strong>Q=10:</strong> $p = 0.1$ (90% confidence) → low flip cost</li>
                </ul>

                <p><strong>Statistical Interpretation:</strong> wMEC finds maximum likelihood solution under sequencing error model</p>
            </div>
        </div>

        <!-- Section 3: Core Algorithm and Dynamic Programming -->
        <div id="core-algorithm" class="section">
            <h2>3. Core Algorithm and Dynamic Programming</h2>

            <div class="highlight">
                <strong>Fixed-Parameter Tractability:</strong> WhatsHap implements an exact, fixed-parameter tractable (FPT) algorithm where read coverage is the only fixed parameter, making it uniquely suited for long-read data.
            </div>

            <h3>Dynamic Programming Framework</h3>

            <div class="math-box">
                <h4>Algorithm Structure</h4>
                <p><strong>Processing Order:</strong> Variants processed from 5' to 3' end of chromosome</p>
                <p><strong>State Space:</strong> At each variant position $v$, consider all $2^k$ bipartitions of $k$ covering reads</p>
                <p><strong>Gray Code Enumeration:</strong> Bipartitions enumerated in Gray code order for constant-time cost updates</p>

                <p><strong>Runtime Complexity:</strong></p>
                $$O(V \cdot 2^c \cdot c)$$

                <p>Where:</p>
                <ul>
                    <li>$V$ = number of variants (linear scaling!)</li>
                    <li>$c$ = maximum read coverage</li>
                    <li>Independent of read length</li>
                </ul>
            </div>

            <div class="algorithm-box">
                <h4>Key Algorithmic Properties</h4>
                <ul class="step-list">
                    <li><strong>Exact Solution:</strong> Guarantees optimal solution to wMEC problem</li>
                    <li><strong>Linear in Variants:</strong> Scales well with genome size</li>
                    <li><strong>Read Length Independent:</strong> Perfect for long-read technologies</li>
                    <li><strong>Coverage Dependent:</strong> Exponential in coverage requires pruning</li>
                </ul>
            </div>
        </div>

        <!-- Section 4: Allele Detection Strategies -->
        <div id="allele-detection" class="section">
            <h2>4. Allele Detection Strategies</h2>

            <div class="highlight">
                <strong>Re-alignment Innovation:</strong> WhatsHap's re-alignment strategy dramatically improves accuracy by correcting reference bias in alignments, especially crucial for noisy long-read data.
            </div>

            <h3>The Reference Bias Problem</h3>

            <div class="warning-box">
                <h4>Alignment Artifacts in Long Reads</h4>
                <p><strong>Problem:</strong> Standard aligners are biased toward reference sequence</p>

                <p><strong>Example Scenario:</strong></p>
                <div class="code-block">
Reference: TCGTGT (with G/A variant at position 3)
Read:      TCAGT
                </div>

                <p><strong>Standard Alignment:</strong></p>
                <div class="code-block">
Read: TC-AGT
Ref:  TCGTGT
Result: "Unknown" allele (gap placement ambiguity)
                </div>

                <p><strong>Re-alignment to Alternative:</strong></p>
                <div class="code-block">
Read: TCA-GT
Alt:  TCATGT
Result: "Alternative" allele (better alignment score)
                </div>
            </div>

            <h3>WhatsHap's Re-alignment Strategy</h3>

            <div class="algorithm-box">
                <h4>Two-Step Allele Detection</h4>
                <ul class="step-list">
                    <li><strong>Extract Window:</strong> Extract read sequence ±10bp around variant</li>
                    <li><strong>Dual Alignment:</strong> Align to both reference and alternative sequences</li>
                    <li><strong>Score Comparison:</strong> Choose allele with higher alignment score</li>
                    <li><strong>Tie Handling:</strong> Equal scores result in "Unknown" assignment</li>
                </ul>
            </div>

            <h3>Performance Impact</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Coverage</th>
                        <th>Without Re-alignment</th>
                        <th>With Re-alignment</th>
                        <th>Improvement</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>10× (Simulated)</strong></td>
                        <td>1.1% switch error</td>
                        <td>0.1% switch error</td>
                        <td>11× better</td>
                    </tr>
                    <tr>
                        <td><strong>10× (Real)</strong></td>
                        <td>1.8% switch error</td>
                        <td>0.3% switch error</td>
                        <td>6× better</td>
                    </tr>
                    <tr>
                        <td><strong>Full Coverage</strong></td>
                        <td>0.48% switch error</td>
                        <td>0.24% switch error</td>
                        <td>2× better</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 5: Read Selection and Coverage Management -->
        <div id="read-selection" class="section">
            <h2>5. Read Selection and Coverage Management</h2>

            <div class="highlight">
                <strong>Intelligent Pruning:</strong> WhatsHap uses a sophisticated read selection heuristic to maintain tractable coverage while maximizing phasing information.
            </div>

            <h3>Read Scoring System</h3>

            <div class="math-box">
                <h4>Per-Read Score Calculation</h4>
                <p><strong>Score Components:</strong></p>
                <ul>
                    <li><strong>Variant Coverage:</strong> Number of heterozygous variants covered</li>
                    <li><strong>Hole Penalty:</strong> Number of "-" entries between first and last variant</li>
                    <li><strong>Quality Bonus:</strong> Mapping and base-calling quality scores</li>
                </ul>

                <p><strong>Scoring Formula:</strong></p>
                $$\text{Score}(r) = \text{variants}(r) - \alpha \cdot \text{holes}(r) + \beta \cdot \text{quality}(r)$$

                <p><strong>Bridging Reads:</strong> Special consideration for reads that connect otherwise isolated variant islands</p>
            </div>

            <div class="info-box">
                <h4>Selection Strategy</h4>
                <ul class="step-list">
                    <li><strong>Greedy Selection:</strong> Choose highest-scoring reads first</li>
                    <li><strong>Coverage Limit:</strong> Default maximum of 15× coverage</li>
                    <li><strong>Bridge Addition:</strong> Add low-scoring reads that connect components</li>
                    <li><strong>Technology Mixing:</strong> Supports combining PacBio, Illumina, 10X data</li>
                </ul>
            </div>
        </div>

        <!-- Section 6: Pedigree Integration (PedMEC) -->
        <div id="pedigree-integration" class="section">
            <h2>6. Pedigree Integration (PedMEC)</h2>

            <div class="highlight">
                <strong>Unique Capability:</strong> WhatsHap is the only tool that combines read-based and genetic phasing, dramatically improving accuracy and enabling low-coverage sequencing.
            </div>

            <h3>PedMEC Extension</h3>

            <div class="math-box">
                <h4>Mathematical Framework</h4>
                <p><strong>Extended Objective:</strong> Minimize error corrections while respecting Mendelian inheritance and minimizing recombination cost</p>

                <p><strong>Objective Function:</strong></p>
                $$\text{minimize} \sum \text{error costs} + \lambda \sum \text{recombination costs}$$

                <p><strong>Constraints:</strong></p>
                <ul>
                    <li>Child haplotypes are recombinations of parental haplotypes</li>
                    <li>Mendelian inheritance rules enforced</li>
                    <li>Recombination events penalized by genetic distance</li>
                </ul>
            </div>

            <h3>Performance Benefits</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Coverage Required</th>
                        <th>Switch Error Rate</th>
                        <th>Key Advantage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Single Individual (15×)</strong></td>
                        <td>15× per individual</td>
                        <td>~1.4%</td>
                        <td>Baseline</td>
                    </tr>
                    <tr>
                        <td><strong>Trio (2×)</strong></td>
                        <td>2× per individual</td>
                        <td>~1.4%</td>
                        <td>Same accuracy, 7.5× less data</td>
                    </tr>
                    <tr>
                        <td><strong>Trio (5×)</strong></td>
                        <td>5× per individual</td>
                        <td>~0.75%</td>
                        <td>Better accuracy, 3× less data</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Section 7: Implementation and Software Engineering -->
        <div id="implementation" class="section">
            <h2>7. Implementation and Software Engineering</h2>

            <div class="highlight">
                <strong>Production Quality:</strong> WhatsHap combines theoretical rigor with practical software engineering, featuring multi-language implementation and standard format support.
            </div>

            <h3>Architecture</h3>

            <div class="algorithm-box">
                <h4>Multi-Language Design</h4>
                <ul class="step-list">
                    <li><strong>Python 3 Frontend:</strong> Command-line interface and workflow management</li>
                    <li><strong>C++ Core:</strong> Performance-critical dynamic programming algorithm</li>
                    <li><strong>Cython Interface:</strong> Seamless Python-C++ integration</li>
                    <li><strong>External Libraries:</strong> PyVCF, pysam, pyfaidx for standard format support</li>
                </ul>
            </div>

            <h3>Usage and Formats</h3>

            <div class="code-block">
# Basic usage
whatshap phase -o phased.vcf input.vcf input.bam

# With pedigree information
whatshap phase -o phased.vcf --ped family.ped input.vcf *.bam

# Multiple technologies
whatshap phase -o phased.vcf input.vcf pacbio.bam illumina.bam 10x.vcf
            </div>

            <div class="info-box">
                <h4>Standard Format Support</h4>
                <ul>
                    <li><strong>Input:</strong> VCF (variants), BAM (reads), PED (pedigree)</li>
                    <li><strong>Output:</strong> VCF with GT and PS tags</li>
                    <li><strong>Compatibility:</strong> GATK ReadBackedPhasing output format</li>
                    <li><strong>Multi-sample:</strong> Automatic sample association via read groups</li>
                </ul>
            </div>
        </div>

        <!-- Section 8: Performance Analysis and Results -->
        <div id="performance" class="section">
            <h2>8. Performance Analysis and Results</h2>

            <div class="highlight">
                <strong>Comprehensive Validation:</strong> WhatsHap demonstrates superior performance across multiple metrics on both simulated and real datasets, establishing it as the gold standard for read-based phasing.
            </div>

            <h3>Experimental Setup</h3>

            <div class="info-box">
                <h4>Validation Datasets</h4>
                <p><strong>Real Data:</strong> GIAB Ashkenazi trio (NA24385) chromosome 1</p>
                <ul>
                    <li>48,023 heterozygous SNVs + 4,265 non-SNVs</li>
                    <li>PacBio reads: 60.2× coverage, 8,687 bp average length</li>
                    <li>Reference: SHAPEIT population phasing</li>
                </ul>

                <p><strong>Simulated Data:</strong> Virtual child with known true haplotypes</p>
                <ul>
                    <li>45,625 heterozygous variants (42,062 SNVs + 3,563 non-SNVs)</li>
                    <li>pbsim-generated reads mimicking real data characteristics</li>
                    <li>Ground truth: Known parental recombination pattern</li>
                </ul>
            </div>

            <h3>Performance Metrics</h3>

            <div class="math-box">
                <h4>Key Evaluation Measures</h4>
                <p><strong>Switch Error Rate:</strong></p>
                $$\text{Switch Error Rate} = \frac{\text{Number of switch errors}}{\text{Total phase connections}}$$

                <p><strong>Phase Connection Ratio:</strong></p>
                $$\text{Phase Connection Ratio} = \frac{\text{Achieved connections}}{\text{Possible connections}}$$

                <p><strong>Theoretical Optimum:</strong> Limited by connectivity graph of reads</p>
            </div>

            <h3>Comparative Results</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Tool</th>
                        <th>Switch Error (Real)</th>
                        <th>Switch Error (Sim)</th>
                        <th>Phase Connections</th>
                        <th>Runtime</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>WhatsHap</strong></td>
                        <td>0.24%</td>
                        <td>0.02%</td>
                        <td>Near optimal</td>
                        <td>Competitive</td>
                    </tr>
                    <tr>
                        <td><strong>WhatsHap (no realign)</strong></td>
                        <td>0.48%</td>
                        <td>0.3%</td>
                        <td>Near optimal</td>
                        <td>Faster</td>
                    </tr>
                    <tr>
                        <td><strong>HapCUT</strong></td>
                        <td>~1.5%</td>
                        <td>~1.0%</td>
                        <td>Good</td>
                        <td>Fast at high coverage</td>
                    </tr>
                    <tr>
                        <td><strong>ReadBackedPhasing</strong></td>
                        <td>~2.0%</td>
                        <td>~1.5%</td>
                        <td>Above theoretical max*</td>
                        <td>Variable</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning-box">
                <h4>Non-SNV Phasing Challenges</h4>
                <p><strong>Additional Error Rate for Non-SNVs:</strong></p>
                <ul>
                    <li><strong>WhatsHap:</strong> 3.8% (simulated), 8.3% (real)</li>
                    <li><strong>HapCUT/phASER:</strong> >20% additional error rate</li>
                </ul>

                <p><strong>Coverage Achievement:</strong></p>
                <ul>
                    <li><strong>WhatsHap:</strong> 97% of non-SNVs phased at full coverage</li>
                    <li><strong>Other tools:</strong> <90% non-SNV phasing</li>
                </ul>
            </div>

            <h3>Key Findings</h3>

            <div class="algorithm-box">
                <h4>Major Insights</h4>
                <ul class="step-list">
                    <li><strong>Re-alignment Impact:</strong> Dramatic improvement dwarfs algorithmic differences</li>
                    <li><strong>Long Read Advantage:</strong> Runtime independent of read length</li>
                    <li><strong>Pedigree Power:</strong> 2× trio coverage matches 15× single individual</li>
                    <li><strong>Production Ready:</strong> Superior accuracy with practical usability</li>
                </ul>
            </div>
        </div>

        <a href="#top" class="back-to-top">↑</a>
    </div>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Initialize MathJax after page load
        window.addEventListener('load', function() {
            if (window.MathJax) {
                MathJax.typesetPromise();
            }
        });
    </script>
</body>
</html>
