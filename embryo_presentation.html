<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Somatic Mutations in Early Human Embryogenesis</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .outline {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .outline h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .outline li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .outline li:last-child {
            border-bottom: none;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        .math-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .algorithm-box {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .algorithm-box h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .performance-metric {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-weight: bold;
        }
        
        .performance-metric.good {
            background: #27ae60;
        }
        
        .performance-metric.medium {
            background: #f39c12;
        }
        
        .nav-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .nav-button {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .hidden {
            display: none;
        }
        
        .embryo-cell {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .mutation {
            fill: #e74c3c;
            stroke: #c0392b;
            stroke-width: 2;
        }
        
        .lineage-line {
            stroke: #34495e;
            stroke-width: 2;
            fill: none;
        }
        
        .asymmetric-line {
            stroke: #e74c3c;
            stroke-width: 3;
            fill: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Somatic Mutations in Early Human Embryogenesis</h1>
            <p>Revealing Asymmetric Cellular Dynamics and Developmental Lineages</p>
        </div>
        
        <div class="outline">
            <h2>📋 Presentation Outline</h2>
            <ul>
                <li><strong>1. Introduction & Study Overview</strong> - Early embryonic mutations and their significance</li>
                <li><strong>2. Mutation Detection Strategy</strong> - Identifying mosaic mutations in adult blood</li>
                <li><strong>3. Experimental Design</strong> - Whole-genome sequencing and validation approaches</li>
                <li><strong>4. Mathematical Framework</strong> - VAF analysis and lineage reconstruction</li>
                <li><strong>5. Asymmetric Cell Dynamics</strong> - 2:1 contribution ratios in early development</li>
                <li><strong>6. Mutation Rate Analysis</strong> - Quantifying early embryonic mutation rates</li>
                <li><strong>7. Biological Mechanisms</strong> - Stochastic vs. deterministic models</li>
                <li><strong>8. Clinical Implications</strong> - Disease predisposition and inheritance patterns</li>
            </ul>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-button" onclick="showSection(1)">Start Presentation</button>
        </div>

        <!-- Section 1: Introduction & Study Overview -->
        <div id="section1" class="section hidden">
            <h2>1. Introduction & Study Overview</h2>

            <div class="highlight">
                <strong>Groundbreaking Discovery:</strong> This study reveals that early human embryonic cells contribute asymmetrically to adult tissues at approximately 2:1 ratios, with ~3 mutations per cell per cell-doubling event during early embryogenesis.
            </div>

            <h3>The Challenge of Early Embryonic Mutations</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Early Embryonic Mutations: From Fertilization to Adult Tissues
                    </text>

                    <!-- Fertilized Egg -->
                    <g transform="translate(50, 80)">
                        <circle cx="40" cy="40" r="30" class="embryo-cell"/>
                        <text x="40" y="45" text-anchor="middle" font-size="12" fill="white">Zygote</text>
                        <text x="40" y="90" text-anchor="middle" font-size="10" fill="#2c3e50">Single cell</text>
                    </g>

                    <!-- First Division -->
                    <g transform="translate(180, 60)">
                        <circle cx="25" cy="25" r="20" class="embryo-cell"/>
                        <circle cx="25" cy="65" r="20" class="embryo-cell"/>
                        <circle cx="25" cy="25" r="5" class="mutation"/>
                        <text x="25" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">2-cell stage</text>
                        <text x="25" y="115" text-anchor="middle" font-size="9" fill="#e74c3c">Mutation occurs</text>
                    </g>

                    <!-- Second Division -->
                    <g transform="translate(280, 50)">
                        <circle cx="15" cy="15" r="15" class="embryo-cell"/>
                        <circle cx="45" cy="15" r="15" class="embryo-cell"/>
                        <circle cx="15" cy="45" r="15" class="embryo-cell"/>
                        <circle cx="45" cy="45" r="15" class="embryo-cell"/>
                        <circle cx="15" cy="15" r="4" class="mutation"/>
                        <circle cx="45" cy="15" r="4" class="mutation"/>
                        <text x="30" y="75" text-anchor="middle" font-size="10" fill="#2c3e50">4-cell stage</text>
                    </g>

                    <!-- Blastocyst -->
                    <g transform="translate(400, 60)">
                        <circle cx="40" cy="40" r="35" fill="none" stroke="#3498db" stroke-width="3"/>
                        <circle cx="40" cy="40" r="15" fill="#f39c12" opacity="0.7"/>
                        <text x="40" y="45" text-anchor="middle" font-size="9" fill="white">ICM</text>
                        <text x="40" y="90" text-anchor="middle" font-size="10" fill="#2c3e50">Blastocyst</text>
                        <text x="40" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">~100 cells</text>
                    </g>

                    <!-- Adult Tissue -->
                    <g transform="translate(550, 60)">
                        <rect width="80" height="60" fill="#27ae60" opacity="0.8" rx="10"/>
                        <text x="40" y="35" text-anchor="middle" font-size="12" fill="white">Adult</text>
                        <text x="40" y="50" text-anchor="middle" font-size="12" fill="white">Blood</text>
                        <text x="40" y="80" text-anchor="middle" font-size="10" fill="#2c3e50">Trillions of cells</text>
                        <text x="40" y="95" text-anchor="middle" font-size="9" fill="#e74c3c">25% carry mutation</text>
                    </g>

                    <!-- Arrows -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                        </marker>
                    </defs>

                    <line x1="110" y1="120" x2="160" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="230" y1="90" x2="260" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="340" y1="90" x2="380" y2="100" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="480" y1="100" x2="530" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <!-- VAF Explanation -->
                    <g transform="translate(100, 200)">
                        <rect width="600" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                            Variant Allele Fraction (VAF) as Developmental Signature
                        </text>

                        <!-- VAF Examples -->
                        <g transform="translate(50, 50)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#2c3e50">Mutation timing determines VAF:</text>

                            <g transform="translate(0, 30)">
                                <rect x="0" y="0" width="100" height="15" fill="#e74c3c" opacity="0.3"/>
                                <rect x="0" y="0" width="50" height="15" fill="#e74c3c"/>
                                <text x="105" y="12" font-size="10" fill="#2c3e50">1st division: ~25% VAF</text>
                            </g>

                            <g transform="translate(0, 50)">
                                <rect x="0" y="0" width="100" height="15" fill="#f39c12" opacity="0.3"/>
                                <rect x="0" y="0" width="25" height="15" fill="#f39c12"/>
                                <text x="105" y="12" font-size="10" fill="#2c3e50">2nd division: ~12.5% VAF</text>
                            </g>

                            <g transform="translate(0, 70)">
                                <rect x="0" y="0" width="100" height="15" fill="#3498db" opacity="0.3"/>
                                <rect x="0" y="0" width="12" height="15" fill="#3498db"/>
                                <text x="105" y="12" font-size="10" fill="#2c3e50">3rd division: ~6.25% VAF</text>
                            </g>
                        </g>

                        <g transform="translate(350, 50)">
                            <text x="0" y="15" font-size="12" font-weight="bold" fill="#2c3e50">Key Insights:</text>
                            <text x="0" y="35" font-size="10" fill="#2c3e50">• VAF reflects developmental timing</text>
                            <text x="0" y="50" font-size="10" fill="#2c3e50">• Lower VAF = later mutation</text>
                            <text x="0" y="65" font-size="10" fill="#2c3e50">• Asymmetric divisions alter expected VAFs</text>
                            <text x="0" y="80" font-size="10" fill="#2c3e50">• Tissue-specific VAF differences</text>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Study Significance</h3>

            <div class="algorithm-box">
                <h4>Why Early Embryonic Mutations Matter</h4>
                <div class="step">
                    <strong>Disease Predisposition:</strong> Mosaic mutations can cause genetic disease syndromes and cancer predisposition
                </div>
                <div class="step">
                    <strong>Inheritance Risk:</strong> High chance of transmission to offspring as de novo germline mutations
                </div>
                <div class="step">
                    <strong>Developmental Insights:</strong> Reveal early human embryonic cell lineages and tissue contributions
                </div>
                <div class="step">
                    <strong>Mutation Processes:</strong> Characterize mutational signatures active during embryogenesis
                </div>
            </div>

            <h3>Study Design Overview</h3>

            <div class="math-box">
                <h4>Experimental Approach</h4>
                <p><strong>Sample Size:</strong> 279 individuals with breast cancer (blood samples)</p>
                <p><strong>Sequencing:</strong> Whole-genome sequencing (mean coverage 32×)</p>
                <p><strong>Target VAF Range:</strong> 10% to 35% (mosaic mutations)</p>
                <p><strong>Validation:</strong> Ultra-high-depth targeted sequencing (median 22,000×)</p>

                <p><strong>Key Innovation:</strong> Phasing mutations to nearby germline polymorphisms</p>
                $$\text{Mosaic Mutation} = \text{Present on only one haplotype in subset of cells}$$

                <p><strong>Final Dataset:</strong> 163 validated early embryonic mutations from 241 individuals</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">Next: Detection Strategy →</button>
            </div>
        </div>

        <!-- Section 2: Mutation Detection Strategy -->
        <div id="section2" class="section hidden">
            <h2>2. Mutation Detection Strategy</h2>

            <div class="highlight">
                <strong>Innovative Approach:</strong> Distinguishing early embryonic mutations from inherited polymorphisms using variant allele fractions and haplotype phasing in adult blood samples.
            </div>

            <h3>VAF-Based Detection Principle</h3>

            <div class="math-box">
                <h4>Theoretical Foundation</h4>
                <p><strong>Inherited Polymorphisms:</strong> Expected VAF ≈ 50% (heterozygous)</p>
                <p><strong>Early Embryonic Mutations:</strong> Expected VAF < 50% (mosaic)</p>

                <p><strong>VAF Calculation:</strong></p>
                $$\text{VAF} = \frac{\text{Number of reads with variant}}{\text{Total number of reads}} \times 100\%$$

                <p><strong>Expected VAF for early mutations:</strong></p>
                $$\text{VAF}_{\text{expected}} = \frac{1}{2^{n+1}} \times 100\%$$

                <p>Where n = number of cell divisions after mutation occurrence</p>

                <p><strong>Detection Range:</strong> 10% ≤ VAF ≤ 35%</p>
            </div>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Haplotype Phasing Strategy for Mutation Validation
                    </text>

                    <!-- Chromosome representation -->
                    <g transform="translate(100, 60)">
                        <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Chromosome Haplotypes in Adult Blood
                        </text>

                        <!-- Maternal haplotype -->
                        <g transform="translate(0, 40)">
                            <line x1="0" y1="20" x2="500" y2="20" stroke="#3498db" stroke-width="8"/>
                            <text x="-20" y="25" font-size="12" fill="#3498db">Mat</text>

                            <!-- SNPs on maternal -->
                            <circle cx="100" cy="20" r="6" fill="#27ae60"/>
                            <text x="100" y="40" text-anchor="middle" font-size="10" fill="#27ae60">A</text>

                            <circle cx="200" cy="20" r="6" fill="#27ae60"/>
                            <text x="200" y="40" text-anchor="middle" font-size="10" fill="#27ae60">G</text>

                            <circle cx="300" cy="20" r="6" fill="#e74c3c"/>
                            <text x="300" y="40" text-anchor="middle" font-size="10" fill="#e74c3c">T</text>
                            <text x="300" y="55" text-anchor="middle" font-size="9" fill="#e74c3c">Mutation</text>

                            <circle cx="400" cy="20" r="6" fill="#27ae60"/>
                            <text x="400" y="40" text-anchor="middle" font-size="10" fill="#27ae60">C</text>
                        </g>

                        <!-- Paternal haplotype -->
                        <g transform="translate(0, 80)">
                            <line x1="0" y1="20" x2="500" y2="20" stroke="#e74c3c" stroke-width="8"/>
                            <text x="-20" y="25" font-size="12" fill="#e74c3c">Pat</text>

                            <!-- SNPs on paternal -->
                            <circle cx="100" cy="20" r="6" fill="#f39c12"/>
                            <text x="100" y="40" text-anchor="middle" font-size="10" fill="#f39c12">T</text>

                            <circle cx="200" cy="20" r="6" fill="#f39c12"/>
                            <text x="200" y="40" text-anchor="middle" font-size="10" fill="#f39c12">C</text>

                            <circle cx="300" cy="20" r="6" fill="#95a5a6"/>
                            <text x="300" y="40" text-anchor="middle" font-size="10" fill="#95a5a6">C</text>
                            <text x="300" y="55" text-anchor="middle" font-size="9" fill="#95a5a6">Reference</text>

                            <circle cx="400" cy="20" r="6" fill="#f39c12"/>
                            <text x="400" y="40" text-anchor="middle" font-size="10" fill="#f39c12">A</text>
                        </g>

                        <!-- Sequencing reads -->
                        <g transform="translate(0, 140)">
                            <text x="250" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                                Sequencing Reads from Blood Sample
                            </text>

                            <!-- Read 1 - maternal with mutation -->
                            <g transform="translate(50, 40)">
                                <rect x="0" y="0" width="200" height="15" fill="#3498db" opacity="0.7"/>
                                <text x="25" y="12" font-size="10" fill="white">A</text>
                                <text x="75" y="12" font-size="10" fill="white">G</text>
                                <text x="125" y="12" font-size="10" fill="white">T</text>
                                <text x="175" y="12" font-size="10" fill="white">C</text>
                                <text x="210" y="12" font-size="10" fill="#2c3e50">Read 1 (25%)</text>
                            </g>

                            <!-- Read 2 - maternal with mutation -->
                            <g transform="translate(50, 60)">
                                <rect x="0" y="0" width="200" height="15" fill="#3498db" opacity="0.7"/>
                                <text x="25" y="12" font-size="10" fill="white">A</text>
                                <text x="75" y="12" font-size="10" fill="white">G</text>
                                <text x="125" y="12" font-size="10" fill="white">T</text>
                                <text x="175" y="12" font-size="10" fill="white">C</text>
                                <text x="210" y="12" font-size="10" fill="#2c3e50">Read 2 (25%)</text>
                            </g>

                            <!-- Read 3 - paternal without mutation -->
                            <g transform="translate(50, 80)">
                                <rect x="0" y="0" width="200" height="15" fill="#e74c3c" opacity="0.7"/>
                                <text x="25" y="12" font-size="10" fill="white">T</text>
                                <text x="75" y="12" font-size="10" fill="white">C</text>
                                <text x="125" y="12" font-size="10" fill="white">C</text>
                                <text x="175" y="12" font-size="10" fill="white">A</text>
                                <text x="210" y="12" font-size="10" fill="#2c3e50">Read 3 (25%)</text>
                            </g>

                            <!-- Read 4 - paternal without mutation -->
                            <g transform="translate(50, 100)">
                                <rect x="0" y="0" width="200" height="15" fill="#e74c3c" opacity="0.7"/>
                                <text x="25" y="12" font-size="10" fill="white">T</text>
                                <text x="75" y="12" font-size="10" fill="white">C</text>
                                <text x="125" y="12" font-size="10" fill="white">C</text>
                                <text x="175" y="12" font-size="10" fill="white">A</text>
                                <text x="210" y="12" font-size="10" fill="#2c3e50">Read 4 (25%)</text>
                            </g>

                            <!-- Analysis -->
                            <g transform="translate(50, 130)">
                                <rect x="0" y="0" width="400" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="5"/>
                                <text x="200" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">
                                    Phasing Analysis
                                </text>
                                <text x="20" y="40" font-size="10" fill="#2c3e50">• Mutation (T) present on maternal haplotype only</text>
                                <text x="20" y="55" font-size="10" fill="#2c3e50">• VAF = 25% (2/4 reads) → Early embryonic origin</text>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>

            <h3>Filtering Strategy</h3>

            <div class="algorithm-box">
                <h4>Multi-Step Validation Process</h4>
                <div class="step">
                    <strong>Initial Screening:</strong> VAF 10-35%, minimum 4 supporting reads
                </div>
                <div class="step">
                    <strong>Haplotype Phasing:</strong> Mutation present on only one haplotype in subset of cells
                </div>
                <div class="step">
                    <strong>Copy Number Check:</strong> Exclude regions with duplications or deletions
                </div>
                <div class="step">
                    <strong>Clonal Hematopoiesis Filter:</strong> Remove samples with >4 mutations or driver genes
                </div>
                <div class="step">
                    <strong>Ultra-deep Validation:</strong> Confirm with 22,000× coverage targeted sequencing
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(1)">← Previous</button>
                <button class="nav-button" onclick="showSection(3)">Next: Experimental Design →</button>
            </div>
        </div>

        <!-- Section 3: Experimental Design -->
        <div id="section3" class="section hidden">
            <h2>3. Experimental Design</h2>

            <div class="highlight">
                <strong>Comprehensive Validation:</strong> Multi-tissue analysis confirms embryonic origin, with mutations present in blood, breast tissue, and lymph nodes at correlated VAFs.
            </div>

            <h3>Sample Characteristics</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Value</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Total Samples</td>
                        <td>279 individuals</td>
                        <td>Breast cancer patients (blood controls)</td>
                    </tr>
                    <tr>
                        <td>Final Analysis</td>
                        <td>241 individuals</td>
                        <td>After removing clonal hematopoiesis</td>
                    </tr>
                    <tr>
                        <td>WGS Coverage</td>
                        <td>32× (mean)</td>
                        <td>Initial mutation detection</td>
                    </tr>
                    <tr>
                        <td>Validation Coverage</td>
                        <td>22,000× (median)</td>
                        <td>Ultra-high-depth confirmation</td>
                    </tr>
                    <tr>
                        <td>Validated Mutations</td>
                        <td>163 mutations</td>
                        <td>High-confidence early embryonic</td>
                    </tr>
                </tbody>
            </table>

            <h3>Multi-Tissue Validation</h3>

            <div class="math-box">
                <h4>Cross-Tissue VAF Correlation</h4>
                <p><strong>Tissue Origins:</strong></p>
                <ul>
                    <li><strong>Blood:</strong> Mesodermal origin</li>
                    <li><strong>Breast tissue:</strong> Ectodermal + Mesodermal origin</li>
                    <li><strong>Lymph nodes:</strong> Mesodermal origin</li>
                </ul>

                <p><strong>VAF Relationship:</strong></p>
                $$\text{VAF}_{\text{tissue}} = \text{VAF}_{\text{blood}} \times \text{tissue-specific factor}$$

                <p><strong>Observation:</strong> VAFs generally lower in breast/lymph node than blood</p>
                <p><strong>Interpretation:</strong> Different tissues develop from slightly different embryonic subpopulations</p>
            </div>

            <h3>Cancer vs. Normal Tissue Analysis</h3>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>

                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Mutation Sharing Between Blood and Breast Cancer
                    </text>

                    <!-- Shared mutations -->
                    <g transform="translate(100, 60)">
                        <rect width="250" height="120" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">Shared Mutations</text>
                        <text x="125" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">n = 37</text>

                        <g transform="translate(20, 60)">
                            <text x="0" y="15" font-size="11" fill="#2c3e50">• Present in both blood and cancer</text>
                            <text x="0" y="30" font-size="11" fill="#2c3e50">• High VAF in cancer (~40%)</text>
                            <text x="0" y="45" font-size="11" fill="#2c3e50">• Early embryonic origin</text>
                        </g>
                    </g>

                    <!-- Non-shared mutations -->
                    <g transform="translate(450, 60)">
                        <rect width="250" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">Non-Shared Mutations</text>
                        <text x="125" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">n = 105</text>

                        <g transform="translate(20, 60)">
                            <text x="0" y="15" font-size="11" fill="#2c3e50">• Present in blood only</text>
                            <text x="0" y="30" font-size="11" fill="#2c3e50">• Low VAF in cancer (~2%)</text>
                            <text x="0" y="45" font-size="11" fill="#2c3e50">• Later embryonic origin</text>
                        </g>
                    </g>

                    <!-- VAF correlation -->
                    <g transform="translate(150, 220)">
                        <rect width="500" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                        <text x="250" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            VAF-Dependent Sharing Pattern
                        </text>

                        <!-- Graph axes -->
                        <line x1="50" y1="120" x2="450" y2="120" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="50" y1="50" x2="50" y2="120" stroke="#2c3e50" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="250" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">VAF in Blood (%)</text>
                        <text x="25" y="85" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90, 25, 85)">Sharing Rate (%)</text>

                        <!-- Data points and trend -->
                        <circle cx="100" cy="110" r="4" fill="#e74c3c"/>
                        <circle cx="150" cy="100" r="4" fill="#f39c12"/>
                        <circle cx="200" cy="85" r="4" fill="#f39c12"/>
                        <circle cx="250" cy="70" r="4" fill="#27ae60"/>
                        <circle cx="300" cy="60" r="4" fill="#27ae60"/>
                        <circle cx="350" cy="55" r="4" fill="#27ae60"/>

                        <!-- Trend line -->
                        <line x1="80" y1="115" x2="370" y2="50" stroke="#3498db" stroke-width="3"/>

                        <!-- Labels -->
                        <text x="80" y="130" font-size="10" fill="#2c3e50">10%</text>
                        <text x="180" y="130" font-size="10" fill="#2c3e50">20%</text>
                        <text x="280" y="130" font-size="10" fill="#2c3e50">30%</text>
                        <text x="380" y="130" font-size="10" fill="#2c3e50">40%</text>

                        <text x="30" y="125" font-size="10" fill="#2c3e50">0</text>
                        <text x="30" y="95" font-size="10" fill="#2c3e50">50</text>
                        <text x="30" y="65" font-size="10" fill="#2c3e50">100</text>
                    </g>
                </svg>
            </div>

            <div class="algorithm-box">
                <h4>Key Experimental Insights</h4>
                <div class="step">
                    <strong>Tissue Specificity:</strong> Lower VAF mutations shared less frequently with cancer
                </div>
                <div class="step">
                    <strong>Developmental Timing:</strong> Earlier mutations (higher VAF) more likely to be shared
                </div>
                <div class="step">
                    <strong>Lineage Tracing:</strong> Blood and breast tissue share common embryonic ancestor
                </div>
                <div class="step">
                    <strong>Single Cell Validation:</strong> Confirmed mosaic pattern in individual leukocytes
                </div>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(2)">← Previous</button>
                <button class="nav-button" onclick="showSection(4)">Next: Mathematical Framework →</button>
            </div>
        </div>

        <!-- Section 4: Mathematical Framework -->
        <div id="section4" class="section hidden">
            <h2>4. Mathematical Framework</h2>

            <div class="highlight">
                <strong>Likelihood-Based Modeling:</strong> Beta-binomial distributions account for sequencing overdispersion while fitting asymmetric lineage models to observed VAF distributions.
            </div>

            <h3>Statistical Model</h3>

            <div class="math-box">
                <h4>Beta-Binomial Distribution</h4>
                <p><strong>Overdispersion Parameter:</strong> θ = 223.9 (95% CI: 201-248)</p>
                <p><strong>Dispersion:</strong> ρ = 1/(1+θ) = 0.004</p>

                <p><strong>Probability Mass Function:</strong></p>
                $$P(m|c,v,θ) = \binom{c}{m} \frac{B(m + vθ, c-m + (1-v)θ)}{B(vθ, (1-v)θ)}$$

                <p>Where:</p>
                <ul>
                    <li>m = number of mutant reads</li>
                    <li>c = total coverage</li>
                    <li>v = expected VAF</li>
                    <li>B = Beta function</li>
                </ul>
            </div>

            <h3>Lineage Model Likelihood</h3>

            <div class="math-box">
                <h4>Mixture Model Approach</h4>
                <p><strong>Total Log-Likelihood:</strong></p>

                $$\mathcal{L} = \sum_{j=1}^{N} \log\left(\sum_{b=1}^{B} r_b \cdot s_b \cdot P(m_j, c_j, v_b, θ)\right)$$

                <p>Where:</p>
                <ul>
                    <li>N = 163 (total mutations)</li>
                    <li>B = number of branches in lineage tree</li>
                    <li>r_b = relative mutation rate of branch b</li>
                    <li>s_b = sensitivity to detect mutations from branch b</li>
                    <li>v_b = expected VAF for mutations from branch b</li>
                </ul>

                <p><strong>Sensitivity Function:</strong></p>
                $$s_b = P(\text{detection}|v_b) \times P(\text{phasing}|v_b)$$
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(3)">← Previous</button>
                <button class="nav-button" onclick="showSection(5)">Next: Asymmetric Dynamics →</button>
            </div>
        </div>

        <!-- Section 5: Asymmetric Cell Dynamics -->
        <div id="section5" class="section hidden">
            <h2>5. Asymmetric Cell Dynamics</h2>

            <div class="highlight">
                <strong>Major Discovery:</strong> Early human embryonic cells contribute asymmetrically to adult blood at approximately 2:1 ratios, contradicting symmetric division models.
            </div>

            <h3>Asymmetry Parameters</h3>

            <div class="math-box">
                <h4>Optimal Asymmetry Model</h4>
                <p><strong>First Division:</strong> α₁ ≈ 0.61 (61% vs 39% contribution)</p>
                <p><strong>Second Division:</strong> α₂ ≈ 0.65 (65% vs 35% contribution)</p>

                <p><strong>Expected VAF:</strong></p>
                $$\text{VAF}_{\text{branch}} = 0.5 \times \text{contribution fraction}$$

                <p><strong>Likelihood Ratio Test:</strong></p>
                $$\text{LR} = -2(\mathcal{L}_0 - \mathcal{L}_1) = -2(-1444.4 - (-1366.3)) = 156.2$$
                $$P < 10^{-40} \text{ (highly significant)}$$
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(4)">← Previous</button>
                <button class="nav-button" onclick="showSection(6)">Next: Mutation Rates →</button>
            </div>
        </div>

        <!-- Section 6: Mutation Rate Analysis -->
        <div id="section6" class="section hidden">
            <h2>6. Mutation Rate Analysis</h2>

            <div class="highlight">
                <strong>Quantitative Results:</strong> Approximately 2.8 substitution mutations per cell per cell-doubling event in early human embryogenesis, validated across multiple approaches.
            </div>

            <h3>Mutation Rate Estimates</h3>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Rate (per cell per doubling)</th>
                        <th>95% CI</th>
                        <th>Sample</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Asymmetric Model</td>
                        <td><span class="performance-metric good">2.8</span></td>
                        <td>2.4-3.3</td>
                        <td>241 individuals</td>
                    </tr>
                    <tr>
                        <td>Symmetric Model</td>
                        <td><span class="performance-metric good">2.8</span></td>
                        <td>2.4-3.3</td>
                        <td>241 individuals</td>
                    </tr>
                    <tr>
                        <td>Family Study</td>
                        <td><span class="performance-metric good">2.8</span></td>
                        <td>1.1-5.8</td>
                        <td>3 families, 7 mutations</td>
                    </tr>
                </tbody>
            </table>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(5)">← Previous</button>
                <button class="nav-button" onclick="showSection(7)">Next: Biological Mechanisms →</button>
            </div>
        </div>

        <!-- Section 7: Biological Mechanisms -->
        <div id="section7" class="section hidden">
            <h2>7. Biological Mechanisms</h2>

            <div class="highlight">
                <strong>Two Competing Models:</strong> Asymmetric contributions may arise from intrinsic cellular differences or stochastic bottlenecks during inner cell mass formation.
            </div>

            <h3>Stochastic Bottleneck Model</h3>

            <div class="math-box">
                <h4>ICM Formation Model</h4>
                <p><strong>Hypergeometric Distribution:</strong></p>

                $$P(k; l, m, n) = \frac{\binom{m}{k}\binom{n-m}{l-k}}{\binom{n}{l}}$$

                <p>Where:</p>
                <ul>
                    <li>n = total blastocyst cells (~64)</li>
                    <li>l = ICM founder cells (~11)</li>
                    <li>m = cells carrying mutation</li>
                    <li>k = mutant cells selected for ICM</li>
                </ul>

                <p><strong>Optimal Parameters:</strong> l = 11 ICM cells from n = 64 total cells</p>
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(6)">← Previous</button>
                <button class="nav-button" onclick="showSection(8)">Next: Clinical Implications →</button>
            </div>
        </div>

        <!-- Section 8: Clinical Implications -->
        <div id="section8" class="section hidden">
            <h2>8. Clinical Implications</h2>

            <div class="highlight">
                <strong>Clinical Significance:</strong> Early embryonic mutations provide insights into disease predisposition, inheritance patterns, and developmental biology with implications for precision medicine.
            </div>

            <h3>Disease Implications</h3>

            <div class="algorithm-box">
                <h4>Clinical Applications</h4>
                <div class="step">
                    <strong>Genetic Disease:</strong> Mosaic mutations can cause variable disease severity
                </div>
                <div class="step">
                    <strong>Cancer Predisposition:</strong> Early mutations may predispose to cancer development
                </div>
                <div class="step">
                    <strong>Inheritance Risk:</strong> High transmission probability to offspring
                </div>
                <div class="step">
                    <strong>Developmental Disorders:</strong> Asymmetric development may contribute to birth defects
                </div>
            </div>

            <h3>Future Directions</h3>

            <div class="math-box">
                <h4>Methodological Advances</h4>
                <p><strong>Lineage Reconstruction:</strong> Similar to mouse studies, human cell lineage trees can be reconstructed</p>
                <p><strong>Multi-tissue Analysis:</strong> Comprehensive tissue sampling for complete lineage maps</p>
                <p><strong>Single-cell Sequencing:</strong> Higher resolution analysis of mosaic patterns</p>

                <p><strong>Mutation Rate Formula:</strong></p>
                $$R = \frac{N \times S}{S \times \sum_{b=1}^{B} s_b}$$

                <p>Where R = mutation rate, N = observed mutations, S = sample size</p>
            </div>

            <div class="highlight">
                <strong>Transformative Impact:</strong> This study establishes the foundation for understanding human embryonic development through somatic mutation analysis, opening new avenues for developmental biology and clinical genetics.
            </div>

            <div class="nav-buttons">
                <button class="nav-button" onclick="showSection(7)">← Previous</button>
                <button class="nav-button" onclick="showSection(1)">Return to Start</button>
            </div>
        </div>

        <script>
            function showSection(sectionNum) {
                // Hide all sections
                for (let i = 1; i <= 8; i++) {
                    const section = document.getElementById('section' + i);
                    if (section) {
                        section.classList.add('hidden');
                    }
                }
                
                // Show the requested section
                const targetSection = document.getElementById('section' + sectionNum);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
            
            // Initialize MathJax after page load
            window.addEventListener('load', function() {
                if (window.MathJax) {
                    MathJax.typesetPromise();
                }
            });
        </script>
    </div>
</body>
</html>
